package com.bmh.project.user.controller;


import cn.hutool.core.bean.BeanUtil;
import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.common.security.model.LoginUser;
import com.bmh.common.security.service.TokenService;
import com.bmh.project.common.vo.KeyValueVo;
import com.bmh.project.supervisor.model.SysSupervisor;
import com.bmh.project.supervisor.service.SysSupervisorService;
import com.bmh.project.user.model.SysUser;
import com.bmh.project.user.service.SysUserService;
import com.bmh.project.user.vo.SysUserDictVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


/**
 * 用户管理
 */
@RestController
@RequestMapping("/user")
public class SysUserController {


    @Resource
    private SysUserService userService;

    @Resource
    private TokenService tokenService;

    @Resource
    private SysSupervisorService sysSupervisorService;

    /**
     * 获取机构用户下拉列表数据
     * @param orgId 机构ID
     * @return
     */
    @GetMapping("/getDictList")
    public Result<List<SysUserDictVo>> getDictList(Integer orgId){
        Example example = new Example (SysUser.class);
        example.excludeProperties ("password").orderBy ("id");
        example.createCriteria ().andEqualTo ("orgId", SecurityUtil.getUserType ()==1?SecurityUtil.getOrgId ():orgId);
        List<SysUser> userList = userService.selectByExample (example);
        return ResultUtil.success (BeanUtil.copyToList (userList, SysUserDictVo.class));
    }

    /**
     * 音乐集体课程获取辅助老师列表
     * @return
     */
    @GetMapping("/mcc/getAssistTeacherList")
    public Result<List<SysUserDictVo>> getAssistTeacherList(){
        List<SysUserDictVo> assistTeacherList = userService.getAssistTeacherList ();
        return ResultUtil.success (assistTeacherList);
    }

    /**
     * 获取本机构用户列表
     * @param param 参数实体
     * @param pageNum 页码
     * @param pageSize 条数
     * @return
     */
    @RequestMapping("/getUserList")
    public Result<PageInfo<SysUser>> getUserList(SysUser param, Integer pageNum, Integer pageSize){
        PageHelper.startPage (pageNum,pageSize);
        List<SysUser> userList = userService.getUserList (param);
        return ResultUtil.success (new PageInfo<SysUser> (userList));
    }


    /**
     * 保存用户信息
     *
     * @param user 用户信息
     * @return
     */
    @RequestMapping ("/save")
    public Result<?> save (@RequestBody SysUser user) {
        if (Objects.isNull (user.getId ())) {
            if (!userService.checkUserNameUnique (user.getUsername ())) {
                return ResultUtil.error ("新增用户'" + user.getUsername () + "'失败，登录账号已存在");
            }
            //String psw = RandomUtil.randomNumbers (8);
            user.setPassword (SecurityUtil.encryptPassword (user.getPassword()));
            user.setCreateTime (new Date ());
            user.setType (2);
            user.setStatus(1);
            userService.insert (user);

            user.setUserNo ("YCXYS" + String.format ("%06d", user.getId ()));
            userService.updateNotNull (user);
        } else {
            user.setPassword (null);
            userService.updateNotNull (user);
        }
        return ResultUtil.success ();
    }

    /**
     * 重置密码
     *
     * @param userId      用户ID
     * @param newPassword 新密码
     * @return
     */
    @RequestMapping ("/resetPwd")
    public Result<?> resetPwd (Integer userId, String newPassword) {
        SysUser user = new SysUser ();
        user.setId (userId);
        user.setPassword (SecurityUtil.encryptPassword (newPassword));
        userService.updateNotNull (user);
        return ResultUtil.success ();
    }

    /**
     * 修改密码
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return
     */
    @RequestMapping ("/editPsw")
    public Result<?> resetPsw (String oldPassword, String newPassword) {
        SysUser user = userService.selectByPrimaryKey (SecurityUtil.getUserId ());
        boolean pswCheck = SecurityUtil.matchesPassword (oldPassword, user.getPassword ());
        if (! pswCheck) {
            return ResultUtil.error ("旧密码不正确");
        }
        user.setPassword (SecurityUtil.encryptPassword (newPassword));
        userService.updateNotNull (user);
        return ResultUtil.success ();
    }

    /**
     * 修改用户状态
     * @param userId 用户ID
     * @param status 要修改的状态
     * @return
     */
    @RequestMapping("/changeStatus")
    public Result<?> changeStatus(Integer userId, Integer status){
        SysUser user = new SysUser ();
        user.setId (userId);
        user.setStatus (status);
        userService.updateNotNull (user);
        return ResultUtil.success ();
    }

    /**
     * 获取本机构医生列表
     * @return
     */
    @GetMapping("/getDoctorList")
    public Result<?> getDoctorList(){
        SysUser param = new SysUser();
        param.setStatus(1);
        param.setOrgId(SecurityUtil.getOrgId());
        //param.setType(2);
        List<SysUser> userList = userService.getUserList (param);
        List<KeyValueVo> list = new ArrayList<>();
        for (SysUser sysUser : userList) {
            KeyValueVo vo = new KeyValueVo();
            vo.setValue(sysUser.getId());
            vo.setText(sysUser.getName());
            list.add(vo);
        }
        return ResultUtil.success (list);
    }

    /**
     * 查询用户信息
     * @return SysUser
     */
    @GetMapping("/info")
    public Result<?> getUserInfo(){
        if (SecurityUtil.getUserType ()==1) {
            SysUser sysUser = userService.selectByPrimaryKey(SecurityUtil.getUserId());
            LoginUser loginUser = SecurityUtil.getLoginUser ();
            loginUser.setUser(sysUser);
            tokenService.setLoginUser (loginUser);
            return ResultUtil.success (sysUser);
        }
        SysSupervisor sysSupervisor = sysSupervisorService.selectByPrimaryKey(SecurityUtil.getUserId());
        LoginUser loginUser = SecurityUtil.getLoginUser ();
        loginUser.setSupervisor(sysSupervisor);
        tokenService.setLoginUser (loginUser);
        return ResultUtil.success (sysSupervisor);
    }
}
