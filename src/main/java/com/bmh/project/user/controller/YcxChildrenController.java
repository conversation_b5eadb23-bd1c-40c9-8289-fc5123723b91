package com.bmh.project.user.controller;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.constant.Constants;
import com.bmh.common.redis.RedisService;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.service.YcxChildrenBookService;
import com.bmh.project.saas.service.SaasService;
import com.bmh.project.user.dto.EndCourseDto;
import com.bmh.project.user.dto.YcxChildrenSurveyDto;
import com.bmh.project.user.dto.YcxChildrenTeacherDto;
import com.bmh.project.user.model.YcxChildren;
import com.bmh.project.user.model.YcxChildrenSurvey;
import com.bmh.project.user.query.*;
import com.bmh.project.user.service.*;
import com.bmh.project.user.vo.*;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.util.*;


/**
 * 孩子患者接口
 */
@RestController
@RequestMapping ("/children")
public class YcxChildrenController {


    @Resource
    private YcxChildrenService childrenService;
    @Resource
    private YcxChildrenBookService bookService;
    @Resource
    private YcxChildrenSurveyService surveyService;
    @Resource
    private YcxChildrenLeaveRecordService leaveRecordService;
    @Resource
    private SaasService saasService;
    @Resource
    private RedisService redisService;
    @Resource
    private YcxChildrenTeacherService childrenTeacherService;
    @Resource
    private YcxChildrenConfigService ycxChildrenConfigService;

    /**
     * 获取机构用户下拉列表数据
     * @param orgId 机构ID
     * @return
     */
    @GetMapping("/getDictList")
    public Result<List<YcxChildren>> getDictList(Integer orgId){
        Example example = new Example (YcxChildren.class);
        example.selectProperties ("id", "name", "linkMobile", "erpPatientId");
        example.createCriteria ().andEqualTo ("orgId", SecurityUtil.getUserType ()==1?SecurityUtil.getOrgId ():orgId);
        List<YcxChildren> childrenList = childrenService.selectByExample (example);
        return ResultUtil.success (childrenList);
    }

    /**
     * 获取儿童信息
     *
     * @param childrenId
     * @return
     */
    @RequestMapping ("/getChildren")
    public Result<YcxChildren> getChildren (Integer childrenId) {
        YcxChildren children = childrenService.selectByPrimaryKey(childrenId);
        //管理员时不脱敏手机号，老师进行手机号脱敏
        if (ObjectUtil.isNotNull(SecurityUtil.getLoginUser())
            && ObjectUtil.isNotNull(SecurityUtil.getLoginUser().getUser())
            && ObjectUtil.isNotNull(SecurityUtil.getLoginUser().getUser().getType())
            && (1 != SecurityUtil.getLoginUser().getUser().getType())) {
            children.setLinkMobile(ObjectUtil.isNull(children.getLinkMobile()) ? "" : DesensitizedUtil.mobilePhone(children.getLinkMobile()));
        }
        return ResultUtil.success(children);
    }

    /**
     * 获取历史孩子
     *
     * @param name     姓名
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping ("getChildList")
    public Result getChildList (Integer id, String name, Integer orgId, Integer pageNum, Integer pageSize) {

        Map<String, Object> param = new HashMap<> ();
        if (StringUtil.isNotEmpty (name)) {
            param.put ("name", name);
        }
        param.put ("orgId", SecurityUtil.getUserType () == 1 ? SecurityUtil.getOrgId () : orgId);
        if (id != null && id > 0) {
            param.put ("id", id);
        }
        //个案权限归属
        Map<String, List<Integer>> dataMap = childrenTeacherService.getChildsByUserId(SecurityUtil.getUserId(), "1");
        //个案权限归属
        param.put("childIds",dataMap.get("childIds"));

        if (pageNum != null && pageSize != null) {
            PageHelper.startPage (pageNum, pageSize);
        }
        List<YcxChildren> list = childrenService.getChildList (param);
        //管理员时不脱敏手机号，老师进行手机号脱敏
        if (ObjectUtil.isNotNull(SecurityUtil.getLoginUser())
            && ObjectUtil.isNotNull(SecurityUtil.getLoginUser().getUser())
            && ObjectUtil.isNotNull(SecurityUtil.getLoginUser().getUser().getType())
            && (1 != SecurityUtil.getLoginUser().getUser().getType())) {
            list.forEach(item->{
                    item.setLinkMobile(ObjectUtil.isNull(item.getLinkMobile())?"": DesensitizedUtil.mobilePhone(item.getLinkMobile()));
                });
        }
        PageInfo page = new PageInfo (list);
        return ResultUtil.success (page);
    }

    /**
     * 根据手机号获取儿童信息
     *
     * @param phone
     * @return
     */
    @RequestMapping ("getChildrenByPhone")
    public Result getChildrenByPhone (String phone) {
        List<YcxChildren> list = childrenService.getChildrenByPhone (phone);
        for (YcxChildren children : list) {
            children.setValue (children.getId ());
            children.setText (children.getName ());
        }
        return ResultUtil.success (list);
    }

    @RequestMapping ("getChildrenInfo")
    public Result getChildrenInfo (Integer childrenId) {
        YcxChildren children = childrenService.selectByPrimaryKey (childrenId);
        return ResultUtil.success (children);
    }

    /**
     * 更新儿童孕周数
     *
     * @param childrenId
     * @param pregnantWeek
     * @return
     */
    @PostMapping ("updatePregnantWeek")
    public Result updatePregnantWeek (Integer childrenId, Integer bookId, Integer pregnantWeek) {
        Integer i = childrenService.updateChildrenPregnantWeek (childrenId, pregnantWeek);
        bookService.updatePregnantWeek (bookId, pregnantWeek);
        return ResultUtil.success (i);
    }

    /**
     * 保存儿童信息
     *
     * @param children
     * @return
     */
    @RequestMapping ("/saveChild")
    public Result<?> saveChild (YcxChildren children, String linkMobileMsgCode) {
        Object cache = redisService.get (Constants.CODE_PREFIX + children.getLinkMobile ());
        String cacheCode = Objects.nonNull (cache) ? cache.toString () : "";
        //当前已经有孩子id，判断库中手机号和传入的是否一致，一致就不判断验证码，
        if (ObjectUtil.isNotNull(children) && ObjectUtil.isNotNull(children.getId())){
            YcxChildren ycxChildren = childrenService.selectByPrimaryKey(children.getId());
            if (ObjectUtil.isNotNull(ycxChildren.getLinkMobile()) && ObjectUtil.isNotNull(children.getLinkMobile()) && ycxChildren.getLinkMobile().equals(children.getLinkMobile())) {
               //判断库中手机号和传入的是否一致，一致就不判断验证码
            } else {
                //手机号有变化时，判断验证码
                if (StrUtil.isEmpty (cacheCode)) {
                    return ResultUtil.error ("验证码已失效");
                }
                if (! cacheCode.equals (linkMobileMsgCode)) {
                    return ResultUtil.error ("验证码错误");
                }
            }
        } else {
            //新增加孩子时，判断验证码
            if (StrUtil.isEmpty (cacheCode)) {
                return ResultUtil.error ("验证码已失效");
            }
            if (! cacheCode.equals (linkMobileMsgCode)) {
                return ResultUtil.error ("验证码错误");
            }
        }
        Integer childrenId = null;
        if (SecurityUtil.getLoginUser ().getIsErp () == 1) {
            childrenId = saasService.saveChild (children, 1);
        } else {
            childrenId = childrenService.saveChild (children);

        }
        return ResultUtil.success (childrenId);
    }

    /**
     * ABA 更新儿童状态
     *
     * @param childrenId 儿童ID
     * @param type       类型(1是否请假 2是否结课)
     * @param status     状态(0否 1是)
     * @return
     */
    @RequestMapping ("/saveStatus")
    public Result<?> saveStatus (Integer childrenId, Integer type, Integer status) {
        if (Objects.isNull (childrenId)) {
            return ResultUtil.error ("个案ID不能为空");
        }
        if (childrenService.getProgressCourseCount (childrenId) > 0) {
            return ResultUtil.error ("个案存在进行中课程，不能请假或结课");
        }
        YcxChildren children = new YcxChildren ();
        children.setId (childrenId);
        if (Objects.nonNull (type) && type == 1) {
            children.setIsLeave (status);
        }
        //TODO 去掉结课逻辑
//        if (Objects.nonNull (type) && type == 2) {
//            children.setIsCourseCompleted (status);
//        }
        children.setUpdateTime (new Date ());
        children.setUpdateUser (SecurityUtil.getNickName ());
        childrenService.updateNotNull (children);

        if (Objects.nonNull (type) && type == 1) {
            leaveRecordService.addRecord (childrenId, status);
        }
        return ResultUtil.success ();
    }

    /**
     * ABA模块使用的儿童列表
     *
     * @param query 查询条件
     * @return
     */
    @RequestMapping ("/aba/getList")
    public Result<PageInfo<YcxAbaChildVo>> getAbaChildList (YcxAbaChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        List<YcxAbaChildVo> childList = childrenService.getAbaChildList (query);
        return ResultUtil.success (new PageInfo<> (childList));
    }

    /**
     * 获取ABA备课儿童列表
     *
     * @param query 查询参数
     * @return
     */
    @RequestMapping ("/aba/prep/getList")
    public Result<PageInfo<YcxAbaPrepChildVo>> getAbaPrepChildList (YcxAbaPrepChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }

        List<YcxAbaPrepChildVo> abaPrepChildList = new ArrayList<> ();

        if (SecurityUtil.getLoginUser ().getIsErp () == 1) {
            abaPrepChildList = saasService.getAbaPrepChildList (query);
        } else {
            abaPrepChildList = childrenService.getAbaPrepChildList (query);
        }

        return ResultUtil.success (new PageInfo<> (abaPrepChildList));
    }

    /**
     * 影子老师模块使用的儿童列表
     * 年级类型(1 小班 2 中班 3 大班) 根据最新的有效的孩子一日流程获得
     * @param query 查询条件
     * @return
     */
    @RequestMapping ("/ea/getList")
    public Result<PageInfo<YcxEaChildVo>>  getEaChildList(YcxEaChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        List<YcxEaChildVo> childList = childrenService.getEaChildList (query);
        return ResultUtil.success (new PageInfo<> (childList));
    }

    /**
     * 保存儿童信息问卷
     *
     * @param survey
     * @return
     */
    @RequestMapping ("/saveSurvey")
    public Result<?> saveSurvey (YcxChildrenSurvey survey) {
        survey.setSource (1);
        survey.setCreateTime (new Date ());
        surveyService.insert (survey);
        return ResultUtil.success ();
    }

    /**
     * 获取儿童信息问卷
     *
     * @param childrenId
     * @return
     */
    @RequestMapping ("/getSurvey")
    public Result<YcxChildrenSurvey> getSurvey (Integer childrenId) {
        List<YcxChildrenSurvey> surveyList = surveyService.selectByParentId (YcxChildrenSurvey.class, "childrenId", childrenId);
        return ResultUtil.success (CollectionUtil.isNotEmpty (surveyList) ? surveyList.get (surveyList.size () - 1) : null);
    }


    /**
     * 保存儿童信息问卷-家长端
     *
     * @param survey
     * @return
     */
    @RequestMapping ("/open/saveSurvey")
    public Result<?> openSaveSurvey (YcxChildrenSurveyDto survey) {

        Object cache = redisService.get (Constants.CODE_PREFIX + survey.getLinkMobile ());
        String cacheCode = Objects.nonNull (cache) ? cache.toString () : "";
        if (StrUtil.isEmpty (cacheCode)) {
            return ResultUtil.error ("验证码已失效");
        }
        if (! cacheCode.equals (survey.getLinkMobileMsgCode ())) {
            return ResultUtil.error ("验证码错误");
        }

        Integer childrenId = surveyService.saveOpenSurvey (survey);
        return ResultUtil.success (childrenId);
    }

    /**
     * 音乐集体课模块使用的儿童列表
     *
     * @param query 查询条件
     * @return
     */
    @RequestMapping ("/mcc/getList")
    public Result<PageInfo<YcxMccChildVo>> getAbaChildList (YcxMccChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        List<YcxMccChildVo> childList = childrenService.getMccChildList (query);
        return ResultUtil.success (new PageInfo<> (childList));
    }

    /**
     * 言语训练模块获取儿童列表
     *
     * @param query
     * @return
     */
    @GetMapping ("/st/getList")
    public Result<PageInfo<YcxStChildVo>> getStList (YcxStChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        List<YcxStChildVo> stChildList = childrenService.getStChildList (query);
        return ResultUtil.success (new PageInfo<> (stChildList));
    }

    /**
     * 获取儿童列表
     *
     * @param query
     * @return
     */
    @GetMapping ("/getEvaluatingChildList")
    public Result<PageInfo<YcxEvaluatingChildrenVo>> getEvaluatingChildList (YcxEvaluatingChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        List<YcxEvaluatingChildrenVo> childList = childrenService.getEvaluatingChildList (query);
        return ResultUtil.success (new PageInfo<> (childList));
    }

    /**
     * 获取个案归属时儿童列表
     *
     * @param query 查询条件
     * @return
     */
    @GetMapping ("/teacher/getList")
    public Result<PageInfo<YcxTeacherChildVo>> getTeacherChildList (YcxTeacherChildQuery query) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        //当前用户的机构id
        query.setOrgId(SecurityUtil.getOrgId ());
        List<YcxTeacherChildVo> childList = childrenService.getTeacherChildList (query);
        return ResultUtil.success (new PageInfo<> (childList));
    }

    /**
     * 保存个案归属的老师
     *
     * @param teacherDto
     * @return
     */
    @RequestMapping ("/teacher/save")
    public Result<?> teacherSave (YcxChildrenTeacherDto teacherDto) {
        if (ObjectUtil.isNull(teacherDto)) {
            return ResultUtil.error("参数不能为空");
        }
        if (ObjectUtil.isEmpty(teacherDto.getChildId())) {
            return ResultUtil.error("孩子ID参数不能为空");
        }
        childrenTeacherService.teacherSave(teacherDto);
        return ResultUtil.success();
    }

    /**
     * 获取个案临时归属儿童列表；已经归属的儿童不再选择
     *
     * @param query 查询条件
     * @return
     */
    @GetMapping ("/teacher/getAttendList")
    public Result<PageInfo<YcxTeacherChildVo>> getAttendList (YcxTeacherChildQuery query) {
        if (ObjectUtil.isNull(query) || ObjectUtil.isNull(query.getCourseType())){
            return ResultUtil.error ("参数不能为空, 课程类型(1综合能力 2言语训练 4融合教育)");
        }
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        //当前用户的机构id
        query.setOrgId(SecurityUtil.getOrgId ());
        query.setUserId(SecurityUtil.getUserId());
        List<YcxTeacherChildVo> childList = childrenService.getAttendList (query);
        return ResultUtil.success (new PageInfo<> (childList));
    }

    /**
     * 获取儿童课程类型
     * @param childId 儿童ID
     * @return 类型列表
     */
    @GetMapping("/getCourseList")
    public Result<?> getCourseList(Integer childId){
        List<CourseTypeVo> courseTypeVoList = childrenService.getCourseList(childId);
        return ResultUtil.success(courseTypeVoList);
    }

    /**
     * 儿童结课接口
     * @param endCourseDto 结课参数
     * @return 操作提示
     */
    @PostMapping("/endCourse")
    public Result<?> endCourse(@RequestBody EndCourseDto endCourseDto){
        return ycxChildrenConfigService.endCourse(endCourseDto);
    }

    /**
     * 获取儿童最后一个计划（包含无效）
     * @param childId 儿童id
     * @param type 1 aba 2 st 3 ea
     * @return id
     */
    @GetMapping("/getLastPlanId")
    public Result<?> getLastPlanId(Integer childId,Integer type){
        Integer planId = childrenService.getLastPlanId(childId,type);
        return ResultUtil.success(planId);
    }

}
