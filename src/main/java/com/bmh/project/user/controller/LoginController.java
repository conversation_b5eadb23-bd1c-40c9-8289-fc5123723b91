package com.bmh.project.user.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.constant.Constants;
import com.bmh.common.security.model.LoginBody;
import com.bmh.common.security.model.LoginUser;
import com.bmh.common.security.service.LoginService;
import com.bmh.common.security.service.TokenService;
import com.bmh.project.supervisor.service.SysSupervisorScopeService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 登录接口
 */
@RestController
@RequestMapping
public class LoginController {


    @Resource
    private LoginService loginService;
    @Resource
    private TokenService tokenService;
    @Resource
    private SysSupervisorScopeService supervisorScopeService;


    /**
     * 登录
     *
     * @param loginBody
     * @return
     */
    @RequestMapping ("/login")
    public Result login (LoginBody loginBody) {
        LoginUser loginUser = loginService.login (loginBody);
        String token = tokenService.createJwtToken (loginUser);
        Map<String, Object> resultMap = new HashMap<> ();

        resultMap.put (Constants.TOKEN, token);
        resultMap.put ("userType", loginUser.getUserType ());
        resultMap.put ("isErp", loginUser.getIsErp ());
        resultMap.put ("orgIsRecharge", loginUser.getOrgIsRecharge());
        resultMap.put ("isParent", loginUser.getIsParent());
        resultMap.put ("isBelong", loginUser.getIsBelong());
        resultMap.put ("isCourseSign", loginUser.getIsCourseSign());

        if (loginUser.getUserType () == 2) {
            resultMap.put ("user", loginUser.getSupervisor ());
            // 如果是督导用户登录，返回该用户的督导范围
            resultMap.put ("supervisorScope", supervisorScopeService.getListBySupervisorId (loginUser.getUserId ()));
        } else {
            resultMap.put ("user", loginUser.getUser ());
        }
        return ResultUtil.success (resultMap);
    }


}
