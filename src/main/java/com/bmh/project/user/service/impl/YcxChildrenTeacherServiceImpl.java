package com.bmh.project.user.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.user.dto.YcxChildrenTeacherDto;
import com.bmh.project.user.mapper.SysOrgMapper;
import com.bmh.project.user.mapper.YcxChildrenTeacherMapper;
import com.bmh.project.user.model.SysOrg;
import com.bmh.project.user.model.YcxChildrenTeacher;
import com.bmh.project.user.service.YcxChildrenTeacherService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * YcxChildrenTeacherService对应的实现类
 *
 * <AUTHOR>
 * @date 2025年06月18日 15:36:53
 */
@Service
public class YcxChildrenTeacherServiceImpl extends BaseServiceImpl<YcxChildrenTeacher> implements YcxChildrenTeacherService {
    @Resource
    private YcxChildrenTeacherMapper ycxChildrenTeacherMapper;
    @Resource
    private SysOrgMapper sysOrgMapper;
    /**
     * 保存个案归属的老师
     * @param teacherDto
     * @return
     */
    @Override
    public int teacherSave(YcxChildrenTeacherDto teacherDto) {
        Integer childId = teacherDto.getChildId();
        Integer orgId = SecurityUtil.getOrgId();
        //没有传入课程类型时，全量去掉非临时任务;
        if (ObjectUtil.isNotNull(teacherDto) && ObjectUtil.isEmpty(teacherDto.getCourseType())) {
            YcxChildrenTeacher childrenTeacher = new YcxChildrenTeacher();
            childrenTeacher.setStatus(0);
            childrenTeacher.setUpdateTime(new Date());
            childrenTeacher.setUpdateUser(SecurityUtil.getNickName());
            Example example = new Example(YcxChildrenTeacher.class);
            //1 正常归属 ; 2 临时任务加入
            //仅处理 attendType 1 正常归属
            //临时任务的数据，过期后查询还需要，不处理
            example.createCriteria().andEqualTo("orgId", orgId).andEqualTo("childId", childId).andEqualTo("attendType", 1);
            ycxChildrenTeacherMapper.updateByExampleSelective(childrenTeacher, example);
        }
        //批量插入
        List<YcxChildrenTeacher> childrenTeacherList = new ArrayList<>();
        teacherDto.getTeacherIds().forEach(item -> {
            YcxChildrenTeacher ycxChildrenTeacher = new YcxChildrenTeacher();
            ycxChildrenTeacher.setChildId(childId);
            ycxChildrenTeacher.setOrgId(orgId);
            ycxChildrenTeacher.setUserId(item);
            ycxChildrenTeacher.setStatus(1);
            ycxChildrenTeacher.setCreateTime(new Date());
            ycxChildrenTeacher.setCreateUser(SecurityUtil.getNickName());
            ycxChildrenTeacher.setAttendType(1);
            ycxChildrenTeacher.setExpireDate(DateUtil.parseDate("2099-12-31"));
            ycxChildrenTeacher.setCourseType("1,2,4,");
            //分情况插入
            if (ObjectUtil.isNotNull(teacherDto) && ObjectUtil.isNotEmpty(teacherDto.getCourseType())) {
                ycxChildrenTeacher.setAttendType(2);
                ycxChildrenTeacher.setExpireDate(new Date());
                ycxChildrenTeacher.setCourseType(teacherDto.getCourseType().toString()+',');
            }
            childrenTeacherList.add(ycxChildrenTeacher);
        });
        if (CollectionUtil.isNotEmpty(childrenTeacherList)) {
            return ycxChildrenTeacherMapper.insertList(childrenTeacherList);
        }
        return 1;
    }

    /**
     * 通过老师id获得相应的可以操作的孩子idsList
     * @param userId
     * @param courseType 课程类型(1综合能力 2言语训练 4融合教育)
     * @return
     */
    @Override
    public Map<String,List<Integer>> getChildsByUserId(Integer userId, String courseType) {
        Map<String,List<Integer>> dataMap = new HashMap<>();
        //开启了个案权限归属，把老师可以管理的孩子ids传入查询条件;督导和管理员不判断权限
        List<Integer> childIds = new ArrayList<>();
        //临时上课孩子Ids
        List<Integer> tempChildIds = new ArrayList<>();
        //老师+开启权限归属的才判断，管理员不判断
        if (SecurityUtil.getUserType () == 1) {
            //实时查询机构个案归属开启状态，不需重新登录获取权限
            if (ObjectUtil.isNotNull(SecurityUtil.getOrgId())){
              SysOrg sysOrg = sysOrgMapper.selectByPrimaryKey(SecurityUtil.getOrgId());
              if (ObjectUtil.isNotNull(sysOrg)) {
                    if (sysOrg.getIsBelong() == 1) {
                        //管理员时才出来，user.type = 1 管理员
                        if (ObjectUtil.isNotNull(SecurityUtil.getLoginUser())
                            && ObjectUtil.isNotNull(SecurityUtil.getLoginUser().getUser())
                            && ObjectUtil.isNotNull(SecurityUtil.getLoginUser().getUser().getType())
                            && (1 != SecurityUtil.getLoginUser().getUser().getType())) {
                            List<YcxChildrenTeacher> childrenTeacherList = ycxChildrenTeacherMapper.getChildsByUserId(userId, courseType);
                            if (CollectionUtil.isNotEmpty(childrenTeacherList)) {
                                childIds.addAll(childrenTeacherList.stream().map(YcxChildrenTeacher::getChildId).distinct().collect(Collectors.toList()));
                                tempChildIds.addAll(childrenTeacherList.stream().filter(item -> item.getAttendType() == 2).map(YcxChildrenTeacher::getChildId).distinct().collect(Collectors.toList()));
                            } else {
                                //开启了个案权限归属，但是没设置权限时，查询不到任何孩子信息
                                childIds.add(null);
                            }
                        }
                    }
                }
            }
        }
        dataMap.put("childIds",childIds);
        dataMap.put("tempChildIds",tempChildIds);
       return dataMap;

    }
}
