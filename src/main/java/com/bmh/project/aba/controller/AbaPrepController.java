package com.bmh.project.aba.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.aba.service.AbaPrepService;
import com.bmh.project.aba.vo.AbaPrepDetailVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Objects;

/**
 * ABA-备课接口
 *
 * <AUTHOR>
 * @since 2023/9/21 14:35
 */
@RestController
@RequestMapping ("/aba/prep")
public class AbaPrepController {

    @Resource
    private AbaPrepService prepService;

    /**
     * 保存备课记录
     *
     * @param planProjectId 计划项目ID
     * @param prepId 备课ID
     * @return prepId 备课ID
     */
    @RequestMapping ("/saveRecord")
    public Result<Integer> saveRecord (@NotNull Integer planProjectId, @NotNull Integer prepId) {
        if(Objects.isNull (planProjectId) || Objects.isNull (prepId)){
            return ResultUtil.error ("参数错误，请联系管理员");
        }
        prepService.saveRecord (planProjectId, prepId);
        return ResultUtil.success (prepId);
    }

    /**
     * 获取备课ID
     * @param childId  儿童ID
     * @return prepId 备课ID
     */
    @GetMapping ("/getPrepId")
    public Result<Integer> generatorPrep (Integer childId){
        Integer prepId = prepService.getPrepId (childId, SecurityUtil.getUserId ());
        return ResultUtil.success (prepId);
    }

    /**
     * 批量获取备课记录详情
     * @param prepIds 备课记录IDS(,分割)
     * @return
     */
    @RequestMapping("/getPrepDetailList")
    public Result<List<AbaPrepDetailVo>> getPrepDetailList(String prepIds){
        List<AbaPrepDetailVo> prepDetailList = prepService.getPrepDetailList (prepIds);
        return ResultUtil.success (prepDetailList);
    }
}
