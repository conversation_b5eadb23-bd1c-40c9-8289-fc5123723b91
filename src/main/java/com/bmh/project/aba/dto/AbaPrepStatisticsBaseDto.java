package com.bmh.project.aba.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class AbaPrepStatisticsBaseDto implements Serializable {

    /**
     * 机构ID
     */
    private Integer orgId;

    /**
     * 儿童ID
     */
    private Integer childId;

    /**
     * 老师ID
     */
    private Integer teacherId;

    /**
     * 老师姓名
     */
    private String teacherName;

    /**
     * 计划ID
     */
    private Integer planId;

    /**
     * 备课ID
     */
    private Integer prepId;

    /**
     * 备课日期
     */
    private Date prepDate;

    /**
     * 备课率
     */
    private Double prepRate;

    /**
     * 课程ID
     */
    private Integer courseId;

    private static final long serialVersionUID = 1L;
}