package com.bmh.project.dashboard.Service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.bmh.project.aba.mapper.*;
import com.bmh.project.aba.model.AbaDomain;
import com.bmh.project.aba.model.AbaPlanProjectGoal;
import com.bmh.project.ai.mapper.AbaPlanDeepSeekLogMapper;
import com.bmh.project.ai.model.AbaPlanDeepSeekLog;
import com.bmh.project.analysis.mapper.AnalysisDashboardMapper;
import com.bmh.project.analysis.query.AnalysisBaseQuery;
import com.bmh.project.dashboard.Service.DashBoardService;
import com.bmh.project.dashboard.util.ShortIdEncryptor;
import com.bmh.project.dashboard.util.Sign;
import com.bmh.project.dashboard.vo.*;
import com.bmh.project.evaluation.vb.mapper.YcxVbResultMapper;
import com.bmh.project.evaluation.vb.model.YcxVbResult;
import com.bmh.project.st.mapper.StCourseActivityMapper;
import com.bmh.project.st.mapper.StCourseActivityTargetMapper;
import com.bmh.project.st.mapper.StCourseRecordMapper;
import com.bmh.project.supervisor.mapper.SysSupervisorMapper;
import com.bmh.project.user.mapper.YcxChildrenMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class DashBoardServiceImpl implements DashBoardService {

    @Resource
    private AnalysisDashboardMapper analysisDashboardMapper;

    @Resource
    private AbaCourseProjectMapper abaCourseProjectMapper;

    @Resource
    private AbaCourseRecordMapper abaCourseRecordMapper;

    @Resource
    private AbaCourseMapper abaCourseMapper;

    @Resource
    private AbaPlanProjectGoalMapper abaPlanProjectGoalMapper;

    @Resource
    private StCourseActivityMapper stCourseActivityMapper;

    @Resource
    private StCourseRecordMapper stCourseRecordMapper;

    @Resource
    private StCourseActivityTargetMapper stCourseActivityTargetMapper;

    @Resource
    private AbaDomainMapper abaDomainMapper;

    @Resource
    private SysSupervisorMapper sysSupervisorMapper;

    @Resource
    private AbaPlanDeepSeekLogMapper abaPlanDeepSeekLogMapper;

    @Resource
    private YcxVbResultMapper ycxVbResultMapper;

    @Resource(name = "taskExecutor")
    private Executor taskExecutor;

    @Resource
    private YcxChildrenMapper ycxChildrenMapper;

    /**
     * 根据code 查询机构今日概况
     *
     * @param code 机密字符串
     * @return 今日概况数据
     */
    @Override
    public TodayOverviewVo todayOverview(String code) {
        int orgId = ShortIdEncryptor.decryptId(code);
        if (-1 == orgId) {
            orgId = 0;
        }
        ZoneId zone = ZoneId.systemDefault();
        LocalDate today = LocalDate.now(zone);
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.plusDays(1).atStartOfDay();

        AnalysisBaseQuery analysisBaseQuery = new AnalysisBaseQuery();
        analysisBaseQuery.setPrecondition(1);
        analysisBaseQuery.setOrgId(orgId);
        //完成课节数
        Integer courseCount = analysisDashboardMapper.getDashBoardCourseCount(analysisBaseQuery);
        //完成项目数
        int projectCount = abaCourseProjectMapper.getProjectCount(orgId, startOfDay, endOfDay);
        int activityCount = stCourseActivityMapper.getActivityCount(orgId, startOfDay, endOfDay);
        //完成目标数
        int shortGoalCount = abaCourseProjectMapper.getShortGoalCount(orgId, startOfDay, endOfDay);
        int targetCount = stCourseActivityTargetMapper.getTargetCount(orgId, startOfDay, endOfDay);
        //完成回合数
        int abaRecordCount = abaCourseRecordMapper.getRecordCount(orgId, startOfDay, endOfDay);
        int stRecordCount = stCourseRecordMapper.getRecordCount(orgId, startOfDay, endOfDay);
        TodayOverviewVo todayOverviewVo = new TodayOverviewVo();
        todayOverviewVo.setCompleteClass(courseCount);
        todayOverviewVo.setCompleteProject(projectCount + activityCount);
        todayOverviewVo.setCompleteGoal(shortGoalCount + targetCount);
        todayOverviewVo.setCompleteRound(abaRecordCount + stRecordCount);
        return todayOverviewVo;
    }

    /**
     * 根据code 查询数据概览
     *
     * @param code 加密字符串
     * @return 总数居
     */
    @Override
    public DataOverviewVo DataOverview(String code) {
        int orgId = ShortIdEncryptor.decryptId(code);
        if (-1 == orgId) {
            orgId = 0;
        }
        AnalysisBaseQuery analysisBaseQuery = new AnalysisBaseQuery();
        analysisBaseQuery.setPrecondition(0);
        analysisBaseQuery.setOrgId(orgId);// 推荐：放入类变量，避免频繁创建销毁
        //课节数
        int courseCount = abaCourseMapper.getCourseCount(orgId);
        //老师数
        //Integer teacherCount = sysUserMapper.selectByOrgId(orgId);
        Integer teacherCount = analysisDashboardMapper.getDashBoardTeacherCount(analysisBaseQuery);
        //个案数
        List<String> childIds = analysisDashboardMapper.getDashBoardChildIds(analysisBaseQuery);
        List<Integer> childIdList = childIds.stream().flatMap(s -> Arrays.stream(s.split(",")))
            .map(Integer::parseInt)
            .distinct()
            .collect(Collectors.toList());
        Integer childCount = childIdList.size();

        //督导次数
        Map<String, BigDecimal> map = analysisDashboardMapper.getDashBoardSupervisionData(analysisBaseQuery);
        int supervisionProjectCount = map.getOrDefault("supervisionProjectCount", BigDecimal.ZERO).intValue();
        //已应用教案次数
        int shortGoalCount = abaPlanProjectGoalMapper.selectShortGoalCount(orgId);
        int targetCount = stCourseActivityTargetMapper.selectTargetCount(orgId);
        DataOverviewVo dataOverviewVo = new DataOverviewVo();
        dataOverviewVo.setClassCount(courseCount);
        dataOverviewVo.setTeacherCount(teacherCount);
        dataOverviewVo.setCaseCount(childCount);
        dataOverviewVo.setSupervisorCount(supervisionProjectCount);
        dataOverviewVo.setUseTeachingPlanCount(shortGoalCount + targetCount);
        dataOverviewVo.setTotalTeachingPlanCount(5310);
        return dataOverviewVo;
    }


    /**
     * 教学排行榜,进步排行榜,儿童问题领域分布,督导排行榜课,节量趋势
     *
     * @param code 加密字符串
     * @return 总数居
     */
    @Override
    public RankDataVo getRankData(String code) throws ExecutionException, InterruptedException {
        int orgId = ShortIdEncryptor.decryptId(code);
        if (-1 == orgId) {
            orgId = 0;
        }
        int finalOrgId = orgId;
        CompletableFuture<List<DailyCourseVo>> dailyCourseFuture = CompletableFuture.supplyAsync(() -> this.getDailyCourseList(finalOrgId), taskExecutor);
        CompletableFuture<List<DomainDistributionVo>> domainDistributionFuture = CompletableFuture.supplyAsync(() -> this.getDomainDistributionData(finalOrgId), taskExecutor);
        CompletableFuture<List<ChildRankVo>> childRankFuture = CompletableFuture.supplyAsync(() -> this.getChildRankList(finalOrgId), taskExecutor);
        CompletableFuture<List<SupervisorRankVo>> supervisorRankFuture = CompletableFuture.supplyAsync(() -> this.getSupervisorRankList(finalOrgId), taskExecutor);
        CompletableFuture<List<TeacherRankVo>> teacherRankFuture = CompletableFuture.supplyAsync(() -> this.getTeacherRankList(finalOrgId), taskExecutor);
        CompletableFuture<AiNumTokenVo> aiTokenFuture = CompletableFuture.supplyAsync(() -> {
            try {
                return this.getAiNumToken();
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }, taskExecutor);

        // 等待所有任务完成后聚合结果
        CompletableFuture<Void> allDone = CompletableFuture.allOf(
            dailyCourseFuture,
            domainDistributionFuture,
            childRankFuture,
            supervisorRankFuture,
            teacherRankFuture,
            aiTokenFuture
        );

        // 当所有完成后构建结果对象
        return allDone.thenApply(v -> {
            try {
                RankDataVo rankDataVo = new RankDataVo();
                rankDataVo.setDailyCourseList(dailyCourseFuture.get());
                rankDataVo.setDomainDistributionList(domainDistributionFuture.get());
                rankDataVo.setChildRankList(childRankFuture.get());
                rankDataVo.setSupervisorRankList(supervisorRankFuture.get());
                rankDataVo.setTeacherRankList(teacherRankFuture.get());
                rankDataVo.setAiNumTokenVo(aiTokenFuture.get());
                return rankDataVo;
            } catch (Exception e) {
                log.error(e.getMessage());
                throw new RuntimeException("异步任务失败", e);
            }
        }).get(); // .get() 会阻塞直到所有任务完成
    }


    /**
     * 获取课节数量趋势
     *
     * @param orgId 机构ID
     * @return ClassCountVo
     */
    @Override
    public List<DailyCourseVo> getDailyCourseList(Integer orgId) {

        // 获取统计时间区间：今天往前推 30 天（不含今天）
        DateTime today = DateUtil.beginOfDay(DateUtil.date());
        DateTime endTime = DateUtil.endOfDay(DateUtil.offsetDay(today, -1));
        DateTime startTime = DateUtil.beginOfDay(DateUtil.offsetDay(today, -60));

        // 查询数据库记录
        List<DailyCourseVo> dbList = analysisDashboardMapper.getThirtyDayCourse(orgId, startTime, endTime);

        // 转为 Map：key 为 yyyy-MM-dd，value 为 classCount
        Map<String, Integer> countMap = dbList.stream()
            .collect(Collectors.toMap(
                vo -> DateUtil.formatDate(vo.getClassDate()),
                DailyCourseVo::getClassCount
            ));

        // 构建结果列表
        List<DailyCourseVo> result = new ArrayList<>(60);
        for (int i = 0; i < 60; i++) {
            DateTime date = DateUtil.offsetDay(startTime, i);
            String key = DateUtil.formatDate(date);
            DailyCourseVo vo = new DailyCourseVo();
            vo.setClassDate(date.toJdkDate());
            vo.setClassCount(countMap.getOrDefault(key, 0));
            result.add(vo);
        }
        return result;
    }


    /**
     * 获取儿童问题领域分布
     *
     * @param orgId 机构ID
     * @return ChildrenQuestionDomainVo
     */
    @Override
    public List<DomainDistributionVo> getDomainDistributionData(Integer orgId) {
        List<AbaPlanProjectGoal> abaPlanProjectGoalList;
        if (0 == orgId) {
            Example example = new Example(AbaPlanProjectGoal.class);
            example.selectProperties("domainId");
            example.createCriteria().andNotIn("orgId", Collections.singletonList("1,19,43,65"));
            abaPlanProjectGoalList = abaPlanProjectGoalMapper.selectByExample(example);
        } else {
            Example example = new Example(AbaPlanProjectGoal.class);
            example.selectProperties("domainId");
            example.createCriteria().andEqualTo("orgId", orgId);
            abaPlanProjectGoalList = abaPlanProjectGoalMapper.selectByExample(example);
        }
//        List<AbaPlanProjectGoal> filteredList = abaPlanProjectGoalList.stream()
//            .filter(goal -> goal.getStatus() == 1 || goal.getStatus() == 2)
//            .collect(Collectors.toList());
        //外圈数量
        Map<Integer, Long> outerRingCountMap = abaPlanProjectGoalList.stream()
            .collect(Collectors.groupingBy(
                AbaPlanProjectGoal::getDomainId,
                Collectors.counting()
            ));
        //内圈数量
//        Map<Integer, Long> innerRingCountMap = filteredList.stream().collect(Collectors.groupingBy(AbaPlanProjectGoal::getDomainId, Collectors.counting()));
        //领域map
        Map<Integer, String> domainMap = abaDomainMapper.selectAll().stream().collect(Collectors.toMap(AbaDomain::getId, AbaDomain::getName));
        List<DomainDistributionVo> domainDistributionVoList = new ArrayList<>();
        domainMap.forEach((key, value) -> {
            DomainDistributionVo domainDistributionVo = new DomainDistributionVo();
            domainDistributionVo.setDomainId(key);
            domainDistributionVo.setDomainName(value);
            domainDistributionVo.setOuterRingCount(outerRingCountMap.containsKey(key) ? outerRingCountMap.get(key).intValue() : 0);
            domainDistributionVoList.add(domainDistributionVo);
        });
        return domainDistributionVoList;
    }

    /**
     * 获取进步排行榜
     *
     * @param orgId 机构ID
     * @return ProgressVo
     */
    @Override
    public List<ChildRankVo> getChildRankList(Integer orgId) {
        //获取时间范围
        DateTime endTime = DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.date(), -1));
        DateTime startTime = DateUtil.offsetDay(endTime, -30);
        if (0 == orgId) {
            startTime = DateUtil.offsetDay(endTime, -100);
            List<ChildRankVo> childRankList = Stream.concat(
                    abaPlanProjectGoalMapper.getOrgChildrenCompleteCount(startTime, endTime).stream(),
                    stCourseActivityTargetMapper.getOrgChildrenCompleteCount(startTime, endTime).stream())
                .collect(Collectors.toMap(
                    ChildRankVo::getChildId,
                    Function.identity(),
                    (a, b) -> new ChildRankVo(a.getShortGoalCount() + b.getShortGoalCount(), a.getChildName(), a.getChildId())
                ))
                .values().stream()
                .sorted(Comparator.comparingInt(ChildRankVo::getShortGoalCount).reversed())
                .collect(Collectors.toList());
            List<OrgChildCountVo> orgChildCountList = analysisDashboardMapper.getAllOrgChildCount (startTime, endTime);
            Map<Integer,Integer> orgChildCountMap = orgChildCountList.stream ().collect (Collectors.toMap (OrgChildCountVo::getOrgId, OrgChildCountVo::getChildCount));
            childRankList.forEach(childRankVo -> {
                Integer numerator = childRankVo.getShortGoalCount();
                Integer denominator =  orgChildCountMap.get(childRankVo.getChildId());
                BigDecimal result = BigDecimal.ZERO;
                if (denominator != null && denominator != 0) {
                    result = BigDecimal.valueOf(numerator).divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
                }
                // 获取 double 值
                double ratio = result.doubleValue();
                childRankVo.setShortGoalRate(ratio);
            });
            List<ChildRankVo> result = childRankList.stream()
                .sorted(Comparator.comparingDouble(ChildRankVo::getShortGoalRate).reversed())
                .limit(10)
                .collect(Collectors.toList());
            return result;
        } else {
            List<ChildRankVo> childRankList = Stream.concat(
                    abaPlanProjectGoalMapper.getChildrenCompleteCount(orgId, startTime, endTime).stream(),
                    stCourseActivityTargetMapper.getChildrenCompleteCount(orgId, startTime, endTime).stream())
                .collect(Collectors.toMap(
                    ChildRankVo::getChildId,
                    Function.identity(),
                    (a, b) -> new ChildRankVo(a.getShortGoalCount() + b.getShortGoalCount(), a.getChildName(), a.getChildId())
                ))
                .values().stream()
                .sorted(Comparator.comparingInt(ChildRankVo::getShortGoalCount).reversed())
                .limit(10)
                .collect(Collectors.toList());
            return childRankList;
        }
    }

    /**
     * 获取督导排行榜
     *
     * @param orgId 机构ID
     * @return SupervisorVo
     */
    @Override
    public List<SupervisorRankVo> getSupervisorRankList(Integer orgId) {
        List<Integer> removeSupervisorList = Arrays.asList(
            1, 2, 3, 8, 9, 10, 21
        );
        //获取时间范围
        DateTime endTime = DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.date(), -1));
        DateTime startTime = DateUtil.offsetDay(endTime, -30);
        if (0 == orgId) {
            List<SupervisorRankVo> supervisorRankVoList = Stream.concat(
                    sysSupervisorMapper.getOrgAbaSupervisorCourseCount(startTime, endTime, removeSupervisorList).stream(),
                    sysSupervisorMapper.getOrgStSupervisorCourseCount(startTime, endTime, removeSupervisorList).stream())
                .collect(Collectors.toMap(SupervisorRankVo::getSupervisionId, Function.identity(), (a, b) -> new SupervisorRankVo(a.getSupervisionProjectCount() + b.getSupervisionProjectCount(), a.getSupervisionName(), a.getSupervisionId())))
                .values().stream()
                .sorted(Comparator.comparingInt(SupervisorRankVo::getSupervisionProjectCount))
                .collect(Collectors.toList());
            List<Map<String, Object>> selectOrgSupervisorCount = sysSupervisorMapper.selectOrgSupervisorCount();
            Map<Integer, Integer> orgSupervisorCountMap = selectOrgSupervisorCount.stream()
                .collect(Collectors.toMap(
                    row -> (Integer) row.get("orgId"),
                    row -> ((Number) row.get("count")).intValue()
                ));
            supervisorRankVoList.forEach(supervisorRankVo -> {
                Integer numerator = supervisorRankVo.getSupervisionProjectCount();
                Integer denominator = orgSupervisorCountMap.get(supervisorRankVo.getSupervisionId());
                BigDecimal result = BigDecimal.ZERO;
                if (denominator != null && denominator != 0) {
                    result = BigDecimal.valueOf(numerator)
                        .divide(BigDecimal.valueOf(denominator), 2, RoundingMode.HALF_UP);
                }
                // 获取 double 值
                double ratio = result.doubleValue();
                supervisorRankVo.setSupervisionRankRate(ratio);
            });
            List<SupervisorRankVo> resultList = supervisorRankVoList
                .stream()
                .sorted(Comparator.comparingDouble(SupervisorRankVo::getSupervisionRankRate).reversed())
                .limit(10)
                .collect(Collectors.toList());
            Collections.reverse(resultList);
            return resultList;
        } else {
            List<SupervisorRankVo> supervisorRankList = Stream.concat(
                    sysSupervisorMapper.getAbaSupervisorCourseCount(orgId, startTime, endTime, removeSupervisorList).stream(),
                    sysSupervisorMapper.getStSupervisorCourseCount(orgId, startTime, endTime, removeSupervisorList).stream())
                .collect(Collectors.toMap(
                    SupervisorRankVo::getSupervisionId,
                    Function.identity(),
                    (a, b) -> new SupervisorRankVo(a.getSupervisionProjectCount() + b.getSupervisionProjectCount(), a.getSupervisionName(), a.getSupervisionId())
                ))
                .values().stream()
                .limit(10)
                .sorted(Comparator.comparingInt(SupervisorRankVo::getSupervisionProjectCount))
                .collect(Collectors.toList());
            return supervisorRankList;
        }
    }

    /**
     * 获取教学排行榜
     *
     * @param orgId 机构ID
     * @return TeachVo
     */
    @Override
    public List<TeacherRankVo> getTeacherRankList(Integer orgId) {
        //获取时间范围
        DateTime endTime = DateUtil.endOfDay(DateUtil.offsetDay(DateUtil.date(), -1));
        DateTime startTime = DateUtil.offsetDay(endTime, -30);
        //老师课程数量列表
        if (0 == orgId) {
            List<TeacherRankVo> teacherCourseCount = analysisDashboardMapper.getAllOrgTeacherCourseCount(startTime, endTime);
            List<TeacherRankVo> result = teacherCourseCount.stream().limit(10).collect(Collectors.toList());
            Collections.reverse(result);
            return result;
        }
        List<TeacherRankVo> teacherCourseCount = analysisDashboardMapper.getTeacherCourseCount(orgId, startTime, endTime);
        return teacherCourseCount.stream().sorted(Comparator.comparingInt(TeacherRankVo::getClassCount)).collect(Collectors.toList());
    }

    /**
     * 获取AI各类型数据
     *
     * @return AI数据
     */
    @Override
    public AiNumTokenVo getAiNumToken() throws Exception {
        //获取近30天AI tokens使用数量
        DateTime end = DateUtil.date();
        DateTime start = DateUtil.offsetDay(end, -30);
        String response = Sign.getToken(start.getTime() / 1000, end.getTime() / 1000);
        JSONObject jsonRsp = new JSONObject(response);
        JSONArray usageResults = jsonRsp.getJSONObject("Result").getJSONArray("UsageResults");
        JSONObject promptTokens = usageResults.getJSONObject(0);
        JSONObject completionTokens = usageResults.getJSONObject(1);
        Integer tokenCount = 0;
        for (Object o : promptTokens.getJSONArray("MetricItems").getJSONObject(0).getJSONArray("Values")) {
            JSONObject promptToken = (JSONObject) o;
            tokenCount += promptToken.getInt("Value");
        }
        for (Object o : completionTokens.getJSONArray("MetricItems").getJSONObject(0).getJSONArray("Values")) {
            JSONObject completionToken = (JSONObject) o;
            tokenCount += completionToken.getInt("Value");
        }
        Example abaExample = new Example(AbaPlanDeepSeekLog.class);
        abaExample.createCriteria().andEqualTo("step", 1);
        Integer iepCount = abaPlanDeepSeekLogMapper.selectCountByExample(abaExample);
        Example ycxExample = new Example(YcxVbResult.class);
        ycxExample.createCriteria().andEqualTo("status", 1);
        Integer reportCount = ycxVbResultMapper.selectCountByExample(ycxExample);
        AiNumTokenVo aiNumTokenVo = new AiNumTokenVo();
        aiNumTokenVo.setIepNum(iepCount);
        aiNumTokenVo.setReportNum(reportCount);
        int part6 = tokenCount * 6 / 10;
        int part3 = tokenCount * 3 / 10;
        int part1 = tokenCount - part6 - part3; // 剩余的归最后一份，避免因整除丢失精度
        aiNumTokenVo.setIepToken(part6);
        aiNumTokenVo.setReportToken(part3);
        aiNumTokenVo.setExpertToken(part1);
        return aiNumTokenVo;
    }
}
