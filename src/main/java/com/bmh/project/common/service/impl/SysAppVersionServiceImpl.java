package com.bmh.project.common.service.impl;


import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.common.mapper.SysAppVersionMapper;
import com.bmh.project.common.model.SysAppVersion;
import com.bmh.project.common.service.SysAppVersionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * SysAppVersionService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年05月07日 17:28:04
 */
@Service
public class SysAppVersionServiceImpl extends BaseServiceImpl<SysAppVersion> implements SysAppVersionService {
    @Resource
    private SysAppVersionMapper sysAppVersionMapper;

    /**
     * 检查更新
     * @param appId appID
     * @param versionCode 版本号
     * @return
     */
    @Override
    public SysAppVersion checkUpdate (String appId, Integer versionCode) {
        SysAppVersion maxVersion = sysAppVersionMapper.getMaxVersion (appId);
        return Objects.isNull (maxVersion)?null:(maxVersion.getVersionCode () > versionCode ? maxVersion : null);
    }
}