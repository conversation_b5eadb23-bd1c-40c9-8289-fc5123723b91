package com.bmh.project.evaluation.sta.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.sta.service.YcxStaService;
import com.bmh.project.evaluation.sta.vo.StaOptionVo;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;

/**
 * 言语评估表(YcxSta)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:41
 */
@RestController
@RequestMapping("/sta")
public class YcxStaController {

    /**
     * 服务对象
     */
    @Resource
    private YcxStaService ycxStaService;

    /**
     * 获取st评估问题列表
     * @param type 类型(1 构音器官结构与运动功能，2构音功能)
     * @param childId 儿童id
     * @param recordId 评估id
     * @return StaOptionVo
     */
    @GetMapping("/getList")
    public Result<?> getList(Integer type,Integer childId,Integer recordId){
        StaOptionVo staOptionVo = ycxStaService.getList(type,childId,recordId);
        return ResultUtil.success(staOptionVo);
    }

    /**
     * 全部重置
     * @param recordId 记录id
     * @return 操作提示
     */
    @GetMapping("/reset")
    public Result<?> allReset(Integer recordId){
        ycxStaService.allReset(recordId);
        return ResultUtil.success();
    }
}
