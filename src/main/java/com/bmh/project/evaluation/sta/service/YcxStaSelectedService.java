package com.bmh.project.evaluation.sta.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.sta.model.YcxStaSelected;

import java.util.List;

/**
 * 言语评估选择项表(YcxStaSelected)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
public interface YcxStaSelectedService extends BaseService<YcxStaSelected> {

    List<YcxStaSelected> getSelectedList(Integer recordId);

    /**
     * 增加/修改记录
     * @param staSelected 记录数据
     */
    void upsert(YcxStaSelected staSelected);
}
