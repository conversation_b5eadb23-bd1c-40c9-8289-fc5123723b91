package com.bmh.project.evaluation.sta.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.sta.model.YcxSta;
import com.bmh.project.evaluation.sta.vo.StaOptionVo;

/**
 * 言语评估表(YcxSta)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:42
 */
public interface YcxStaService extends BaseService<YcxSta> {

    /**
     * 获取st评估问题列表
     * @param type 类型(1 构音器官结构与运动功能，2构音功能)
     * @param childId 儿童id
     * @return StaOptionVo
     */
    StaOptionVo getList(Integer type, Integer childId,Integer recordId);

    /**
     * 全部重置
     * @param recordId 记录id
     */
    void allReset(Integer recordId);
}
