package com.bmh.project.evaluation.sta.vo;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bmh.project.evaluation.sta.model.YcxStaOption;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class StaDetailVo {
    /**
     * 题目ID
     */
    private Integer id;
    /**
     * 类型(1 构音器官结构与运动功能，2构音功能)
     */
    private Integer type;
    /**
     * 标签
     */
    private String tag;
    /**
     * 题目详情
     */
    private String content;
    /**
     * 评估老师
     */
    private String staTeacher;
    /**
     * 题目选项
     */
    private Integer score;
    /**
     * 题目选中项
     */
    private Integer choice;
    /**
     * 评估ID
     */
    private Integer recordId;

    /**
     * 最终选项文本
     */
    private String choiceStr;

    /**
     * 选项列表
     */
    private List<YcxStaOption> optionList;

    public String getChoiceStr() {
        if (CollectionUtil.isNotEmpty(this.optionList)&& ObjectUtil.isNotEmpty(this.choice)) {
            StringBuilder stringBuilder = new StringBuilder();
            this.optionList.forEach(option -> {
                if (Objects.equals(option.getScore(), this.choice)){
                    stringBuilder.append("☑ ").append(option.getName()).append("  ");
                }else {
                    stringBuilder.append("☐ ").append(option.getName()).append("  ");
                }
            });
            this.choiceStr = stringBuilder.toString();
        }
        return this.choiceStr;
    }
}
