package com.bmh.project.evaluation.sta.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.sta.model.YcxStaSelected;
import com.bmh.project.evaluation.sta.vo.StaDetailVo;
import com.bmh.project.evaluation.sta.vo.StaSelectedVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 言语评估选择项表(YcxStaSelected)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
public interface YcxStaSelectedMapper extends BaseMapper<YcxStaSelected> {

    List<YcxStaSelected> getSelectedList(@Param("recordId") Integer recordId);

    void allReset(@Param("recordId") Integer recordId,@Param("updateTime") Date updateTime);

    /**
     * 根据报告ID列表查询报告详情
     * @param list recordId
     * @return 报告详情列表
     */
    List<StaDetailVo> selectDetailByRecordIdList(@Param("list") List<String> list);
}
