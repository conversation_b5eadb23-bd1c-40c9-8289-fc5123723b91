package com.bmh.project.evaluation.sta.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.sta.mapper.YcxStaMapper;
import com.bmh.project.evaluation.sta.mapper.YcxStaSelectedMapper;
import com.bmh.project.evaluation.sta.model.YcxSta;
import com.bmh.project.evaluation.sta.model.YcxStaOption;
import com.bmh.project.evaluation.sta.model.YcxStaSelected;
import com.bmh.project.evaluation.sta.service.YcxStaService;
import com.bmh.project.evaluation.sta.vo.StaOptionVo;
import com.bmh.project.evaluation.sta.vo.StaVo;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;
import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 言语评估表(YcxSta)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:42
 */
@Service("ycxStaService")
public class YcxStaServiceImpl extends BaseServiceImpl<YcxSta> implements YcxStaService {

    @Resource
    private YcxStaMapper ycxStaMapper;

    @Resource
    private YcxStaSelectedMapper ycxStaSelectedMapper;

    /**
     * 获取st评估问题列表
     *
     * @param type    类型(1 构音器官结构与运动功能，2构音功能)
     * @param childId 儿童id
     * @return StaOptionVo
     */
    @Override
    public StaOptionVo getList(Integer type, Integer childId, Integer recordId) {
        StaOptionVo staOptionVo = new StaOptionVo();
        List<YcxSta> ycxStaList = ycxStaMapper.selectList(type);
        if (ObjectUtil.isNotEmpty(recordId)) {
            Example staSelectedExample = new Example(YcxStaSelected.class);
            staSelectedExample.createCriteria()
                .andEqualTo("recordId", recordId)
                .andEqualTo("status", 1);
            List<YcxStaSelected> ycxStaSelectedList = ycxStaSelectedMapper.selectByExample(staSelectedExample);
            if (CollectionUtil.isNotEmpty(ycxStaSelectedList)) {
                Map<Integer, YcxStaSelected> groupStaId = ycxStaSelectedList.stream().collect(Collectors.toMap(YcxStaSelected::getStaId, Function.identity()));
                for (YcxSta ycxSta : ycxStaList) {
                    if (groupStaId.containsKey(ycxSta.getId())) {
                        YcxStaSelected ycxStaSelected = groupStaId.get(ycxSta.getId());
                        for (YcxStaOption option : ycxSta.getOptions()) {
                            if (option.getId() == Integer.parseInt(ycxStaSelected.getAnswer())) {
                                ycxSta.setScore(ycxStaSelected.getScore());
                                ycxSta.setSelectedId(ycxStaSelected.getId());
                            }
                        }
                    }
                }
            }
        }
        if (type == 1) {
            LinkedHashMap<String, List<YcxSta>> groupByTag = ycxStaList.stream()
                .collect(Collectors.groupingBy(
                    YcxSta::getTag,
                    LinkedHashMap::new,
                    Collectors.toList()
                ));
            List<StaVo> staVoList = new ArrayList<>();
            groupByTag.forEach((tag, value) -> {
                StaVo staVo = new StaVo();
                staVo.setName(tag);
                staVo.setList(value);
                staVoList.add(staVo);
            });
            staOptionVo.setStaList(staVoList);
        } else {
            staOptionVo.setStaS(ycxStaList);
        }
        return staOptionVo;
    }

    /**
     * 全部重置
     *
     * @param recordId 记录id
     */
    @Override
    public void allReset(Integer recordId) {
        ycxStaSelectedMapper.allReset(recordId, new Date());
    }
}
