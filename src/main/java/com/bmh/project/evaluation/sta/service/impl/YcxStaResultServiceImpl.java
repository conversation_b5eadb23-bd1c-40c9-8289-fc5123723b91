package com.bmh.project.evaluation.sta.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.sta.mapper.YcxStaMapper;
import com.bmh.project.evaluation.sta.mapper.YcxStaResultMapper;
import com.bmh.project.evaluation.sta.model.YcxStaResult;
import com.bmh.project.evaluation.sta.service.YcxStaResultService;
import com.bmh.project.evaluation.sta.vo.StaSelectedVo;
import com.bmh.project.evaluation.sta.vo.StaTagVo;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 言语评估结果表(YcxStaResult)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@Service("44")
public class YcxStaResultServiceImpl extends BaseServiceImpl<YcxStaResult> implements YcxStaResultService, YcxResultService<YcxStaResult> {

    @Resource
    private YcxStaResultMapper ycxStaResultMapper;

    @Resource
    private YcxStaMapper ycxStaMapper;

    /**
     * 保存评估结果
     * @param childrenRecord 参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveResult(YcxChildrenRecord childrenRecord) {
        List<StaSelectedVo> staSelectedVoList = ycxStaResultMapper.selectByRecordId(childrenRecord.getId());
        Map<Integer, List<StaSelectedVo>> groupType = staSelectedVoList.stream().collect(Collectors.groupingBy(StaSelectedVo::getType));
        List<StaSelectedVo> staSelectedVoList1 = groupType.get(1);
        Map<String, List<StaSelectedVo>> groupByTag = staSelectedVoList1.stream().collect(Collectors.groupingBy(StaSelectedVo::getTag));
        JSONObject recordResult = new JSONObject();
        //获取每个标签的总分
        List<StaTagVo> staTagVoList = ycxStaMapper.getTagTotalScore();
        List<StaTagVo> typeOne = new ArrayList<>();
        LinkedHashMap<String,Integer> templateScore = staTagVoList
            .stream()
            .collect(Collectors.toMap(StaTagVo::getTag,StaTagVo::getTotalScore,(oldValue, newValue) -> oldValue,LinkedHashMap::new));
        templateScore.keySet().forEach(tag -> {
            List<StaSelectedVo> list = groupByTag.get(tag);
            int count = 0;
            if (list != null) {
                count = list.stream().mapToInt(StaSelectedVo::getScore).sum();
            }
            StaTagVo staTagVo = new StaTagVo();
            staTagVo.setTag(tag);
            staTagVo.setScore(count);
            staTagVo.setTotalScore(templateScore.get(tag));
            typeOne.add(staTagVo);
        });
        recordResult.put("typeOne",typeOne);
        List<StaSelectedVo> staSelectedVoList2 = groupType.get(2);
        //获取type为2的分数选项
        List<Integer> scoreList = ycxStaMapper.getTwoDisTinctScore();
        JSONObject typeTwo = new JSONObject();
        scoreList.forEach(score -> typeTwo.putOnce("NO"+score,0));
        if (CollectionUtil.isNotEmpty(staSelectedVoList2)) {
            Map<Integer, List<StaSelectedVo>> groupByScore = staSelectedVoList2.stream().collect(Collectors.groupingBy(StaSelectedVo::getScore));
            groupByScore.forEach((score,list)-> typeTwo.set("NO"+score,list.size()));
        }
        recordResult.put("typeTwo",typeTwo);
        YcxStaResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxStaResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setResultChart(recordResult.toString());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        //插入评估结果
        ycxStaResultMapper.insert(result);
    }

    /**
     * 获取评估结果
     * @param recordId 评测记录ID
     * @return 评估结果
     */
    @Override
    public YcxStaResult getResult(Integer recordId) {
        Example example = new Example(YcxStaResult.class);
        example.orderBy("createTime").desc();
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxStaResult> list = this.selectByExample(example);
        return CollectionUtil.isNotEmpty(list) ? list.get(0) : null;
    }

    /**
     * 修改评估
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxStaResult result = new YcxStaResult ();
        result.setRecom (recom);
        Example example = new Example (YcxStaResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxStaResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}
