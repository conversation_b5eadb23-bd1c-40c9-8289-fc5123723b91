package com.bmh.project.evaluation.sta.model;

import lombok.Data;
import lombok.experimental.Accessors;
import javax.persistence.*;
import java.util.Date;
import java.util.List;

/**
 * 言语评估表(YcxSta)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@Data
@Accessors(chain = true)
@Table(name = "ycx_sta")
public class YcxSta  {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
    *类型(1 构音器官结构与运动功能，2构音功能)
    */
    @Column(name = "type")
    private Integer type;

    /**
    *标签
    */
    @Column(name = "tag")
    private String tag;

    /**
    *题目内容
    */
    @Column(name = "content")
    private String content;

    /**
    *顺序
    */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
    *状态(0无效，1有效)
    */
    @Column(name = "status")
    private Integer status;

    /**
     *创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选项
     */
    @Transient
    private List<YcxStaOption> options;

    /**
     * 0 未选中 1 已选中
     */
    @Transient
    private Integer score;

    /**
     * 记录id
     */
    @Transient
    private Integer selectedId;
}
