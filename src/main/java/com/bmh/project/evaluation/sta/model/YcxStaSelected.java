package com.bmh.project.evaluation.sta.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.util.Date;

/**
 * 言语评估选择项表(YcxStaSelected)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@Data
@Accessors(chain = true)
@Table(name = "ycx_sta_selected")
public class YcxStaSelected {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
    *测评记录id
    */
    @Column(name = "record_id")
    private Integer recordId;

    /**
    *测评问题id
    */
    @Column(name = "sta_id")
    private Integer staId;

    /**
    *选中项id
    */
    @Column(name = "answer")
    private String answer;

    /**
    *分数
    */
    @Column(name = "score")
    private Integer score;

    /**
    *备注
    */
    @Column(name = "remark")
    private String remark;

    /**
    *状态(0.有效 1无效)
    */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
