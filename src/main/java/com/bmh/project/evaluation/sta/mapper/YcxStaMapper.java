package com.bmh.project.evaluation.sta.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.sta.model.YcxSta;
import com.bmh.project.evaluation.sta.vo.StaTagVo;

import java.util.List;

/**
 * 言语评估表(YcxSta)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:43
 */
public interface YcxStaMapper extends BaseMapper<YcxSta> {

    /**
     * 获取st评估问题列表
     * @param type 类型(1 构音器官结构与运动功能，2构音功能)
     * @return List<YcxSta>
     */
    List<YcxSta> selectList(Integer type);

    /**
     * 获取每个标签的总分
     * @return List<StaTagVo>
     */
    List<StaTagVo> getTagTotalScore();

    List<Integer> getTwoDisTinctScore();

}
