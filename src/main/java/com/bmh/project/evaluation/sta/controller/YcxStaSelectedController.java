package com.bmh.project.evaluation.sta.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.sta.model.YcxStaSelected;
import com.bmh.project.evaluation.sta.service.YcxStaSelectedService;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;

/**
 * 言语评估选择项表(YcxStaSelected)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@RestController
@RequestMapping("/sta/selected")
public class YcxStaSelectedController {

    /**
     * 服务对象
     */
    @Resource
    private YcxStaSelectedService ycxStaSelectedService;

    /**
     * 增加/修改记录
     * @param staSelected 记录数据
     * @return 记录id
     */
    @RequestMapping ("/upsert")
    public Result<Integer> add (YcxStaSelected staSelected) {
        ycxStaSelectedService.upsert(staSelected);
        return ResultUtil.success (staSelected.getId ());
    }

    /**
     * 获取记录
     * @param recordId 评估id
     * @return 记录列表
     */
    @GetMapping("/getList")
    public Result<?> getList(Integer recordId){
        List<YcxStaSelected> selectedList = ycxStaSelectedService.getSelectedList(recordId);
        return ResultUtil.success (selectedList);
    }
}
