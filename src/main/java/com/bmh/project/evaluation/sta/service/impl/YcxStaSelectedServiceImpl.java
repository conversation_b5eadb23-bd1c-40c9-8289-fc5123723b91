package com.bmh.project.evaluation.sta.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.sta.mapper.YcxStaSelectedMapper;
import com.bmh.project.evaluation.sta.model.YcxStaSelected;
import com.bmh.project.evaluation.sta.service.YcxStaSelectedService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 言语评估选择项表(YcxStaSelected)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@Service("ycxStaSelectedService")
public class YcxStaSelectedServiceImpl extends BaseServiceImpl<YcxStaSelected> implements YcxStaSelectedService {

    @Resource
    private YcxStaSelectedMapper ycxStaSelectedMapper;

    /**
     * 根据评估id获取评估记录
     * @param recordId 评估id
     * @return 记录列表
     */
    @Override
    public List<YcxStaSelected> getSelectedList(Integer recordId) {
        return ycxStaSelectedMapper.getSelectedList(recordId);
    }

    /**
     * 增加/修改记录
     * @param staSelected 记录数据
     */
    @Override
    public void upsert(YcxStaSelected staSelected) {
        if (ObjectUtil.isEmpty(staSelected.getId())){
            staSelected.setStatus (1);
            staSelected.setCreateTime (new Date());
            staSelected.setUpdateTime (new Date ());
            ycxStaSelectedMapper.insert (staSelected);
        }else {
            staSelected.setUpdateTime (new Date ());
            ycxStaSelectedMapper.updateByPrimaryKeySelective(staSelected);
        }
    }
}
