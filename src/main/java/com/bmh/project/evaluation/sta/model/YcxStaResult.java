package com.bmh.project.evaluation.sta.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import javax.persistence.*;
import java.util.Date;

/**
 * 言语评估结果表(YcxStaResult)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@Data
@Accessors(chain = true)
@Table(name = "ycx_sta_result")
@EqualsAndHashCode(callSuper = true)
public class YcxStaResult extends YcxResult {

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
    *记录ID
    */
    @Column(name = "record_id")
    private Integer recordId;

    /**
    *孩子ID
    */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
    *孩子年龄
    */
    @Column(name = "children_age")
    private String childrenAge;

    /**
    *机构ID
    */
    @Column(name = "org_id")
    private Integer orgId;

    /**
    *测评结果
    */
    @Column(name = "result")
    private String result;

    /**
    *建议
    */
    @Column(name = "recom")
    private String recom;

    /**
    *用于展示结果说明的字符串
    */
    @Column(name = "result_his")
    private String resultHis;

    /**
    *用于展示图表的json数据
    */
    @Column(name = "result_chart")
    private String resultChart;

    /**
    *操作人id
    */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
    *操作人新姓名
    */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

}
