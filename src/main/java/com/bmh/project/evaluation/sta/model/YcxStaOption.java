package com.bmh.project.evaluation.sta.model;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;

/**
 * 言语评估项目表(YcxStaOption)表实体类
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
@Data
@Accessors(chain = true)
@Table(name = "ycx_sta_option")
public class YcxStaOption{

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 题目id
     */
    @Column(name = "sta_id")
    private Integer staId;

    /**
    *名称
    */
    @Column(name = "name")
    private String name;

    /**
    *值
    */
    @Column(name = "value")
    private String value;

    /**
    *分数
    */
    @Column(name = "score")
    private Integer score;

}
