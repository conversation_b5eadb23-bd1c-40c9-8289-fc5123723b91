package com.bmh.project.evaluation.sta.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.sta.model.YcxStaResult;
import com.bmh.project.evaluation.sta.vo.StaSelectedVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 言语评估结果表(YcxStaResult)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-30 10:13:44
 */
public interface YcxStaResultMapper extends BaseMapper<YcxStaResult> {

    List<StaSelectedVo> selectByRecordId(@Param("recordId") Integer recordId);
}
