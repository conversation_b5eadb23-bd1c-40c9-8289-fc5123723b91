package com.bmh.project.evaluation.nyad.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import com.bmh.project.evaluation.nyad.model.YcxNyadSelected;

import java.util.List;

/**
 * ycx_nyad_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月12日 18:49:04
 */
public interface YcxNyadSelectedService extends BaseService<YcxNyadSelected> {

    void saveSelected(List<YcxNyadSelected> selecteds, Integer recordId);

    List<YcxNyadSelected> getSelecteds(Integer recordId);
}