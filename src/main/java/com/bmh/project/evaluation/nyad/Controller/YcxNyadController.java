package com.bmh.project.evaluation.nyad.Controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.nyad.model.YcxNyad;
import com.bmh.project.evaluation.nyad.service.YcxNyadService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 8-18岁青少年气质量表
 */
@RestController
@RequestMapping("/nyad")
public class YcxNyadController {

    @Resource
    private YcxNyadService ycxNyadService;

    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("/getNyadList")
    public Result<List<YcxNyad>> getNyadList(){
        List<YcxNyad> list = ycxNyadService.getList();
        return ResultUtil.success(list);
    }
}
