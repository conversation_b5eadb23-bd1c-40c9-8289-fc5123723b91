package com.bmh.project.evaluation.nyad.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.nyad.mapper.YcxNyadResultMapper;
import com.bmh.project.evaluation.nyad.model.YcxNyadResult;
import com.bmh.project.evaluation.nyad.model.YcxNyadSelected;
import com.bmh.project.evaluation.nyad.service.YcxNyadResultService;
import com.bmh.project.evaluation.nyad.service.YcxNyadSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxNyadResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 18:49:04
 */
@Service("32")
public class YcxNyadResultServiceImpl extends BaseServiceImpl<YcxNyadResult> implements YcxNyadResultService, YcxResultService<YcxNyadResult> {
    @Resource
    private YcxNyadResultMapper ycxNyadResultMapper;
    @Resource
    private YcxNyadSelectedService nyadSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxNyadResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxNyadResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());

        Example example = new Example(YcxNyadResult.class);
        example.createCriteria().andEqualTo("recordId", childrenRecord.getId());
        List<YcxNyadResult> list = this.selectByExample(example);
        if (CollectionUtil.isEmpty(list)) {
            ycxNyadResultMapper.insert(result);
        } else {
            ycxNyadResultMapper.updateByExampleSelective(result, example);
        }

        List<YcxNyadSelected> selecteds = result.getSelecteds();
        nyadSelectedService.saveSelected(selecteds, result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxNyadResult getResult(Integer recordId) {
        Example example = new Example(YcxNyadResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxNyadResult> ycxNyadResults = selectByExample(example);
        if (ycxNyadResults != null && ycxNyadResults.size() > 0) {
            YcxNyadResult result = ycxNyadResults.get(0);
            result.setSelecteds(nyadSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxNyadResult result = new YcxNyadResult();
        result.setRecom(recom);
        Example example = new Example(YcxNyadResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId", recordId);
        ycxNyadResultMapper.updateByExampleSelective(result, example);

    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}