package com.bmh.project.evaluation.nyad.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nyad.mapper.YcxNyadSelectedMapper;
import com.bmh.project.evaluation.nyad.model.YcxNyadSelected;
import com.bmh.project.evaluation.nyad.service.YcxNyadSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxNyadSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 18:49:04
 */
@Service
public class YcxNyadSelectedServiceImpl extends BaseServiceImpl<YcxNyadSelected> implements YcxNyadSelectedService {
    @Resource
    private YcxNyadSelectedMapper ycxNyadSelectedMapper;

    @Override
    public void saveSelected(List<YcxNyadSelected> selecteds, Integer recordId) {
        Example example = new Example(YcxNyadSelected.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        ycxNyadSelectedMapper.deleteByExample(example);
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public List<YcxNyadSelected> getSelecteds(Integer recordId) {
        return this.ycxNyadSelectedMapper.getSelecteds(recordId);
    }
}