package com.bmh.project.evaluation.nyad.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nyad.mapper.YcxNyadMapper;
import com.bmh.project.evaluation.nyad.model.YcxNyad;
import com.bmh.project.evaluation.nyad.service.YcxNyadService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxNyadService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 18:49:04
 */
@Service
public class YcxNyadServiceImpl extends BaseServiceImpl<YcxNyad> implements YcxNyadService {
    @Resource
    private YcxNyadMapper ycxNyadMapper;

    /**
     * 获取问题列表
     * @return
     */
    @Override
    public List<YcxNyad> getList() {
        return ycxNyadMapper.getList();
    }
}