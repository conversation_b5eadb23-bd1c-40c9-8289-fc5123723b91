package com.bmh.project.evaluation.csi.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.csi.mapper.YcxCsiSelectedMapper;
import com.bmh.project.evaluation.csi.model.YcxCsiSelected;
import com.bmh.project.evaluation.csi.service.YcxCsiSelectedService;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCsiSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月14日 09:57:18
 */
@Service
public class YcxCsiSelectedServiceImpl extends BaseServiceImpl<YcxCsiSelected> implements YcxCsiSelectedService {
    @Resource
    private YcxCsiSelectedMapper ycxCsiSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxCsiSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxCsiSelectedMapper.insertList(selecteds);
    }

    /**
     * 获取结果项
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public List<YcxCsiSelected> getSelecteds (Integer recordId) {
        Example example = new Example (YcxSnapSelected.class);
        example.createCriteria ().andEqualTo ("recordId",recordId);
        return ycxCsiSelectedMapper.selectByExample (example);
    }
}