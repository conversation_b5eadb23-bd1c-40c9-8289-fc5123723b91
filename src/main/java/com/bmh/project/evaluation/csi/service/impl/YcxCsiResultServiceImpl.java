package com.bmh.project.evaluation.csi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.csi.mapper.YcxCsiResultMapper;
import com.bmh.project.evaluation.csi.model.YcxCsiResult;
import com.bmh.project.evaluation.csi.model.YcxCsiSelected;
import com.bmh.project.evaluation.csi.service.YcxCsiResultService;
import com.bmh.project.evaluation.csi.service.YcxCsiSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCsiResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月14日 09:57:18
 */
@Service("25")
public class YcxCsiResultServiceImpl extends BaseServiceImpl<YcxCsiResult> implements YcxCsiResultService, YcxResultService<YcxCsiResult> {
    @Resource
    private YcxCsiResultMapper ycxCsiResultMapper;
    @Resource
    private YcxCsiSelectedService csiSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxCsiResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxCsiResult.class);
        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge ());
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxCsiResultMapper.insert (result);

        List<YcxCsiSelected> selecteds = result.getSelecteds ();
        csiSelectedService.saveSelecteds (selecteds, result.getRecordId ());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxCsiResult getResult (Integer recordId) {
        Example example = new Example (YcxCsiResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxCsiResult> list = this.selectByExample (example);
        if (CollectionUtil.isNotEmpty (list)) {
            YcxCsiResult result = list.get (0);
            result.setSelecteds (csiSelectedService.getSelecteds (recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxCsiResult result = new YcxCsiResult ();
        result.setRecom (recom);
        Example example = new Example (YcxCsiResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxCsiResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}