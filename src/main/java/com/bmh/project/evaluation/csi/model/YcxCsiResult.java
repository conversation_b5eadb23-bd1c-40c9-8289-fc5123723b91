package com.bmh.project.evaluation.csi.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_csi_result")
public class YcxCsiResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄(周)
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 前庭平衡和双侧分化评测结果
     */
    @Column(name = "res_type1")
    private String resType1;

    /**
     * 脑神经生理抑制困难评测结果
     */
    @Column(name = "res_type2")
    private String resType2;

    /**
     * 触觉防御和反应不足评测结果
     */
    @Column(name = "res_type3")
    private String resType3;

    /**
     * 发育期运动障碍评测结果
     */
    @Column(name = "res_type4")
    private String resType4;

    /**
     * 视觉空间和形态感觉评测结果
     */
    @Column(name = "res_type5")
    private String resType5;

    /**
     * 压力情绪反应评测结果
     */
    @Column(name = "res_type6")
    private String resType6;

    /**
     * 自我形象不良评测结果
     */
    @Column(name = "res_type7")
    private String resType7;

    /**
     * 自我形象不良评测结果
     */
    @Column(name = "res_type8")
    private String resType8;

    /**
     * 基础信息
     */
    @Column(name = "base_info")
    private String baseInfo;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选择项
     */
    @Transient
    private List<YcxCsiSelected> selecteds;

    private static final long serialVersionUID = 1L;
}