package com.bmh.project.evaluation.csi.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.csi.model.YcxCsi;

import java.util.List;

/**
 * ycx_csi表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月14日 09:57:18
 */
public interface YcxCsiService extends BaseService<YcxCsi> {

    /**
     * 根据类型获取问题
     * @param type 类型(1前庭平衡和大脑双侧分化，2脑神经生理抑制困难，3触觉防御过多及反应不足，4发育期运用障碍，5视觉空间、形态，6本体觉(重力不安症)，7情绪反应，8心里承受压力及行为表现)
     * @return
     */
    List<YcxCsi> getCsiListByType(Integer type);
}