package com.bmh.project.evaluation.csi;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.csi.model.YcxCsi;
import com.bmh.project.evaluation.csi.service.YcxCsiDictService;
import com.bmh.project.evaluation.csi.service.YcxCsiService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.util.List;

/**
 * 儿童感觉统合能力测评(3-12岁)
 *
 * <AUTHOR>
 * @since 2021/7/14 9:57
 */
@RestController
@RequestMapping("/csi")
public class YcxCsiController {

    @Resource
    private YcxCsiService csiService;
    @Resource
    private YcxCsiDictService dictService;

    /**
     * 根据类型获取问题
     * @param type 类型(1前庭平衡和大脑双侧分化，2脑神经生理抑制困难，3触觉防御过多及反应不足，4发育期运用障碍，5视觉空间、形态，6本体觉(重力不安症)，7情绪反应，8心里承受压力及行为表现)
     * @return
     */
    @RequestMapping("/getCsiListByType")
    public Result<List<YcxCsi>> getCsiListByType(Integer type){
        return ResultUtil.success (csiService.getCsiListByType (type));
    }

    /**
     * 获取标准分
     * @param type 类型(1前庭平衡和大脑双侧分化，2脑神经生理抑制困难，3触觉防御过多及反应不足，4发育期运用障碍，5视觉空间、形态，6本体觉(重力不安症)，7情绪反应，8心里承受压力及行为表现)
     * @param origScore 原始分
     * @return
     */
    @RequestMapping("/getScoreByDict")
    public Result<String> getScoreByDict(Integer type,String origScore ){
        if(type==null|| StringUtil.isEmpty(origScore)){
            return ResultUtil.error("参数错误");
        }
        String realScore = dictService.getScoreByDict(type, origScore);
        return ResultUtil.success(realScore);
    }
}
