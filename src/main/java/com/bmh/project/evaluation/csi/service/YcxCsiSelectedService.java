package com.bmh.project.evaluation.csi.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.csi.model.YcxCsiSelected;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;

import java.util.List;

/**
 * ycx_csi_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月14日 09:57:18
 */
public interface YcxCsiSelectedService extends BaseService<YcxCsiSelected> {

    /**
     * 保存选择项
     * @param selecteds 选择项
     * @param recordId 记录ID
     */
    void saveSelecteds(List<YcxCsiSelected> selecteds, Integer recordId);

    /**
     * 获取结果项
     * @param recordId 记录ID
     * @return
     */
    List<YcxCsiSelected> getSelecteds(Integer recordId);
}