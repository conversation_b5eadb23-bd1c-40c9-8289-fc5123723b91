package com.bmh.project.evaluation.csi.model;

import com.bmh.project.evaluation.snap.model.YcxSnapOption;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_csi")
public class YcxCsi implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1前庭平衡和大脑双侧分化，2脑神经生理抑制困难，3触觉防御过多及反应不足，4发育期运用障碍，5视觉空间、形态，6本体觉(重力不安症)，7情绪反应，8心里承受压力及行为表现)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态(0无效 1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选项
     */
    @Transient
    private List<YcxCsiOption> options;

    private static final long serialVersionUID = 1L;
}