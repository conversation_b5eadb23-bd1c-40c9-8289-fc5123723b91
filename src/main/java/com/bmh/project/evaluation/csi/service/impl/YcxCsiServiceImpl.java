package com.bmh.project.evaluation.csi.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.csi.mapper.YcxCsiMapper;
import com.bmh.project.evaluation.csi.model.YcxCsi;
import com.bmh.project.evaluation.csi.service.YcxCsiService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxCsiService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月14日 09:57:18
 */
@Service
public class YcxCsiServiceImpl extends BaseServiceImpl<YcxCsi> implements YcxCsiService {
    @Resource
    private YcxCsiMapper ycxCsiMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1前庭平衡和大脑双侧分化，2脑神经生理抑制困难，3触觉防御过多及反应不足，4发育期运用障碍，5视觉空间、形态，6本体觉(重力不安症)，7情绪反应，8心里承受压力及行为表现)
     * @return
     */
    @Override
    public List<YcxCsi> getCsiListByType (Integer type) {
        return ycxCsiMapper.getCsiListByType (type);
    }
}