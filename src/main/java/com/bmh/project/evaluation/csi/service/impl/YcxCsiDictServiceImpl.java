package com.bmh.project.evaluation.csi.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.csi.mapper.YcxCsiDictMapper;
import com.bmh.project.evaluation.csi.model.YcxCsiDict;
import com.bmh.project.evaluation.csi.service.YcxCsiDictService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxCsiDictService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月14日 09:57:18
 */
@Service
public class YcxCsiDictServiceImpl extends BaseServiceImpl<YcxCsiDict> implements YcxCsiDictService {
    @Resource
    private YcxCsiDictMapper ycxCsiDictMapper;

    /**
     * 获取标准分
     *
     * @param type       类型(1前庭平衡和大脑双侧分化，2脑神经生理抑制困难，3触觉防御过多及反应不足，4发育期运用障碍，5视觉空间、形态，6本体觉(重力不安症)，7情绪反应，8心里承受压力及行为表现)
     * @param origScore 原始分
     * @return
     */
    @Override
    public String getScoreByDict (Integer type, String origScore) {
        Example example = new Example(YcxCsiDict.class);
        example.createCriteria().andEqualTo("type", type).andEqualTo("origScore", origScore);
        List<YcxCsiDict> list = ycxCsiDictMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty (list)?list.get(0).getRealScore():null;
    }
}