package com.bmh.project.evaluation.csi.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_csi_dict")
public class YcxCsiDict implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1前庭平衡和双侧分化，2脑神经生理抑制困难，3触觉防御和反应不足，4发育期运动障碍，5视觉空间和形态感觉，6本体觉重力不安症，7压力情绪反应，8自我形象不良)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 原始分数
     */
    @Column(name = "orig_score")
    private String origScore;

    /**
     * 真实分数
     */
    @Column(name = "real_score")
    private String realScore;

    private static final long serialVersionUID = 1L;
}