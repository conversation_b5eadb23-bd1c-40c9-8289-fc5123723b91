package com.bmh.project.evaluation.adosg.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.adosg.mapper.YcxAdosgMapper;
import com.bmh.project.evaluation.adosg.model.YcxAdosg;
import com.bmh.project.evaluation.adosg.service.YcxAdosgService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxAdosgService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:22:46
 */
@Service
public class YcxAdosgServiceImpl extends BaseServiceImpl<YcxAdosg> implements YcxAdosgService {
    @Resource
    private YcxAdosgMapper ycxAdosgMapper;

    @Override
    public List<YcxAdosg> getAdosgListByType(Integer type) {
        return this.ycxAdosgMapper.getAdosgListByType(type);
    }


}