package com.bmh.project.evaluation.adosg.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.adosg.mapper.YcxAdosgSelectedMapper;
import com.bmh.project.evaluation.adosg.model.YcxAdosgSelected;
import com.bmh.project.evaluation.adosg.service.YcxAdosgSelectedService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.chat.model.YcxChatSelected;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * YcxAdosgSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:22:46
 */
@Service
public class YcxAdosgSelectedServiceImpl extends BaseServiceImpl<YcxAdosgSelected> implements YcxAdosgSelectedService {
    @Resource
    private YcxAdosgSelectedMapper ycxAdosgSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxAdosgSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public Map<String, Object> getSelecteds(Integer recordId) {
        Map<String, Object> resultSelected = new HashMap<>();
        List<YcxChatSelected> selecteds1 = this.ycxAdosgSelectedMapper.selectSelectedByType(1,recordId);
        List<YcxChatSelected> selecteds2 = this.ycxAdosgSelectedMapper.selectSelectedByType(2,recordId);
        List<YcxChatSelected> selecteds3 = this.ycxAdosgSelectedMapper.selectSelectedByType(3,recordId);
        List<YcxChatSelected> selecteds4 = this.ycxAdosgSelectedMapper.selectSelectedByType(4,recordId);
        resultSelected.put("selecteds1", selecteds1);
        resultSelected.put("selecteds2", selecteds2);
        resultSelected.put("selecteds3", selecteds3);
        resultSelected.put("selecteds4", selecteds4);
        return resultSelected;

    }
}