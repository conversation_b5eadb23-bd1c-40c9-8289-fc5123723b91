package com.bmh.project.evaluation.adosg.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.adosg.model.YcxAdosg;
import com.bmh.project.evaluation.adosg.service.YcxAdosgService;
import com.bmh.project.evaluation.chat.model.YcxChat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * ADOS-G 孤独症诊断观察量表
 */
@RestController
@RequestMapping("adosg")
public class AdosgController {


    @Autowired
    private YcxAdosgService adosgService;


    /**
     * 根据类型获取问题
     * @param type 类型(1语言和沟通，2社会互动，3领域游戏，4刻板行为和局限兴趣)
     * @return
     */
    @RequestMapping("getAdosgListByType")
    public Result getAdosgListByType(Integer type){
        List<YcxAdosg> list = adosgService.getAdosgListByType(type);
        return ResultUtil.success(list);
    }



}
