package com.bmh.project.evaluation.adosg.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_adosg")
public class YcxAdosg implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1语言和沟通，2社会互动，3领域游戏，4刻板行为和局限兴趣)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 是否核心项目(0否，1是)
     */
    @Column(name = "is_core")
    private Integer isCore;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;


    /**
     * 选项
     */
    @Transient
    private List<YcxAdosgOption> options;

}