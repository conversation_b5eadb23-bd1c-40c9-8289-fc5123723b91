package com.bmh.project.evaluation.adosg.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.adosg.model.YcxAdosgSelected;

import java.util.List;
import java.util.Map;

/**
 * ycx_adosg_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:22:46
 */
public interface YcxAdosgSelectedService extends BaseService<YcxAdosgSelected> {

    void saveSelecteds(List<YcxAdosgSelected> selecteds, Integer recordId);

    Map<String, Object> getSelecteds(Integer recordId);

}