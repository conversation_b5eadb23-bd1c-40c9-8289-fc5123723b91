package com.bmh.project.evaluation.adosg.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.adosg.mapper.YcxAdosgResultMapper;
import com.bmh.project.evaluation.adosg.model.YcxAdosgResult;
import com.bmh.project.evaluation.adosg.service.YcxAdosgResultService;
import com.bmh.project.evaluation.adosg.service.YcxAdosgSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxAdosgResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:22:46
 */
@Service("16")
public class YcxAdosgResultServiceImpl extends BaseServiceImpl<YcxAdosgResult> implements YcxAdosgResultService, YcxResultService<YcxAdosgResult> {
    @Resource
    private YcxAdosgResultMapper ycxAdosgResultMapper;
    @Resource
    private YcxAdosgSelectedService adosgSelectedService;


    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxAdosgResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxAdosgResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        adosgSelectedService.saveSelecteds(result.getSelecteds(), result.getRecordId());
    }

    @Override
    public YcxAdosgResult getResult(Integer recordId) {
        Example example = new Example(YcxAdosgResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxAdosgResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            YcxAdosgResult result = list.get(0);
            result.setResultSelecteds(adosgSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxAdosgResult result = new YcxAdosgResult ();
        result.setRecom (recom);
        Example example = new Example (YcxAdosgResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxAdosgResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}