package com.bmh.project.evaluation.adosg.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_adosg_selected")
public class YcxAdosgSelected implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "record_id")
    private Integer recordId;

    @Column(name = "adosg_id")
    private Integer adosgId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;


    /**
     * 题目内容
     */
    @Transient
    private String content;

    /**
     * 顺序
     */
    @Transient
    private Integer orderNum;

    /**
     * 选项答案
     */
    @Transient
    private String optionName;


}