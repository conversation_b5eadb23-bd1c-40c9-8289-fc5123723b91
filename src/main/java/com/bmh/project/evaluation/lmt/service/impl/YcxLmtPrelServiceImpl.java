package com.bmh.project.evaluation.lmt.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrel;
import com.bmh.project.evaluation.lmt.service.YcxLmtPrelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxLmtPrelService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年08月15日 13:57:08
 */
@Service
public class YcxLmtPrelServiceImpl extends BaseServiceImpl<YcxLmtPrel> implements YcxLmtPrelService {
    @Resource
    private YcxLmtPrelMapper ycxLmtPrelMapper;

    /**
     * 获取问题列表
     *
     * @param domainId 领域ID
     * @param resultId 评估结果ID
     * @return
     */
    @Override
    public List<YcxLmtPrel> getList (Integer domainId, Integer resultId) {
        return ycxLmtPrelMapper.getList (domainId, resultId);
    }
}