package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectTemplate;

import java.util.List;

/**
 * ycx_lmt_project_template表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年11月14日 16:37:39
 */
public interface YcxLmtProjectTemplateService extends BaseService<YcxLmtProjectTemplate> {

    /**
     * 根据阶段获取项目
     * @param level 阶段
     * @return
     */
    List<YcxLmtProjectTemplate> getListByLevel (Integer level);
}