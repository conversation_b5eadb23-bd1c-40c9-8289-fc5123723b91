package com.bmh.project.evaluation.lmt.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_lmt_prel_option")
public class YcxLmtPrelOption implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 问题ID
     */
    @Column(name = "lmt_prel_id")
    private Integer lmtPrelId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}