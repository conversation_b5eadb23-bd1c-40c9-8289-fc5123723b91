package com.bmh.project.evaluation.lmt.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_lmt_project")
public class YcxLmtProject implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 儿童ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 评估记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 领域ID
     */
    @Column(name = "domain_id")
    private Integer domainId;

    /**
     * 领域名称
     */
    @Column(name = "domain_name")
    private String domainName;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private Integer projectId;

    /**
     * 项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * 项目阶段
     */
    @Column(name = "project_level")
    private Integer projectLevel;

    /**
     * 短期目标ID
     */
    @Column(name = "short_goal_id")
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    @Column(name = "short_goal_name")
    private String shortGoalName;

    /**
     * 目标达成数量
     */
    @Column(name = "pass_count")
    private Integer passCount;

    /**
     * 目标未达成数量
     */
    @Column(name = "fail_count")
    private Integer failCount;

    /**
     * 通过率(pass_count/10向下取整)
     */
    @Column(name = "pass_rate")
    private Double passRate;

    /**
     * 是否默认项目(0否 1是)
     */
    @Column(name = "is_default")
    private Integer isDefault;

    /**
     * 状态(0已删除 1正常)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;


    public YcxLmtProject(){
    }
    public YcxLmtProject(Integer recordId){
        this.recordId = recordId;
    }

    private static final long serialVersionUID = 1L;
}