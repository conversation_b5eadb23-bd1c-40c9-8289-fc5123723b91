package com.bmh.project.evaluation.lmt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectMapper;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectRecordMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtProject;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectRecord;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectRecordService;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * YcxLmtProjectRecordService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年08月16日 16:41:48
 */
@Service
public class YcxLmtProjectRecordServiceImpl extends BaseServiceImpl<YcxLmtProjectRecord> implements YcxLmtProjectRecordService {
    @Resource
    private YcxLmtProjectRecordMapper ycxLmtProjectRecordMapper;
    @Resource
    private YcxLmtProjectMapper  lmtProjectMapper;

    /**
     * 计算项目通过数量
     *
     * @param lmtProjectId 项目ID
     */
    @Async
    @Override
    public void calcPassCount (Integer lmtProjectId) {
        Map<String, BigDecimal> statisticsMap = ycxLmtProjectRecordMapper.calcPassCount (lmtProjectId);
        if(CollectionUtil.isEmpty (statisticsMap)){
            statisticsMap = new HashMap<> ();
            statisticsMap.put ("passCount",BigDecimal.ZERO);
            statisticsMap.put ("failCount",BigDecimal.ZERO);
        }
        YcxLmtProject lmtProject = new YcxLmtProject ();
        lmtProject.setId(lmtProjectId);
        lmtProject.setPassCount(Objects.nonNull (statisticsMap.get ("passCount"))?statisticsMap.get ("passCount").intValue ():0);
        lmtProject.setFailCount(Objects.nonNull (statisticsMap.get ("failCount"))?statisticsMap.get ("failCount").intValue ():0);
        BigDecimal passRate = NumberUtil.div (String.valueOf (lmtProject.getPassCount ()), 5 + "", 2, RoundingMode.DOWN);
        lmtProject.setPassRate(passRate.doubleValue ());
        lmtProjectMapper.updateByPrimaryKeySelective ( lmtProject);
    }

    /**
     * 根据项目ID删除项目记录
     *
     * @param lmtProjectId 项目ID
     */
    @Override
    public void delByLmtProjectId (Integer lmtProjectId) {
        YcxLmtProjectRecord record = new YcxLmtProjectRecord ();
        record.setStatus (0);
        record.setRemark ("随同项目一同删除记录");
        record.setUpdateTime (new Date ());
        record.setUpdateUser (SecurityUtil.getNickName ());
        Example example = new Example (YcxLmtProjectRecord.class);
        example.createCriteria ().andEqualTo ("lmtProjectId", lmtProjectId);
        ycxLmtProjectRecordMapper.updateByExample (record, example);
    }
}