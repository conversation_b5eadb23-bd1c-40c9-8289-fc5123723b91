package com.bmh.project.evaluation.lmt.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class YcxLmtProjectVo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 领域名称
     */
    private String domainName;
    /**
     * 江苏对应领域名称
     */
    private String jsDomainName;

    /**
     * 项目ID
     */
    private Integer projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目阶段
     */
    private Integer projectLevel;

    /**
     * 短期目标ID
     */
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    private String shortGoalName;

    /**
     * 目标达成数量
     */
    private Integer passCount;

    /**
     * 目标未达成数量
     */
    private Integer failCount;

    /**
     * 通过率(pass_count/10向下取整)
     */
    private Double passRate;

    /**
     *
     */
    private String passRatePercent;

    /**
     * 是否默认项目(0否 1是)
     */
    private Integer isDefault;

    /**
     * 记录
     */
    private List<YcxLmtProjectRecordVo> recordList;

    public String getPassRatePercent() {
        return String.valueOf(this.passRate * 100)+"%";
    }

    public String getJsDomainName() {
        switch (this.domainName) {
            case "先备技能":
                this.jsDomainName = "自理";
                return "自理";
            case "注意力":
                this.jsDomainName = "认知1";
                return "认知1";
            case "沟通":
                this.jsDomainName = "语言";
                return "语言";
            case "社交/游戏":
                this.jsDomainName = "情感表达和社交互动";
                return "情感表达和社交互动";
            case "认知":
                this.jsDomainName = "认知2";
                return "认知2";
            case "运动":
                this.jsDomainName = "大肌肉 小肌肉";
                return "大肌肉 小肌肉";
            case "情绪":
                this.jsDomainName = "模仿";
                return "模仿";
            default:
                return "";
        }
    }

    private static final long serialVersionUID = 1L;
}
