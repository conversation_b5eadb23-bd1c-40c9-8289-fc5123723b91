package com.bmh.project.evaluation.lmt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.evaluation.lmt.dto.YcxLmtPrelResultDto;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelResultMapper;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelSelectedMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelResult;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelSelected;
import com.bmh.project.evaluation.lmt.service.YcxLmtPrelResultService;
import com.bmh.project.evaluation.lmt.vo.YcxLmtPrelDomainResultVo;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * YcxLmtPrelResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年08月15日 13:57:08
 */
@Service
public class YcxLmtPrelResultServiceImpl extends BaseServiceImpl<YcxLmtPrelResult> implements YcxLmtPrelResultService {
    @Resource
    private YcxLmtPrelResultMapper ycxLmtPrelResultMapper;
    @Resource
    private YcxLmtPrelSelectedMapper selectedMapper;

    /**
     * 保存初评表结果
     *
     * @param dto
     * @return
     */
    @Override
    @Transactional (rollbackFor = Exception.class)
    public YcxLmtPrelResult doctorSaveResult (YcxLmtPrelResultDto dto) {
        Integer totalScore = null;
        Integer level = null;
        if (CollectionUtil.isEmpty (dto.getSelectedList ())) {
            level = dto.getLevel ();
        } else {
            totalScore = dto.getSelectedList ().stream ().distinct().mapToInt (YcxLmtPrelSelected::getScore).sum ();
            if (totalScore >= 0 && totalScore <= 24) {
                level = 1;
            } else if (totalScore >= 25 && totalScore <= 49) {
                level = 2;
            } else if (totalScore >= 50 && totalScore <= 70) {
                level = 3;
            }
        }

        YcxLmtPrelResult result = new YcxLmtPrelResult ();
        result.setRecordId (dto.getRecordId ());
        result.setOrgId (dto.getOrgId ());
        result.setChildrenId (dto.getChildrenId ());
        result.setScore (totalScore);
        result.setLevel (level);
        result.setOperatorType (1);
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxLmtPrelResultMapper.insert (result);

        if (CollectionUtil.isNotEmpty (dto.getSelectedList ())) {
            dto.getSelectedList ().stream().distinct().forEach (selected -> {
                selected.setOrgId (dto.getOrgId ());
                selected.setChildrenId (dto.getChildrenId ());
                selected.setResultId (result.getId ());
                selected.setStatus (1);
                selected.setCreateTime (new Date ());
                selected.setUpdateTime (new Date ());
            });
            selectedMapper.insertList (dto.getSelectedList ());
        }
        return result;
    }

    /**
     * 家长端保存初评表结果
     *
     * @param dto
     * @return
     */
    @Override
    public YcxLmtPrelResult parentSaveResult (YcxLmtPrelResultDto dto) {
        Example example = new Example (YcxLmtPrelResult.class);
        example.createCriteria ().andEqualTo ("childrenId", dto.getChildrenId ());
        example.orderBy ("createTime").desc ();
        PageHelper.startPage (1, 1);
        YcxLmtPrelResult checkResult = ycxLmtPrelResultMapper.selectOneByExample (example);

        Integer totalScore = null;
        Integer level = null;
        if (CollectionUtil.isEmpty (dto.getSelectedList ())) {
            level = dto.getLevel ();
        } else {
            totalScore = dto.getSelectedList ().stream ().distinct().mapToInt (YcxLmtPrelSelected::getScore).sum ();
            if (totalScore >= 0 && totalScore <= 24) {
                level = 1;
            } else if (totalScore >= 25 && totalScore <= 49) {
                level = 2;
            } else if (totalScore >= 50 && totalScore <= 70) {
                level = 3;
            }
        }

        YcxLmtPrelResult result = new YcxLmtPrelResult ();
        result.setOrgId (dto.getOrgId ());
        result.setChildrenId (dto.getChildrenId ());
        result.setScore (totalScore);
        result.setLevel (level);
        result.setOperatorType (SecurityUtil.checkLogin () ? 1 : 2);
        result.setOperatorId (SecurityUtil.checkLogin () ? SecurityUtil.getUserId () : 0);
        result.setOperatorName (SecurityUtil.checkLogin () ? SecurityUtil.getNickName () : "家长填写");
        result.setCreateTime (new Date ());
        if (Objects.nonNull (checkResult) && Objects.isNull (checkResult.getRecordId ())) {
            result.setId (checkResult.getId ());
            ycxLmtPrelResultMapper.updateByPrimaryKeySelective (result);
        } else {
            ycxLmtPrelResultMapper.insert (result);
        }

        if (CollectionUtil.isNotEmpty (dto.getSelectedList ())) {
            dto.getSelectedList ().stream().distinct().forEach (selected -> {
                selected.setOrgId (dto.getOrgId ());
                selected.setChildrenId (dto.getChildrenId ());
                selected.setResultId (result.getId ());
                selected.setStatus (1);
                selected.setCreateTime (new Date ());
                selected.setUpdateTime (new Date ());
            });
            if (Objects.nonNull (checkResult) && Objects.isNull (checkResult.getRecordId ())) {
                // 删除之前的选项
                YcxLmtPrelSelected param = new YcxLmtPrelSelected ();
                param.setStatus (0);
                Example selectedExample = new Example (YcxLmtPrelSelected.class);
                selectedExample.createCriteria ().andEqualTo ("resultId", checkResult.getId ());
                selectedMapper.updateByExampleSelective (param, selectedExample);
            }
            selectedMapper.insertList (dto.getSelectedList ());
        }
        return result;
    }

    /**
     * 根据儿童ID获取初评结果
     *
     * @param childrenId 儿童ID
     * @param isParent 是否只查询家长端填写的结果
     * @return
     */
    @Override
    public YcxLmtPrelResult getResultByChildrenId (Integer childrenId, Integer isParent) {
        Example example = new Example (YcxLmtPrelResult.class);
        Example.Criteria criteria = example.createCriteria ().andEqualTo ("childrenId", childrenId);
        if (Objects.nonNull (isParent) && isParent == 1) {
            criteria.andEqualTo ("operatorType", 2);
        }
        example.orderBy ("createTime").desc ();
        PageHelper.startPage (1, 1);
        YcxLmtPrelResult result = ycxLmtPrelResultMapper.selectOneByExample (example);
        if (Objects.isNull (result)) {
            return null;
        }
        if (Objects.nonNull (result.getScore ()) && StrUtil.isEmpty (result.getResult ())) {
            // 分数不为空且结果为空时，生成result:领域得分
            List<YcxLmtPrelDomainResultVo> domainScoreList = selectedMapper.getDomainScoreList (result.getId ());
            if (CollectionUtil.isNotEmpty (domainScoreList)) {
                result.setResult (JSONUtil.toJsonStr (domainScoreList));
                ycxLmtPrelResultMapper.updateByPrimaryKeySelective (result);
            }
        }
        return result;
    }

    /**
     * 根据记录ID获取初评结果
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public YcxLmtPrelResult getResultByRecordId (Integer recordId) {
        Example example = new Example (YcxLmtPrelResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        example.orderBy ("createTime").desc ();
        PageHelper.startPage (1, 1);
        YcxLmtPrelResult result = ycxLmtPrelResultMapper.selectOneByExample (example);

        if (Objects.isNull (result)) {
            return null;
        }
        if (Objects.nonNull (result.getScore ()) && StrUtil.isEmpty (result.getResult ())) {
            // 分数不为空且结果为空时，生成result:领域得分
            List<YcxLmtPrelDomainResultVo> domainScoreList = selectedMapper.getDomainScoreList (result.getId ());
            if (CollectionUtil.isNotEmpty (domainScoreList)) {
                result.setResult (JSONUtil.toJsonStr (domainScoreList));
                ycxLmtPrelResultMapper.updateByPrimaryKeySelective (result);
            }
        }
        return result;
    }
}
