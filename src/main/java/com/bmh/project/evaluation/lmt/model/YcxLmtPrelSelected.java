package com.bmh.project.evaluation.lmt.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode
@Table(name = "ycx_lmt_prel_selected")
public class YcxLmtPrelSelected implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 初评结果ID
     */
    @Column(name = "result_id")
    private Integer resultId;

    /**
     * 题目ID
     */
    @Column(name = "lmt_prel_id")
    private Integer lmtPrelId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}
