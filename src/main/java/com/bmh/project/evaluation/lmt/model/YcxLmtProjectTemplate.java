package com.bmh.project.evaluation.lmt.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_lmt_project_template")
public class YcxLmtProjectTemplate implements Serializable {
    /**
     * 领域ID
     */
    @Column(name = "domain_id")
    private Integer domainId;

    /**
     * 领域名称
     */
    @Column(name = "domain_name")
    private String domainName;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private Integer projectId;

    /**
     * 项目名称
     */
    @Column(name = "project_name")
    private String projectName;

    /**
     * 项目阶段
     */
    @Column(name = "project_level")
    private Integer projectLevel;

    /**
     * 短期目标ID
     */
    @Column(name = "short_goal_id")
    private Integer shortGoalId;

    /**
     * 短期目标名称
     */
    @Column(name = "short_goal_name")
    private String shortGoalName;

    private static final long serialVersionUID = 1L;
}