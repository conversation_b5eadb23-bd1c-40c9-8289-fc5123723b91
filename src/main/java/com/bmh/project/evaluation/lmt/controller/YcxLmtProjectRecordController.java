package com.bmh.project.evaluation.lmt.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectRecord;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectRecordService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * LMT评估-评估项目记录接口
 *
 * <AUTHOR>
 * @since 2023/8/17 10:20
 */
@RestController
@RequestMapping("/lmt/project/record")
public class YcxLmtProjectRecordController {

    @Resource
    private YcxLmtProjectRecordService ycxLmtProjectRecordService;

    /**
     * 增加记录
     * @param record 记录数据
     * @return
     */
    @RequestMapping ("/add")
    public Result<Integer> add (YcxLmtProjectRecord record) {
        record.setStatus (1);
        record.setCreateUser (SecurityUtil.getNickName ());
        record.setCreateTime (new Date ());
        record.setUpdateUser (SecurityUtil.getNickName ());
        record.setUpdateTime (new Date ());
        ycxLmtProjectRecordService.insert (record);
        // 计算统计数据
        ycxLmtProjectRecordService.calcPassCount (record.getLmtProjectId ());
        return ResultUtil.success (record.getId ());
    }

    /**
     * 删除记录
     * @param recordId 记录ID
     * @return
     */
    @RequestMapping("/del")
    public Result<?> del(Integer recordId){
        YcxLmtProjectRecord record = ycxLmtProjectRecordService.selectByPrimaryKey (recordId);
        if(Objects.isNull (record)){
            return ResultUtil.error ("记录ID不正确");
        }
        record.setStatus (0);
        record.setUpdateTime (new Date ());
        record.setUpdateUser (SecurityUtil.getNickName ());
        ycxLmtProjectRecordService.updateNotNull (record);
        // 计算统计数据
        ycxLmtProjectRecordService.calcPassCount (record.getLmtProjectId ());
        return ResultUtil.success ();
    }
}
