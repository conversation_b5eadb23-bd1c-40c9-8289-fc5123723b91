package com.bmh.project.evaluation.lmt.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelResult;
import com.bmh.project.report.vo.ReportLMTInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface YcxLmtPrelResultMapper extends BaseMapper<YcxLmtPrelResult> {
    List<ReportLMTInfoVo> selectAnswerAndContent(@Param("childrenId") Integer childrenId, @Param("recordId") String recordId);
}