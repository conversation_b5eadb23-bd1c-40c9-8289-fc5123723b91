package com.bmh.project.evaluation.lmt.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_lmt_prel_result")
public class YcxLmtPrelResult implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评估ID信息
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 阶段
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 初评结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 操作人类型(1医生 2家长)
     */
    @Column(name = "operator_type")
    private Integer operatorType;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}