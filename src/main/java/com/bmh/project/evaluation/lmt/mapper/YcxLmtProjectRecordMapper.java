package com.bmh.project.evaluation.lmt.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtDomain;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface YcxLmtProjectRecordMapper extends BaseMapper<YcxLmtProjectRecord> {

    /**
     * 计算项目通过数量
     *
     * @param lmtProjectId 项目ID
     */
    Map<String, BigDecimal> calcPassCount (Integer lmtProjectId);

    /**
     * 计算领域结果
     * @param recordId 评测记录ID
     * @return
     */
    List<YcxLmtDomain> calcDomainResult (Integer recordId);
}