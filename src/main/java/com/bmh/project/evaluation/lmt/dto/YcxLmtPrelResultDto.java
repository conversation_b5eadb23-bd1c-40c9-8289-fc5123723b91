package com.bmh.project.evaluation.lmt.dto;

import com.bmh.project.evaluation.lmt.model.YcxLmtPrelSelected;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * LMT初评表保存结果DTO
 *
 * <AUTHOR>
 * @since 2023/8/15 15:12
 */
@Data
public class YcxLmtPrelResultDto {

    /**
     * 评估ID信息
     */
    private Integer recordId;

    /**
     * 机构ID
     */
    @NotNull (message = "机构ID不能为空")
    private Integer orgId;

    /**
     * 儿童ID
     */
    @NotNull(message = "儿童ID不能为空")
    private Integer childrenId;

    /**
     * 阶段
     */
    private Integer level;

    /**
     * 选项记录
     */
    List<YcxLmtPrelSelected> selectedList;
}
