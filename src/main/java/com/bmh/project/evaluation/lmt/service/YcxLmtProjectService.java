package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.lmt.model.YcxLmtProject;
import com.bmh.project.evaluation.lmt.vo.YcxLmtProjectVo;

import java.util.HashMap;
import java.util.List;

/**
 * ycx_lmt_project表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年08月16日 16:41:48
 */
public interface YcxLmtProjectService extends BaseService<YcxLmtProject> {

    /**
     * 获取评估内容
     * @param recordId 记录ID
     * @param prelResultId 初评结果ID
     * @param level 阶段(第一次必传)
     * @return
     */
    HashMap<String, Object> getList (Integer recordId, Integer prelResultId, Integer level);

    /**
     * 获取评估内容
     * @param recordId 记录ID
     * @return
     */
    List<YcxLmtProjectVo> supervisorGetList (Integer recordId);

    /**
     * 检查评估所处阶段
     * @param recordId 记录ID
     * @return
     */
    Integer checkHaveProject(Integer recordId);
}