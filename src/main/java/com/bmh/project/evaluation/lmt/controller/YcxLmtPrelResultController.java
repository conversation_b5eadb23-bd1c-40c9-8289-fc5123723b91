package com.bmh.project.evaluation.lmt.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.lmt.dto.YcxLmtPrelResultDto;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelResult;
import com.bmh.project.evaluation.lmt.service.YcxLmtPrelResultService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * LMT评估-初评表结果接口
 *
 * <AUTHOR>
 * @since 2023/8/15 13:57
 */
@RestController
@RequestMapping("/lmt/prel/result")
public class YcxLmtPrelResultController {

    @Resource
    private YcxLmtPrelResultService resultService;

    /**
     * 保存结果-医生端
     *
     * @return
     */
    @RequestMapping("/saveResult")
    public Result<YcxLmtPrelResult> saveResult(@Validated  @RequestBody YcxLmtPrelResultDto dto){
        if(Objects.isNull (dto.getRecordId ())){
            return ResultUtil.error ("评估ID不能为空");
        }
        YcxLmtPrelResult result =  resultService.doctorSaveResult (dto);
        return ResultUtil.success(result);
    }

    /**
     * 绑定评估记录ID
     * @param resultId 结果ID
     * @param recordId 评估记录ID
     * @return
     */
    @PostMapping ("/saveRecordId")
    public Result<?>  saveRecordId(Integer resultId,Integer recordId){
        YcxLmtPrelResult result = new YcxLmtPrelResult ();
        result.setId (resultId);
        result.setRecordId (recordId);
        resultService.updateNotNull (result);
        return ResultUtil.success ();
    }

    /**
     * 保存结果-家长端
     *
     * @return
     */
    @RequestMapping("/open/saveResult")
    public Result<YcxLmtPrelResult> openSaveResult(@Validated  @RequestBody YcxLmtPrelResultDto dto){
        YcxLmtPrelResult result = resultService.parentSaveResult (dto);
        return ResultUtil.success(result);
    }

    /**
     * 根据儿童ID获取初评结果
     * @param childrenId 儿童ID
     * @param isParent 是否只查询家长端填写的结果
     * @return
     */
    @RequestMapping("/getResult")
    public Result<YcxLmtPrelResult>  getResult(Integer childrenId, Integer isParent){
        YcxLmtPrelResult result = resultService.getResultByChildrenId (childrenId, isParent);
        return ResultUtil.success (result);
    }

    /**
     * 根据儿童ID获取初评结果
     * @param recordId 评估记录ID
     * @return
     */
    @RequestMapping("/getResultByRecordId")
    public Result<YcxLmtPrelResult>  getResultByRecordId(Integer recordId){
        YcxLmtPrelResult result = resultService.getResultByRecordId (recordId);
        return ResultUtil.success (result);
    }

    /**
     * 根据儿童ID获取初评结果-家长端
     * @param childrenId 儿童ID
     * @return
     */
    @RequestMapping("/open/getResult")
    public Result<YcxLmtPrelResult>  getOpenResult(Integer childrenId, Integer isParent){
        YcxLmtPrelResult result = resultService.getResultByChildrenId (childrenId, isParent);
        return ResultUtil.success (result);
    }
}
