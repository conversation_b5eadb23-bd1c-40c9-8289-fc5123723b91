package com.bmh.project.evaluation.lmt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.mapper.YcxChildrenEvaluatingMapper;
import com.bmh.project.book.mapper.YcxChildrenEvaluatingProjectMapper;
import com.bmh.project.book.model.YcxChildrenEvaluating;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelResultMapper;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelResult;
import com.bmh.project.evaluation.lmt.model.YcxLmtProject;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectTemplate;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectService;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectTemplateService;
import com.bmh.project.evaluation.lmt.vo.YcxLmtProjectDomainVo;
import com.bmh.project.evaluation.lmt.vo.YcxLmtProjectVo;
import com.github.pagehelper.PageHelper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * YcxLmtProjectService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年08月16日 16:41:48
 */
@Service
public class YcxLmtProjectServiceImpl extends BaseServiceImpl<YcxLmtProject> implements YcxLmtProjectService {
    @Resource
    private YcxLmtProjectMapper lmtProjectMapper;
    @Resource
    private YcxChildrenEvaluatingMapper evaluatingMapper;
    @Resource
    private YcxChildrenEvaluatingProjectMapper evaluatingProjectMapper;
    @Resource
    private YcxLmtProjectTemplateService lmtProjectTemplateService;
    @Resource
    private YcxLmtPrelResultMapper lmtPrelResultMapper;


    /**
     * 获取评估内容
     * @param recordId 记录ID
     * @param prelResultId 初评结果ID
     * @param level 阶段(第一次必传)
     * @return
     */
    @Override
    public HashMap<String, Object> getList (Integer recordId, Integer prelResultId, Integer level) {
        HashMap<String, Object> resultMap = new HashMap<> ();
        List<YcxLmtProjectVo> lmtProjectList = lmtProjectMapper.getList (recordId);
        PageHelper.startPage (1,1);
        List<YcxLmtProject> allProject = this.selectByParentId (YcxLmtProject.class, "recordId", recordId);
        if(CollectionUtil.isEmpty (lmtProjectList) && CollectionUtil.isEmpty (allProject) && Objects.nonNull (level)){
            // 初始化5个项目
            this.setDefaultProject (recordId, level);
            lmtProjectList = lmtProjectMapper.getList (recordId);

            // 更新初评结果
            YcxLmtPrelResult prelResult = new YcxLmtPrelResult ();
            prelResult.setId (prelResultId);
            prelResult.setRecordId (recordId);
            lmtPrelResultMapper.updateByPrimaryKeySelective (prelResult);
        }
        List<YcxLmtProjectDomainVo> domainList = lmtProjectList.stream()
                .collect(Collectors.groupingBy(YcxLmtProjectVo::getDomainId, LinkedHashMap::new, Collectors.toList()))
                .entrySet().stream()
                .map(entry -> {
                    YcxLmtProjectDomainVo domainVo = new YcxLmtProjectDomainVo ();
                    domainVo.setDomainId(entry.getKey());
                    domainVo.setDomainName(entry.getValue().get(0).getDomainName());
                    domainVo.setProjectList(entry.getValue());
                    return domainVo;
                })
                .collect(Collectors.toList());

        resultMap.put ("level", Objects.nonNull (level)?level:allProject.get (0).getProjectLevel ());
        resultMap.put ("domainList", domainList);
        return resultMap;
    }

    /**
     * 获取评估内容
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public List<YcxLmtProjectVo> supervisorGetList (Integer recordId) {
        return lmtProjectMapper.getList (recordId);
    }

    /**
     * 检查评估所处阶段
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public Integer checkHaveProject (Integer recordId) {
        Example example = new Example (YcxLmtProject.class);
        example.createCriteria ().andEqualTo ("recordId", recordId).andEqualTo ("status", 1);
        int projectCount = lmtProjectMapper.selectCountByExample (example);

        return projectCount > 0 ? 1 : 0;
    }

    /**
     * 设置默认项目
     * @param recordId 记录ID
     * @param level 阶段
     */
    private void setDefaultProject(Integer recordId, Integer level){
        // 获取记录信息
        YcxChildrenEvaluatingProject childrenEvaluatingProject = evaluatingProjectMapper.selectByPrimaryKey (recordId);
        YcxChildrenEvaluating childrenEvaluating = evaluatingMapper.selectByPrimaryKey (childrenEvaluatingProject.getEvaluatingId ());

        // 获取项目模板信息
        List<YcxLmtProjectTemplate> projectTemplateList = lmtProjectTemplateService.getListByLevel (level);
        List<YcxLmtProject> lmtProjectList = BeanUtil.copyToList (projectTemplateList, YcxLmtProject.class);
        lmtProjectList.forEach (lmtProject -> {
            lmtProject.setChildrenId(childrenEvaluating.getChildrenId ());
            lmtProject.setRecordId(recordId);
            lmtProject.setPassCount(0);
            lmtProject.setFailCount(0);
            lmtProject.setPassRate(0.0D);
            lmtProject.setIsDefault (1);
            lmtProject.setStatus(1);
            lmtProject.setCreateUser(SecurityUtil.getNickName ());
            lmtProject.setCreateTime(new Date());
            lmtProject.setUpdateTime(new Date());
            lmtProject.setUpdateUser(SecurityUtil.getNickName ());
        });

        this.insertList (lmtProjectList);
    }

}