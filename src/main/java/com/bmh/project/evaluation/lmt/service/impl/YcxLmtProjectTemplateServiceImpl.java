package com.bmh.project.evaluation.lmt.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectTemplateMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectTemplate;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectTemplateService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxLmtProjectTemplateService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年11月14日 16:37:39
 */
@Service
public class YcxLmtProjectTemplateServiceImpl extends BaseServiceImpl<YcxLmtProjectTemplate> implements YcxLmtProjectTemplateService {
    @Resource
    private YcxLmtProjectTemplateMapper ycxLmtProjectTemplateMapper;

    /**
     * 根据阶段获取项目
     *
     * @param level 阶段
     * @return
     */
    @Override
    public List<YcxLmtProjectTemplate> getListByLevel (Integer level) {
        Example example = new Example (YcxLmtProjectTemplate.class);
        example.createCriteria ().andEqualTo ("projectLevel", level);
        example.orderBy ("domainId").asc ().orderBy ("projectId").asc ();
        return ycxLmtProjectTemplateMapper.selectByExample (example);
    }
}