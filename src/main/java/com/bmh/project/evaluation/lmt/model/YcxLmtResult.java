package com.bmh.project.evaluation.lmt.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_lmt_result")
public class YcxLmtResult extends YcxResult implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private String childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 阶段
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 阶段解释
     */
    @Column(name = "level_explain")
    private String levelExplain;

    /**
     * 阶段建议
     */
    @Column(name = "level_recom")
    private String levelRecom;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 测试结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 初评表ID
     */
    @Column(name = "prel_result_id")
    private Integer prelResultId;

    /**
     * 初评表阶段
     */
    @Column(name = "prel_result_level")
    private Integer prelResultLevel;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}