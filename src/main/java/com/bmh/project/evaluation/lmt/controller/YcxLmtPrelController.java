package com.bmh.project.evaluation.lmt.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrel;
import com.bmh.project.evaluation.lmt.service.YcxLmtPrelService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * LMT评估-初评表接口
 *
 * <AUTHOR>
 * @since 2023/8/15 13:57
 */
@RestController
@RequestMapping("/lmt/prel")
public class YcxLmtPrelController {

    @Resource
    private YcxLmtPrelService lmtPrelService;

    /**
     * 获取问题列表
     *
     * @param domainId 领域ID
     * @param resultId 评估结果ID
     * @return
     */
    @RequestMapping("/getList")
    public Result<List<YcxLmtPrel>> getList(Integer domainId, Integer resultId){
        List<YcxLmtPrel> iatList = lmtPrelService.getList(domainId, resultId);
        return ResultUtil.success(iatList);
    }

    /**
     * 获取问题列表
     *
     * @return
     */
    @RequestMapping("/open/getList")
    public Result<List<YcxLmtPrel>> openGetList(){
        List<YcxLmtPrel> iatList = lmtPrelService.getList(null, null);
        return ResultUtil.success(iatList);
    }
}
