package com.bmh.project.evaluation.lmt.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.NumberUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtDomainMapper;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectRecordMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtDomain;
import com.bmh.project.evaluation.lmt.service.YcxLmtDomainService;
import com.bmh.project.evaluation.lmt.vo.YcxLmtDomainResultVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * YcxLmtDomainService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年11月14日 18:13:29
 */
@Service
public class YcxLmtDomainServiceImpl extends BaseServiceImpl<YcxLmtDomain> implements YcxLmtDomainService {
    @Resource
    private YcxLmtDomainMapper ycxLmtDomainMapper;
    @Resource
    private YcxLmtProjectRecordMapper projectRecordMapper;

    /**
     * 统计领域结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public List<YcxLmtDomainResultVo> statisticalDomainResult (Integer recordId) {
        List<YcxLmtDomain> domainList = projectRecordMapper.calcDomainResult (recordId);
        domainList.forEach (domain -> {
            BigDecimal passRate = NumberUtil.div (String.valueOf (domain.getPassCount ()),
                    String.valueOf (domain.getPassCount () + domain.getFailCount ()),
                    2, RoundingMode.DOWN);
            domain.setPassRate (passRate.doubleValue ());
            domain.setStatus (1);
            domain.setCreateUser (SecurityUtil.getNickName ());
            domain.setCreateTime (new Date ());
            domain.setUpdateUser (SecurityUtil.getNickName ());
            domain.setUpdateTime (new Date ());
        });

        ycxLmtDomainMapper.insertList (domainList);

        return BeanUtil.copyToList (domainList, YcxLmtDomainResultVo.class);
    }
}