package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrel;

import java.util.List;

/**
 * ycx_lmt_prel表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年08月15日 13:57:08
 */
public interface YcxLmtPrelService extends BaseService<YcxLmtPrel> {

    /**
     * 获取问题列表
     *
     * @param domainId 领域ID
     * @param resultId 评估结果ID
     * @return
     */
    List<YcxLmtPrel> getList(Integer domainId, Integer resultId);
}