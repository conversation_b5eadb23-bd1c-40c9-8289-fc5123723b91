package com.bmh.project.evaluation.lmt.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.lmt.mapper.YcxLmtResultMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtResult;
import com.bmh.project.evaluation.lmt.service.YcxLmtDomainService;
import com.bmh.project.evaluation.lmt.service.YcxLmtResultService;
import com.bmh.project.evaluation.lmt.vo.YcxLmtDomainResultVo;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxLmtResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年08月17日 12:54:44
 */
@Service ("41")
public class YcxLmtResultServiceImpl extends BaseServiceImpl<YcxLmtResult> implements YcxLmtResultService, YcxResultService<YcxLmtResult> {
    @Resource
    private YcxLmtResultMapper ycxLmtResultMapper;
    @Resource
    private YcxLmtDomainService lmtDomainService;

    private final String[] LEVEL_EXPLAIN = {
            "1.在七大领域的发育处于萌芽和严重落后阶段;\n" +
                    "2.亟需要立刻康复，且干预时长至少是60h/月;\n" +
                    "3.处于这阶段的孩子，往往表现出社交无意识状态，对周围社交环境是“目中无人 ”。应从最基础的眼神交流、辨认熟人，手指提要求、呼名反应、仿说能力等为基础进行干预。这些社交元素既是诊断孤独症的重要依据，也是社交和沟通的先备技能",
            "1.在七大领域的发育处于正在发展但部分领域严重不足;\n" +
                    "2.亟需要立刻康复，且干预时长至少是40h/月;\n" +
                    "3.当孩子具备基本的社交和主动与人沟通的动机时，会在这一阶段重点提升恰当的社交互动和沟通技能。这阶段我们会结合社交小组课干预的方式，为孩子创造互动的场景和机会，并重点提升其等待、情绪识别、分享、选择、轮流、基础回合对话等技能。",
            "1.在七大领域的发育处于渐成熟且有融合需求;\n" +
                    "2.亟需要立刻康复，且干预时长至少是30h/月;\n" +
                    "3.对于轻度的3-8岁孤独症孩子，应以回归生活，回归主流幼儿园和小学作为干预的首要目标，我们在干预时会模拟这些生活学习场景，为孩子提升融入集体的技能。相反如果孩子没有语言、没有交往，集体环境坐不定，不会提问，有严重的攻击行为，那即使在幼儿园能够混读，也难以实现干预的理想效果。这一级的社交元素包括提升孩子玩合作、竞争、输赢、分享和轮流游戏，以及学习遵守集体规则。"};
    private final String LEVEL_RECOM = "（1）正确率在0%-39%，此项目/阶段不适合作为当前教学目标，需要降低阶段/更换目标;\n" +
            "（2）正确率在40%-79%，此项目可作为当前教学目标;\n" +
            "（3）正确率在80%-100%，此项目孩子已经精熟，作为巩固泛化内容";

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxLmtResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxLmtResult.class);

        // 统计领域结果
        List<YcxLmtDomainResultVo> domainList = lmtDomainService.statisticalDomainResult (childrenRecord.getId ());

        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge () + "");
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setLevel (calculateStage (domainList,result.getPrelResultLevel ()));
        result.setLevelExplain (LEVEL_EXPLAIN[result.getLevel () - 1]);
        result.setLevelRecom (LEVEL_RECOM);
        result.setResult (JSONUtil.toJsonStr (domainList));
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxLmtResultMapper.insert (result);
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxLmtResult getResult (Integer recordId) {
        Example example = new Example (YcxLmtResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxLmtResult> list = ycxLmtResultMapper.selectByExample (example);
        return CollectionUtil.isNotEmpty (list) ? list.get (0) : null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxLmtResult result = new YcxLmtResult ();
        result.setRecom (recom);
        Example example = new Example (YcxLmtResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxLmtResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject (List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult (YcxChildrenEvaluatingProject record) {

    }

    /**
     * 获取评测结果
     *
     * @param childrenId 儿童ID
     * @return
     */
    @Override
    public List<YcxLmtResult> getResultByChildId (Integer childrenId) {
        Example example = new Example (YcxLmtResult.class);
        example.createCriteria ().andEqualTo ("childrenId", childrenId);
        List<YcxLmtResult> list = ycxLmtResultMapper.selectByExample (example);
        return list;
    }

    /**
     * 根据领域结果计算阶段
     *
     * @param domainResults   领域结果
     * @param prelResultLevel 初评表阶段
     * @return
     */
    private Integer calculateStage (List<YcxLmtDomainResultVo> domainResults, Integer prelResultLevel) {
        long lowStageCount = domainResults.stream ()
                .filter (domainResult -> domainResult.getPassRate () <= 0.40d)
                .count ();

        long midStageCount = domainResults.stream ()
                .filter (domainResult -> domainResult.getPassRate () >= 0.41d && domainResult.getPassRate () <= 0.79d)
                .count ();

        long highStageCount = domainResults.stream ()
                .filter (domainResult -> domainResult.getPassRate () >= 0.80d)
                .count ();

        if (lowStageCount >= 5) {
            return prelResultLevel == 1 ? prelResultLevel : prelResultLevel - 1;
        } else if (midStageCount >= 5) {
            return prelResultLevel == 3 ? prelResultLevel : prelResultLevel + 1;
        } else if (highStageCount >= 5) {
            return prelResultLevel == 3 ? prelResultLevel : prelResultLevel + 1;
        } else {
            if (2 <= midStageCount) {
                return prelResultLevel;
            } else {
                return prelResultLevel == 3 ? prelResultLevel : prelResultLevel + 1;
            }
        }
    }
}