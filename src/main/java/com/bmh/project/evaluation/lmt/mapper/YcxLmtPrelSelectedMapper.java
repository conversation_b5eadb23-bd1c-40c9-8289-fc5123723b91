package com.bmh.project.evaluation.lmt.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelSelected;
import com.bmh.project.evaluation.lmt.vo.YcxLmtPrelDomainResultVo;

import java.util.List;

public interface YcxLmtPrelSelectedMapper extends BaseMapper<YcxLmtPrelSelected> {

    /**
     * 获取领域分数列表
     * @param resultId
     * @return
     */
    List<YcxLmtPrelDomainResultVo> getDomainScoreList(Integer resultId);
}