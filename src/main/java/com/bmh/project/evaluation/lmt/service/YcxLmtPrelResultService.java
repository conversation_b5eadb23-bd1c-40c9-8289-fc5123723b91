package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.lmt.dto.YcxLmtPrelResultDto;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrelResult;

/**
 * ycx_lmt_prel_result表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年08月15日 13:57:08
 */
public interface YcxLmtPrelResultService extends BaseService<YcxLmtPrelResult> {

    /**
     * 医生端保存初评表结果
     * @param dto
     * @return
     */
    YcxLmtPrelResult doctorSaveResult (YcxLmtPrelResultDto dto);

    /**
     * 家长端保存初评表结果
     * @param dto
     * @return
     */
    YcxLmtPrelResult parentSaveResult (YcxLmtPrelResultDto dto);

    /**
     * 根据儿童ID获取初评结果
     * @param childrenId 儿童ID
     * @param isParent 是否只查询家长端填写的结果
     * @return
     */
    YcxLmtPrelResult getResultByChildrenId(Integer childrenId, Integer isParent);

    /**
     * 根据记录ID获取初评结果
     * @param recordId 记录ID
     * @return
     */
    YcxLmtPrelResult getResultByRecordId(Integer recordId);
}