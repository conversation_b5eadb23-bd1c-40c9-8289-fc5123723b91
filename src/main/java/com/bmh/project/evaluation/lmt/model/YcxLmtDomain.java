package com.bmh.project.evaluation.lmt.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_lmt_domain")
public class YcxLmtDomain implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 儿童ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 评估记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 领域ID
     */
    @Column(name = "domain_id")
    private Integer domainId;

    /**
     * 领域名称
     */
    @Column(name = "domain_name")
    private String domainName;

    /**
     * 目标达成数量
     */
    @Column(name = "pass_count")
    private Integer passCount;

    /**
     * 目标未达成数量
     */
    @Column(name = "fail_count")
    private Integer failCount;

    /**
     * 正确率(该领域下【+的数量】/【做过的回合数】向下取整)
     */
    @Column(name = "pass_rate")
    private Double passRate;

    /**
     * 状态(0已删除 1正常)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    private static final long serialVersionUID = 1L;
}