package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.lmt.model.YcxLmtProjectRecord;

/**
 * ycx_lmt_project_record表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年08月16日 16:41:48
 */
public interface YcxLmtProjectRecordService extends BaseService<YcxLmtProjectRecord> {

    /**
     * 计算项目通过数量
     * @param lmtProjectId 项目ID
     */
    void calcPassCount (Integer lmtProjectId);

    /**
     * 根据项目ID删除项目记录
     * @param lmtProjectId 项目ID
     */
    void delByLmtProjectId (Integer lmtProjectId);
}