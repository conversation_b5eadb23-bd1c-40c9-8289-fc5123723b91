package com.bmh.project.evaluation.lmt.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.evaluation.lmt.model.YcxLmtProject;
import com.bmh.project.evaluation.lmt.model.YcxLmtResult;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectRecordService;
import com.bmh.project.evaluation.lmt.service.YcxLmtProjectService;
import com.bmh.project.evaluation.lmt.service.YcxLmtResultService;
import com.bmh.project.evaluation.lmt.vo.YcxLmtProjectVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * LMT评估-评估项目接口
 *
 * <AUTHOR>
 * @since 2023/8/16 16:46
 */
@RestController
@RequestMapping ("/lmt/project")
public class YcxLmtProjectController {

    @Resource
    private YcxLmtProjectService lmtProjectService;
    @Resource
    private YcxLmtProjectRecordService  lmtProjectRecordService;

    @Resource
    private YcxLmtResultService lmtResultService;


    /**
     * 获取评估内容
     * @param recordId 记录ID
     * @param prelResultId 初评结果ID
     * @param level 阶段(第一次必传)
     * @return
     */
    @RequestMapping ("/getList")
    public Result<HashMap<String, Object>> getList (Integer recordId, Integer prelResultId, Integer level) {
        HashMap<String, Object> result = lmtProjectService.getList (recordId,prelResultId, level);
        return ResultUtil.success (result);
    }

    /**
     * 检查评估所处阶段
     * @param recordId 记录ID
     * @return
     */
    @GetMapping ("/checkHaveProject")
    public Result<?> checkHaveProject(Integer recordId){
        Integer haveProject = lmtProjectService.checkHaveProject (recordId);
        return ResultUtil.success (haveProject);
    }

    /**
     * 督导端-获取评估内容
     * @param recordId 记录ID
     * @return
     */
    @RequestMapping ("/supervisor/getList")
    public Result<List<YcxLmtProjectVo>> supervisorGetList (Integer recordId) {
        List<YcxLmtProjectVo> result = lmtProjectService.supervisorGetList (recordId);
        return ResultUtil.success (result);
    }

    /**
     * 新增项目
     * @param project
     * @return
     */
    @RequestMapping("/add")
    public Result<?> add(YcxLmtProject project){
        project.setPassCount(0);
        project.setFailCount(0);
        project.setPassRate(0.0D);
        project.setStatus(1);
        project.setCreateUser(SecurityUtil.getNickName ());
        project.setCreateTime(new Date());
        project.setUpdateTime(new Date());
        project.setUpdateUser(SecurityUtil.getNickName ());
        lmtProjectService.insert (project);
        return ResultUtil.success (project.getId ());
    }

    /**
     * 删除项目
     * @param lmtProjectId 项目ID
     * @return
     */
    @RequestMapping("/del")
    public Result<?>  del(Integer lmtProjectId){
        YcxLmtProject project = new YcxLmtProject ();
        project.setId (lmtProjectId);
        project.setStatus (0);
        project.setUpdateTime (new Date ());
        project.setUpdateUser (SecurityUtil.getNickName ());
        lmtProjectService.updateNotNull (project);
        lmtProjectRecordService.delByLmtProjectId (lmtProjectId);
        return ResultUtil.success ();
    }

    /**
     * 检查该儿童是否存在评估结果
     * @param childrenId 儿童ID
     * @return
     */
    @RequestMapping("/checkResultExit")
    public Result<?>  checkResultExit(Integer childrenId){
        List<YcxLmtResult> resultList = lmtResultService.getResultByChildId (childrenId);
        return ResultUtil.success (CollectionUtil.isNotEmpty (resultList)?1:0);
    }
}
