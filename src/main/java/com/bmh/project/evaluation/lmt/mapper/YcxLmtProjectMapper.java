package com.bmh.project.evaluation.lmt.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtProject;
import com.bmh.project.evaluation.lmt.vo.YcxLmtProjectVo;
import org.apache.ibatis.annotations.Param;
import java.util.List;

public interface YcxLmtProjectMapper extends BaseMapper<YcxLmtProject> {

    /**
     * 获取评估内容
     * @param recordId 记录ID
     * @return
     */
    List<YcxLmtProjectVo> getList (@Param("recordId") Integer recordId);
}