package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cmt.model.YcxCmtResult;
import com.bmh.project.evaluation.lmt.model.YcxLmtResult;

import java.util.List;

/**
 * ycx_lmt_result表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年08月17日 12:54:44
 */
public interface YcxLmtResultService extends BaseService<YcxLmtResult> {

    /**
     * 获取评测结果
     * @param childrenId 儿童ID
     * @return
     */
    List<YcxLmtResult> getResultByChildId(Integer childrenId);
}