package com.bmh.project.evaluation.lmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.lmt.model.YcxLmtDomain;
import com.bmh.project.evaluation.lmt.vo.YcxLmtDomainResultVo;

import java.util.List;

/**
 * ycx_lmt_domain表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年11月14日 18:13:29
 */
public interface YcxLmtDomainService extends BaseService<YcxLmtDomain> {

    /**
     * 统计领域结果
     * @param recordId 评测记录ID
     * @return
     */
    List<YcxLmtDomainResultVo> statisticalDomainResult(Integer recordId);
}