package com.bmh.project.evaluation.lmt.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.lmt.model.YcxLmtPrel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface YcxLmtPrelMapper extends BaseMapper<YcxLmtPrel> {

    /**
     * 获取问题列表
     *
     * @param domainId 领域ID
     * @param resultId 评估结果ID
     * @return
     */
    List<YcxLmtPrel> getList (@Param ("domainId") Integer domainId, @Param ("resultId") Integer resultId);
}