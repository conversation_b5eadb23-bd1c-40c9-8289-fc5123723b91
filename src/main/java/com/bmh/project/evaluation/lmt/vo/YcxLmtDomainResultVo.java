package com.bmh.project.evaluation.lmt.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class YcxLmtDomainResultVo implements Serializable {

    /**
     * 领域ID
     */
    private Integer domainId;

    /**
     * 领域名称
     */
    private String domainName;

    /**
     * 目标达成数量
     */
    private Integer passCount;

    /**
     * 目标未达成数量
     */
    private Integer failCount;

    /**
     * 正确率(该领域下【+的数量】/【做过的回合数】向下取整)
     */
    private Double passRate;

    private static final long serialVersionUID = 1L;
}