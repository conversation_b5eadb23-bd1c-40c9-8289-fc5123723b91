package com.bmh.project.evaluation.lmt.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_lmt_project_record")
public class YcxLmtProjectRecord implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 儿童ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 评估项目ID
     */
    @Column(name = "lmt_project_id")
    private Integer lmtProjectId;

    /**
     * 项目ID
     */
    @Column(name = "project_id")
    private Integer projectId;

    /**
     * 目标达成状态(0未达成 1已达成)
     */
    @Column(name = "target_done")
    private Integer targetDone;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 是否有效(0无效 1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    private String updateUser;

    private static final long serialVersionUID = 1L;
}