package com.bmh.project.evaluation.inma.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.inma.mapper.YcxInmaResultMapper;
import com.bmh.project.evaluation.inma.model.YcxInmaResult;
import com.bmh.project.evaluation.inma.model.YcxInmaSelected;
import com.bmh.project.evaluation.inma.service.YcxInmaResultService;
import com.bmh.project.evaluation.inma.service.YcxInmaSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxInmaResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月21日 15:45:32
 */
@Service("10")
public class YcxInmaResultServiceImpl extends BaseServiceImpl<YcxInmaResult> implements YcxInmaResultService, YcxResultService<YcxInmaResult> {
    @Resource
    private YcxInmaResultMapper ycxInmaResultMapper;
    @Resource
    private YcxInmaSelectedService inmaSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxInmaResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxInmaResult.class);
        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge ());
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxInmaResultMapper.insert (result);

        List<YcxInmaSelected> selecteds = result.getSelecteds ();
        inmaSelectedService.saveSelecteds (selecteds, result.getRecordId ());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxInmaResult getResult (Integer recordId) {
        Example example = new Example (YcxInmaResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxInmaResult> list = this.selectByExample (example);
        return CollectionUtil.isNotEmpty (list)? list.get (0) : null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxInmaResult result = new YcxInmaResult ();
        result.setRecom (recom);
        Example example = new Example (YcxInmaResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxInmaResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}