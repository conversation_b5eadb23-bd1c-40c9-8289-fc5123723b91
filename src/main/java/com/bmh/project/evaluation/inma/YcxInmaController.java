package com.bmh.project.evaluation.inma;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.inma.model.YcxInma;
import com.bmh.project.evaluation.inma.service.YcxInmaService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * INMA神经运动检查20项
 *
 * <AUTHOR>
 * @since 2021/7/21 15:58
 */
@RestController
@RequestMapping("/inma")
public class YcxInmaController {

    @Resource
    private YcxInmaService inmaService;

    /**
     * 获取问题列表
     *
     * @return
     */
    @RequestMapping("/getList")
    public Result<?> getList(){
        List<YcxInma> list = inmaService.getList ();
        return ResultUtil.success(list);
    }
}
