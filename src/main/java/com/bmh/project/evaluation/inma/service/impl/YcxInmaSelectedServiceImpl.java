package com.bmh.project.evaluation.inma.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.inma.mapper.YcxInmaSelectedMapper;
import com.bmh.project.evaluation.inma.model.YcxInmaSelected;
import com.bmh.project.evaluation.inma.service.YcxInmaSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxInmaSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月21日 15:45:32
 */
@Service
public class YcxInmaSelectedServiceImpl extends BaseServiceImpl<YcxInmaSelected> implements YcxInmaSelectedService {
    @Resource
    private YcxInmaSelectedMapper ycxInmaSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxInmaSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxInmaSelectedMapper.insertList(selecteds);
    }
}