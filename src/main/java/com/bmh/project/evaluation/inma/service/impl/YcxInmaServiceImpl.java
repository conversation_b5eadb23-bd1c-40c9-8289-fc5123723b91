package com.bmh.project.evaluation.inma.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.inma.mapper.YcxInmaMapper;
import com.bmh.project.evaluation.inma.model.YcxInma;
import com.bmh.project.evaluation.inma.service.YcxInmaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxInmaService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月21日 15:45:32
 */
@Service
public class YcxInmaServiceImpl extends BaseServiceImpl<YcxInma> implements YcxInmaService {
    @Resource
    private YcxInmaMapper ycxInmaMapper;

    /**
     * 获取问题列表
     *
     * @return
     */
    @Override
    public List<YcxInma> getList () {
        return ycxInmaMapper.getList ();
    }
}