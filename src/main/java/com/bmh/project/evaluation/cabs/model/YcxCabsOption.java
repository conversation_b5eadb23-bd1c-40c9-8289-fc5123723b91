package com.bmh.project.evaluation.cabs.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_cabs_option")
public class YcxCabsOption implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "cabs_id")
    private Integer cabsId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    private static final long serialVersionUID = 1L;
}