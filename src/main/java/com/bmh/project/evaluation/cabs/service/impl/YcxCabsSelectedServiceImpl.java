package com.bmh.project.evaluation.cabs.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cabs.mapper.YcxCabsSelectedMapper;
import com.bmh.project.evaluation.cabs.model.YcxCabsSelected;
import com.bmh.project.evaluation.cabs.service.YcxCabsSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxCabsSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 18:55:44
 */
@Service
public class YcxCabsSelectedServiceImpl extends BaseServiceImpl<YcxCabsSelected> implements YcxCabsSelectedService {
    @Resource
    private YcxCabsSelectedMapper ycxCabsSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxCabsSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public List<YcxCabsSelected> getSelecteds(Integer recordId) {
        return this.ycxCabsSelectedMapper.getSelecteds(recordId);
    }



}