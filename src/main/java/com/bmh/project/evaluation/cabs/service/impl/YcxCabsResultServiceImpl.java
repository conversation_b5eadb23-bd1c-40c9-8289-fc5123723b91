package com.bmh.project.evaluation.cabs.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.cabs.mapper.YcxCabsResultMapper;
import com.bmh.project.evaluation.cabs.model.YcxCabsResult;
import com.bmh.project.evaluation.cabs.model.YcxCabsSelected;
import com.bmh.project.evaluation.cabs.service.YcxCabsResultService;
import com.bmh.project.evaluation.cabs.service.YcxCabsSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCabsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 18:55:44
 */
@Service("13")
public class YcxCabsResultServiceImpl extends BaseServiceImpl<YcxCabsResult> implements YcxCabsResultService, YcxResultService<YcxCabsResult> {
    @Resource
    private YcxCabsResultMapper ycxCabsResultMapper;
    @Resource
    private YcxCabsSelectedService cabsSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {

        YcxCabsResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxCabsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        List<YcxCabsSelected> selecteds = result.getSelecteds();
        cabsSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }

    @Override
    public YcxCabsResult getResult(Integer recordId) {
        Example example = new Example(YcxCabsResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxCabsResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            YcxCabsResult result = list.get(0);
            result.setSelecteds(cabsSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxCabsResult result = new YcxCabsResult ();
        result.setRecom (recom);
        Example example = new Example (YcxCabsResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxCabsResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}