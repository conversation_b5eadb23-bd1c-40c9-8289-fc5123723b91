package com.bmh.project.evaluation.cabs.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cabs.mapper.YcxCabsMapper;
import com.bmh.project.evaluation.cabs.model.YcxCabs;
import com.bmh.project.evaluation.cabs.service.YcxCabsService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxCabsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 18:55:44
 */
@Service
public class YcxCabsServiceImpl extends BaseServiceImpl<YcxCabs> implements YcxCabsService {
    @Resource
    private YcxCabsMapper ycxCabsMapper;

    @Override
    public List<YcxCabs> getList() {
        return this.ycxCabsMapper.getList();
    }
}