package com.bmh.project.evaluation.cabs.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.cabs.model.YcxCabs;
import com.bmh.project.evaluation.cabs.service.YcxCabsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * CABS 克氏孤独症行为量表
 */
@RestController
@RequestMapping("cabs")
public class CabsController {


    @Autowired
    public YcxCabsService cabsService;


    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("getList")
    public Result getList(){
        List<YcxCabs> list = cabsService.getList();
        return ResultUtil.success(list);
    }



}
