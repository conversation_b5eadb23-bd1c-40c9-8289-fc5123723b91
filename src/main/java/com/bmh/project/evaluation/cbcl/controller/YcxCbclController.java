package com.bmh.project.evaluation.cbcl.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.cbcl.service.YcxCbclService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Achenbanch儿童行为量表CBCL
 *
 * <AUTHOR>
 * @since 2021/7/13 17:45
 */
@RestController
@RequestMapping("/cbcl")
public class YcxCbclController {

    @Resource
    private YcxCbclService cbclService;

    /**
     * 根据类型获取问题
     * @param type 类型(1一般项目 2社会能力 3行为问题(VIII))
     * @return
     */
    @RequestMapping("/getCbclListByType")
    public Result<?> getCbclListByType(Integer type){
        return ResultUtil.success (cbclService.getCbclListByType (type));
    }
}
