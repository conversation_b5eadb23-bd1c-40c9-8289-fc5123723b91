package com.bmh.project.evaluation.cbcl.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cbcl.model.YcxCbcl;

import java.util.List;

/**
 * ycx_cbcl表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月13日 17:44:53
 */
public interface YcxCbclService extends BaseService<YcxCbcl> {

    /**
     * 根据类型获取问题
     * @param type 类型(1一般项目 2社会能力 3行为问题(VIII))
     * @return
     */
    List<YcxCbcl> getCbclListByType(Integer type);
}