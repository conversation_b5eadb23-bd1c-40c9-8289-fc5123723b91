package com.bmh.project.evaluation.cbcl.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_cbcl")
public class YcxCbcl implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1一般项目 2社会能力 3行为问题(VIII))
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 题目等级
     */
    @Column(name = "level")
    private Integer level;

    /**
     * 父问题ID
     */
    @Column(name = "parent_id")
    private Integer parentId;

    /**
     * 是否有子问题(0没有 1有)
     */
    @Column(name = "has_child")
    private Integer hasChild;

    /**
     * 状态(0无效 1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选项
     */
    @Transient
    private List<YcxCbclOption> options;


    /**
     * 子问题
     */
    @Transient
    private List<YcxCbcl> childCbcl;

    private static final long serialVersionUID = 1L;
}