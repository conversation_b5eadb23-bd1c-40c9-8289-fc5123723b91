package com.bmh.project.evaluation.cbcl.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cbcl.mapper.YcxCbclMapper;
import com.bmh.project.evaluation.cbcl.model.YcxCbcl;
import com.bmh.project.evaluation.cbcl.service.YcxCbclService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxCbclService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月13日 17:44:53
 */
@Service
public class YcxCbclServiceImpl extends BaseServiceImpl<YcxCbcl> implements YcxCbclService {
    @Resource
    private YcxCbclMapper ycxCbclMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1一般项目 2社会能力 3行为问题(VIII))
     * @return
     */
    @Override
    public List<YcxCbcl> getCbclListByType (Integer type) {
        List<YcxCbcl> list =  ycxCbclMapper.getCbclListByType (type, null, 1);
        list.forEach (cbcl -> {
            if(cbcl.getHasChild () == 1){
                cbcl.setChildCbcl (ycxCbclMapper.getCbclListByType (type, cbcl.getId (), 2));
            }
        });
        return list;
    }
}