package com.bmh.project.evaluation.cbcl.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_cbcl_option")
public class YcxCbclOption implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 父表ID
     */
    @Column(name = "cbcl_id")
    private Integer cbclId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}