package com.bmh.project.evaluation.cbcl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.cbcl.mapper.YcxCbclResultMapper;
import com.bmh.project.evaluation.cbcl.model.YcxCbclResult;
import com.bmh.project.evaluation.cbcl.model.YcxCbclSelected;
import com.bmh.project.evaluation.cbcl.service.YcxCbclResultService;
import com.bmh.project.evaluation.cbcl.service.YcxCbclSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * YcxCbclResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月13日 17:44:53
 */
@Service("20")
public class YcxCbclResultServiceImpl extends BaseServiceImpl<YcxCbclResult> implements YcxCbclResultService, YcxResultService<YcxCbclResult> {
    @Resource
    private YcxCbclResultMapper ycxCbclResultMapper;
    @Resource
    private YcxCbclSelectedService cbclSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxCbclResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxCbclResult.class);
        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge ());
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxCbclResultMapper.insert (result);

        List<YcxCbclSelected> selecteds = result.getSelecteds ();
        cbclSelectedService.saveSelecteds (selecteds, result.getRecordId ());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxCbclResult getResult (Integer recordId) {
        Example example = new Example (YcxCbclResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxCbclResult> list = this.selectByExample (example);
        if (CollectionUtil.isNotEmpty (list)) {
            YcxCbclResult result = list.get (0);
            List<YcxCbclSelected> selecteds = cbclSelectedService.getSelecteds (recordId);
            result.setSelectedResult ( selecteds.stream()
                            .sorted(Comparator.comparingInt(YcxCbclSelected::getCbclOrderNum))
                            .collect(Collectors.groupingBy(YcxCbclSelected::getCbclType)));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxCbclResult result = new YcxCbclResult ();
        result.setRecom (recom);
        Example example = new Example (YcxCbclResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxCbclResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}