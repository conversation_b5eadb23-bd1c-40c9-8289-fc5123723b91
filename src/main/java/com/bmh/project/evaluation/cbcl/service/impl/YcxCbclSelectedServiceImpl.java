package com.bmh.project.evaluation.cbcl.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cbcl.mapper.YcxCbclSelectedMapper;
import com.bmh.project.evaluation.cbcl.model.YcxCbclSelected;
import com.bmh.project.evaluation.cbcl.service.YcxCbclSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCbclSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月13日 17:44:53
 */
@Service
public class YcxCbclSelectedServiceImpl extends BaseServiceImpl<YcxCbclSelected> implements YcxCbclSelectedService {
    @Resource
    private YcxCbclSelectedMapper ycxCbclSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxCbclSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxCbclSelectedMapper.insertList(selecteds);
    }

    /**
     * 获取结果项
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public List<YcxCbclSelected> getSelecteds (Integer recordId) {
        return ycxCbclSelectedMapper.getSelecteds (recordId);
    }
}