package com.bmh.project.evaluation.cbcl.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cbcl.model.YcxCbclSelected;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;

import java.util.List;

/**
 * ycx_cbcl_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月13日 17:44:53
 */
public interface YcxCbclSelectedService extends BaseService<YcxCbclSelected> {

    /**
     * 保存选择项
     * @param selecteds 选择项
     * @param recordId 记录ID
     */
    void saveSelecteds(List<YcxCbclSelected> selecteds, Integer recordId);

    /**
     * 获取结果项
     * @param recordId 记录ID
     * @return
     */
    List<YcxCbclSelected> getSelecteds(Integer recordId);
}