package com.bmh.project.evaluation.cbcl.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_cbcl_selected")
public class YcxCbclSelected implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题ID
     */
    @Column(name = "cbcl_id")
    private Integer cbclId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 输入框1
     */
    @Column(name = "text1")
    private String text1;

    /**
     * 输入框2
     */
    @Column(name = "text2")
    private String text2;

    /**
     * 输入框3
     */
    @Column(name = "text3")
    private String text3;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 问题类型
     */
    @Transient
    private Integer cbclType;

    /**
     * 问题排序
     */
    @Transient
    private Integer cbclOrderNum;

    /**
     * 问题名称
     */
    @Transient
    private String cbclContent;

    /**
     * 选项名称
     */
    @Transient
    private String optionName;

    private static final long serialVersionUID = 1L;
}