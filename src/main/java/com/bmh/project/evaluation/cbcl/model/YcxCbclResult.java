package com.bmh.project.evaluation.cbcl.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Table(name = "ycx_cbcl_result")
public class YcxCbclResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄(周)
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 社会能力评测结果
     */
    @Column(name = "res_type2")
    private String resType2;

    /**
     * 行为问题评测结果
     */
    @Column(name = "res_type3")
    private String resType3;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选择项
     */
    @Transient
    private List<YcxCbclSelected> selecteds;

    /**
     * 选择结果项
     */
    @Transient
    private Map<Integer, List<YcxCbclSelected>> selectedResult;

    private static final long serialVersionUID = 1L;
}