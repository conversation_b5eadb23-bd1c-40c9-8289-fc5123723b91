package com.bmh.project.evaluation.cbcl.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.cbcl.model.YcxCbcl;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface YcxCbclMapper extends BaseMapper<YcxCbcl> {

    /**
     * 根据类型获取问题
     *
     * @param type     类型(1一般项目 2社会能力 3行为问题(VIII))
     * @param parentId 父ID
     * @param level    等级
     * @return
     */
    List<YcxCbcl> getCbclListByType (@Param ("type") Integer type, @Param ("parentId") Integer parentId, @Param ("level") Integer level);
}