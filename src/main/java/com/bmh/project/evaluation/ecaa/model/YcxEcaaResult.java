package com.bmh.project.evaluation.ecaa.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_ecaa_result")
public class YcxEcaaResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private String childrenAge;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 1健康得分
     */
    @Column(name = "res_type1")
    private Integer resType1;

    /**
     * 2语言得分
     */
    @Column(name = "res_type2")
    private Integer resType2;

    /**
     * 3社会得分
     */
    @Column(name = "res_type3")
    private Integer resType3;

    /**
     * 4科学得分
     */
    @Column(name = "res_type4")
    private Integer resType4;

    /**
     * 5艺术得分
     */
    @Column(name = "res_type5")
    private Integer resType5;

    /**
     * 6问题行为得分
     */
    @Column(name = "res_type6")
    private Integer resType6;

    /**
     * 总得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 测评结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人新姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;


    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_chart")
    private String resultChart;

    /**
     *  选中项
     */
    @Transient
    private List<YcxEcaaSelected> ecaaSelecteds;

    private static final long serialVersionUID = 1L;
}