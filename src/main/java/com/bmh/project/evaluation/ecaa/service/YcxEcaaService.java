package com.bmh.project.evaluation.ecaa.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.ecaa.model.YcxEcaa;

import java.util.List;
import java.util.Map;

/**
 * ycx_ecaa表对应的Service接口
 *
 * <AUTHOR>
 * @date 2025年01月10日 17:28:23
 */
public interface YcxEcaaService extends BaseService<YcxEcaa> {

    /**
     * 按照类型查看问题
     * @return
     */
    Map<String, List<YcxEcaa>> getEcaaByType(Integer type, Integer recordId);
}