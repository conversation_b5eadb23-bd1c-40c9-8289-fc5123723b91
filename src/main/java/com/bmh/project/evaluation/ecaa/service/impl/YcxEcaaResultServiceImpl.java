package com.bmh.project.evaluation.ecaa.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.ecaa.mapper.YcxEcaaResultMapper;
import com.bmh.project.evaluation.ecaa.model.YcxEcaaResult;
import com.bmh.project.evaluation.ecaa.model.YcxEcaaSelected;
import com.bmh.project.evaluation.ecaa.service.YcxEcaaResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.ecaa.service.YcxEcaaSelectedService;
import com.bmh.project.evaluation.gesell.model.YcxGesellResult;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxEcaaResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2025年01月10日 17:28:23
 */
@Service("42")
public class YcxEcaaResultServiceImpl extends BaseServiceImpl<YcxEcaaResult> implements YcxEcaaResultService, YcxResultService<YcxEcaaResult> {
    @Resource
    private YcxEcaaResultMapper ycxEcaaResultMapper;
    @Resource
    private YcxEcaaSelectedService ecaaSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxEcaaResult ecaaResult = JSONUtil.toBean (childrenRecord.getResultStr (), YcxEcaaResult.class);
        ecaaResult.setRecordId(childrenRecord.getId());
        ecaaResult.setChildrenAge(childrenRecord.getChildrenAge()+"");
        ecaaResult.setChildrenId(childrenRecord.getChildrenId());
        ecaaResult.setOrgId(childrenRecord.getOrgId());
        ecaaResult.setOperatorId(SecurityUtil.getUserId());
        ecaaResult.setOperatorName(SecurityUtil.getNickName());
        ecaaResult.setCreateTime(new Date());
        this.insert(ecaaResult);

        List<YcxEcaaSelected> ecaaSelecteds = ecaaResult.getEcaaSelecteds();
        ecaaSelectedService.saveSelecteds(ecaaSelecteds, ecaaResult.getRecordId());
    }

    @Override
    public YcxEcaaResult getResult(Integer recordId) {
        Example example = new Example(YcxEcaaResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxEcaaResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxEcaaResult result = new YcxEcaaResult ();
        result.setRecom (recom);
        Example example = new Example (YcxGesellResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxEcaaResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}