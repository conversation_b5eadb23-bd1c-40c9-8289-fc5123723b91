package com.bmh.project.evaluation.ecaa.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_ecaa")
public class YcxEcaa implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1健康，2语言，3社会，4科学，5艺术，6问题行为)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目标签
     */
    @Column(name = "tag")
    private String tag;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选项
     */
    @Transient
    private List<YcxEcaaOption> options;

    /**
     * 是否选中
     */
    @Transient
    private String checkOptionValue;

    private static final long serialVersionUID = 1L;
}