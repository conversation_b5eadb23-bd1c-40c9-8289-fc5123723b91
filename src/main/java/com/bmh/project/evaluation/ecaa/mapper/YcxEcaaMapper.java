package com.bmh.project.evaluation.ecaa.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.ecaa.model.YcxEcaa;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface YcxEcaaMapper extends BaseMapper<YcxEcaa> {

    /**
     * 按照类型查看问题
     * @return
     */
    List<YcxEcaa> getEcaaByType (@Param ("type") Integer type, @Param ("recordId") Integer recordId);
}