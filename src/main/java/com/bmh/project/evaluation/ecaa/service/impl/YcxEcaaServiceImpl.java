package com.bmh.project.evaluation.ecaa.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ecaa.mapper.YcxEcaaMapper;
import com.bmh.project.evaluation.ecaa.model.YcxEcaa;
import com.bmh.project.evaluation.ecaa.service.YcxEcaaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * YcxEcaaService对应的实现类
 *
 * <AUTHOR>
 * @date 2025年01月10日 17:28:23
 */
@Service
public class YcxEcaaServiceImpl extends BaseServiceImpl<YcxEcaa> implements YcxEcaaService {
    @Resource
    private YcxEcaaMapper ycxEcaaMapper;

    @Override
    public Map<String, List<YcxEcaa>> getEcaaByType (Integer type, Integer recordId) {
        List<YcxEcaa> ecaaList = ycxEcaaMapper.getEcaaByType (type, recordId);
        return ecaaList.stream ().collect (Collectors.groupingBy (YcxEcaa::getTag));
    }
}