package com.bmh.project.evaluation.ecaa.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_ecaa_option")
public class YcxEcaaOption implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "ecaa_id")
    private Integer ecaaId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}