package com.bmh.project.evaluation.ecaa.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.ecaa.model.YcxEcaa;
import com.bmh.project.evaluation.ecaa.service.YcxEcaaService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * ECAA(Early Childhood Admission Assessment) 幼儿入学评估
 */
@RestController
@RequestMapping("/ecaa")
public class YcxEcaaController {

    @Resource
    private YcxEcaaService ycxEcaaService;

    /**
     * 根据分类获取题库
     * @param type 类型(1健康，2语言，3社会，4科学，5艺术，6问题行为)
     * @return
     */
    @GetMapping ("/getList")
    public Result<Map<String, List<YcxEcaa>>> getEccaByType(Integer type, Integer recordId) {
        Map<String, List<YcxEcaa>> epqcList = ycxEcaaService.getEcaaByType(type, recordId);
        return ResultUtil.success(epqcList);
    }

}
