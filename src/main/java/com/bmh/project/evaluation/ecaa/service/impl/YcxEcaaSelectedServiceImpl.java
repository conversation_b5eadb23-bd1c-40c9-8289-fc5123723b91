package com.bmh.project.evaluation.ecaa.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ecaa.mapper.YcxEcaaSelectedMapper;
import com.bmh.project.evaluation.ecaa.model.YcxEcaaSelected;
import com.bmh.project.evaluation.ecaa.service.YcxEcaaSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxEcaaSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2025年01月10日 17:28:23
 */
@Service
public class YcxEcaaSelectedServiceImpl extends BaseServiceImpl<YcxEcaaSelected> implements YcxEcaaSelectedService {
    @Resource
    private YcxEcaaSelectedMapper ycxEcaaSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxEcaaSelected> ecaaSelecteds, Integer recordId) {
        ecaaSelecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(ecaaSelecteds);
    }
}