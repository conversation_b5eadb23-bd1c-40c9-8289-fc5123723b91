package com.bmh.project.evaluation.ecaa.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_ecaa_selected")
public class YcxEcaaSelected implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 测评记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 测评问题id
     */
    @Column(name = "ecaa_id")
    private Integer ecaaId;

    /**
     * 选中项id
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态(0.有效 1无效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}