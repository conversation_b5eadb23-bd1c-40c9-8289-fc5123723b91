package com.bmh.project.evaluation.epqc.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

@Data
@Table(name = "ycx_epqc_selected")
public class YcxEpqcSelected extends YcxResult implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录Id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题Id
     */
    @Column(name = "epqa_id")
    private Integer epqaId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}