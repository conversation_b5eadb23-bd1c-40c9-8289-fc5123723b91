package com.bmh.project.evaluation.epqc.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.epqc.model.YcxEpqcDict;

/**
 * ycx_epqc_dict表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月16日 19:11:45
 */
public interface YcxEpqcDictService extends BaseService<YcxEpqcDict> {
    /**
     * 根据类型，年龄，性别，分数，查询对应的标准分
     * @param epqcType （类型）
     * @param gender （年龄）
     * @param age （年龄）
     * @param score （分数）
     * @return
     */
    Integer getEpqcDict(Integer epqcType, Integer gender, Integer age, Integer score);
}