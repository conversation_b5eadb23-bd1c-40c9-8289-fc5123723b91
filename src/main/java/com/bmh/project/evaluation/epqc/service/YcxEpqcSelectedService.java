package com.bmh.project.evaluation.epqc.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import com.bmh.project.evaluation.epqc.model.YcxEpqcSelected;

import java.util.List;

/**
 * ycx_epqc_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月12日 19:15:33
 */
public interface YcxEpqcSelectedService extends BaseService<YcxEpqcSelected> {

    void saveSelected(List<YcxEpqcSelected> selecteds, Integer recordId);

    List<YcxEpqcSelected> getSelecteds(Integer recordId);
}