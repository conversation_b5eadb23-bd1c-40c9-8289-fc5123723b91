package com.bmh.project.evaluation.epqc.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.epqc.model.YcxEpqc;
import com.bmh.project.evaluation.epqc.model.YcxEpqcDict;
import com.bmh.project.evaluation.epqc.service.YcxEpqcDictService;
import com.bmh.project.evaluation.epqc.service.YcxEpqcService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * EPQC 艾森克人格个性检测
 */
@RestController
@RequestMapping("/epqc")
public class YcxEpqcController {

    @Resource
    private YcxEpqcService ycxEpqcService;
    @Resource
    private YcxEpqcDictService dictService;

    /**
     * 按照类型查看问题
     * @return
     */
    @RequestMapping("/getEpqcByType")
    public Result<List<YcxEpqc>> getEpqcByType() {
        List<YcxEpqc> epqcList = ycxEpqcService.getEpqcByType();
        return ResultUtil.success(epqcList);
    }

    /**
     * 根据类型，年龄，性别，分数，查询对应的标准分
     * @param dict
     * @return
     */
    @RequestMapping("/getEpqcScore")
    public Result<Integer> getEpqcDict(@RequestBody YcxEpqcDict dict){
        Integer score = dictService.getEpqcDict(dict.getEpqcType(),dict.getGender(),dict.getAge(),dict.getScore());
        return ResultUtil.success(score);
    }
}
