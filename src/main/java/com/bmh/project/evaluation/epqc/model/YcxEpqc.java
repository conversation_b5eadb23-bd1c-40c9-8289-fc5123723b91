package com.bmh.project.evaluation.epqc.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.evaluation.nyls.model.YcxNylsOption;
import lombok.Data;

@Data
@Table(name = "ycx_epqc")
public class YcxEpqc implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1P,2E,3N,4L)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目
     */
    @Column(name = "content")
    private String content;

    /**
     * 排序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxEpqcOption> options;
}