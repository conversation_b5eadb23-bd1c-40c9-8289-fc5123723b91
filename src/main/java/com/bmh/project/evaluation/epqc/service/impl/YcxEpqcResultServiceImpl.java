package com.bmh.project.evaluation.epqc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.epqc.mapper.YcxEpqcResultMapper;
import com.bmh.project.evaluation.epqc.model.YcxEpqcResult;
import com.bmh.project.evaluation.epqc.service.YcxEpqcResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.epqc.service.YcxEpqcSelectedService;
import com.bmh.project.evaluation.nyls.model.YcxNylsResult;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxEpqcResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 19:15:33
 */
@Service("33")
public class YcxEpqcResultServiceImpl extends BaseServiceImpl<YcxEpqcResult> implements YcxEpqcResultService, YcxResultService<YcxEpqcResult> {
    @Resource
    private YcxEpqcResultMapper ycxEpqcResultMapper;
    @Resource
    private YcxEpqcSelectedService epqcSelectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxEpqcResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxEpqcResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setCreatedTime(new Date());

        Example example = new Example(YcxEpqcResult.class);
        example.createCriteria().andEqualTo("recordId", childrenRecord.getId());
        List<YcxEpqcResult> list = this.selectByExample(example);
        if(CollectionUtil.isEmpty(list)) {
            ycxEpqcResultMapper.insert(result);
        }else{
            ycxEpqcResultMapper.updateByExampleSelective(result,example);
        }

        epqcSelectedService.saveSelected(result.getSelecteds(), result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxEpqcResult getResult(Integer recordId) {
        Example example = new Example(YcxEpqcResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxEpqcResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0) {
            YcxEpqcResult result = list.get(0);
            result.setSelecteds(epqcSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxEpqcResult result = new YcxEpqcResult();
        result.setRecom(recom);
        Example example = new Example(YcxNylsResult.class);
        Example.Criteria criteria = example.createCriteria();;
        criteria.andEqualTo("recordId",recordId);
        ycxEpqcResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}