package com.bmh.project.evaluation.epqc.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.epqc.mapper.YcxEpqcMapper;
import com.bmh.project.evaluation.epqc.model.YcxEpqc;
import com.bmh.project.evaluation.epqc.service.YcxEpqcService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxEpqcService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 19:15:33
 */
@Service
public class YcxEpqcServiceImpl extends BaseServiceImpl<YcxEpqc> implements YcxEpqcService {
    @Resource
    private YcxEpqcMapper ycxEpqcMapper;

    /**
     * 按照类型查看问题
     * @return
     */
    @Override
    public List<YcxEpqc> getEpqcByType() {
        return ycxEpqcMapper.getEpqcByType();
    }
}