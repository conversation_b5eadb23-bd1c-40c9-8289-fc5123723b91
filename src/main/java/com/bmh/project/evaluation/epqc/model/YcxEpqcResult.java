package com.bmh.project.evaluation.epqc.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import lombok.Data;

@Data
@Table(name = "ycx_epqc_result")
public class YcxEpqcResult extends YcxResult implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录Id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子Id
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 机构Id
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 内外向分(E)
     */
    @Column(name = "e_score")
    private Integer eScore;

    /**
     * 情绪稳定(N)
     */
    @Column(name = "n_score")
    private Integer nScore;

    /**
     * 精神质(P)
     */
    @Column(name = "p_score")
    private Integer pScore;

    /**
     * 掩饰(L)
     */
    @Column(name = "l_score")
    private Integer lScore;

    /**
     * 评测结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 操作人Id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 选中项
     */
    @Transient
    private List<YcxEpqcSelected> selecteds;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    private static final long serialVersionUID = 1L;
}