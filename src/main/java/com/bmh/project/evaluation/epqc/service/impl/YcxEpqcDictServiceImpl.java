package com.bmh.project.evaluation.epqc.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.epqc.mapper.YcxEpqcDictMapper;
import com.bmh.project.evaluation.epqc.model.YcxEpqcDict;
import com.bmh.project.evaluation.epqc.service.YcxEpqcDictService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * YcxEpqcDictService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月16日 19:11:45
 */
@Service
public class YcxEpqcDictServiceImpl extends BaseServiceImpl<YcxEpqcDict> implements YcxEpqcDictService {
    @Resource
    private YcxEpqcDictMapper ycxEpqcDictMapper;

    /**
     * 根据类型，年龄，性别，分数，查询对应的标准分
     * @param epqcType （类型）
     * @param gender （年龄）
     * @param age （年龄）
     * @param score （分数）
     * @return
     */
    @Override
    public Integer getEpqcDict(Integer epqcType, Integer gender, Integer age, Integer score) {
        return ycxEpqcDictMapper.getEpqcDict(epqcType,gender,age,score);
    }
}