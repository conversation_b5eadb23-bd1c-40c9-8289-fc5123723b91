package com.bmh.project.evaluation.epqc.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_epqc_dict")
public class YcxEpqcDict implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 对应的主表的类型
     */
    @Column(name = "epqc_type")
    private Integer epqcType;

    /**
     * 性别(1:男 2: 女)
     */
    @Column(name = "gender")
    private Integer gender;

    /**
     * 年龄
     */
    @Column(name = "age")
    private Integer age;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 对应的标准分
     */
    @Column(name = "standardscor")
    private Integer standardscor;

    private static final long serialVersionUID = 1L;
}