package com.bmh.project.evaluation.epqc.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.epqc.model.YcxEpqcDict;

public interface YcxEpqcDictMapper extends BaseMapper<YcxEpqcDict> {
    /**
     * 根据类型，年龄，性别，分数，查询对应的标准分
     * @param epqcType （类型）
     * @param gender （年龄）
     * @param age （年龄）
     * @param score （分数）
     * @return
     */
    Integer getEpqcDict(Integer epqcType, Integer gender, Integer age, Integer score);
}