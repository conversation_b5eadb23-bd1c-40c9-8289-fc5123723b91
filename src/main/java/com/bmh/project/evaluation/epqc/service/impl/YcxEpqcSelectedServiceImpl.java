package com.bmh.project.evaluation.epqc.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.epqc.mapper.YcxEpqcSelectedMapper;
import com.bmh.project.evaluation.epqc.model.YcxEpqcResult;
import com.bmh.project.evaluation.epqc.model.YcxEpqcSelected;
import com.bmh.project.evaluation.epqc.service.YcxEpqcSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxEpqcSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 19:15:33
 */
@Service
public class YcxEpqcSelectedServiceImpl extends BaseServiceImpl<YcxEpqcSelected> implements YcxEpqcSelectedService {
    @Resource
    private YcxEpqcSelectedMapper ycxEpqcSelectedMapper;

    @Override
    public void saveSelected(List<YcxEpqcSelected> selecteds, Integer recordId) {
        Example example = new Example(YcxEpqcResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        ycxEpqcSelectedMapper.deleteByExample(example);

        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public List<YcxEpqcSelected> getSelecteds(Integer recordId) {
        return ycxEpqcSelectedMapper.getSelecteds(recordId);
    }
}