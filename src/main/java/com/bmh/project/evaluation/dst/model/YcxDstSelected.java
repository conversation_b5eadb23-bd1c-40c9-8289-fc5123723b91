package com.bmh.project.evaluation.dst.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_dst_selected")
public class YcxDstSelected implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题项ID
     */
    @Column(name = "problem_id")
    private Integer problemId;

    /**
     * 选项(1通过，2失败，3不确定)
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}