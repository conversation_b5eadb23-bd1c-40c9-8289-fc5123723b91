package com.bmh.project.evaluation.dst.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.dst.mapper.YcxDstDictMapper;
import com.bmh.project.evaluation.dst.model.YcxDstDict;
import com.bmh.project.evaluation.dst.service.YcxDstDictService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * YcxDstDictService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 09:52:08
 */
@Service
public class YcxDstDictServiceImpl extends BaseServiceImpl<YcxDstDict> implements YcxDstDictService {
    @Resource
    private YcxDstDictMapper ycxDstDictMapper;

    @Override
    public String getScoreByDict(Integer type, Integer month, String origScore) {
        Example example = new Example(YcxDstDict.class);
        example.createCriteria().andEqualTo("type", type).andEqualTo("month", month).andEqualTo("origScore", origScore);
        List<YcxDstDict> list = this.selectByExample(example);
        if(CollectionUtil.isNotEmpty(list)){
            return list.get(0).getRealScore();
        }
        return null;
    }



}