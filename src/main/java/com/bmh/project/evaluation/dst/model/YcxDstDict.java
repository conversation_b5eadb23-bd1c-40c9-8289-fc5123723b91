package com.bmh.project.evaluation.dst.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_dst_dict")
public class YcxDstDict implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1MI，2DQ)
     */
    @Column(name = "type")
    private Integer type;

    @Column(name = "month")
    private Integer month;

    /**
     * 原始分数
     */
    @Column(name = "orig_score")
    private String origScore;

    /**
     * 真实分数
     */
    @Column(name = "real_score")
    private String realScore;

    private static final long serialVersionUID = 1L;
}