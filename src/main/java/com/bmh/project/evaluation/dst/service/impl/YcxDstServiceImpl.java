package com.bmh.project.evaluation.dst.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.dst.mapper.YcxDstMapper;
import com.bmh.project.evaluation.dst.model.YcxDst;
import com.bmh.project.evaluation.dst.service.YcxDstService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxDstService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月17日 10:09:32
 */
@Service
public class YcxDstServiceImpl extends BaseServiceImpl<YcxDst> implements YcxDstService {
    @Resource
    private YcxDstMapper ycxDstMapper;

    @Override
    public List<YcxDst> getDdstByType(Integer type) {
        Example example = new Example(YcxDst.class);
        example.orderBy("month").asc().orderBy ("isBlank").asc ();
        example.createCriteria().andEqualTo("type", type).andEqualTo("status",1);
        List<YcxDst> list = this.selectByExample(example);
        return list;
    }

}