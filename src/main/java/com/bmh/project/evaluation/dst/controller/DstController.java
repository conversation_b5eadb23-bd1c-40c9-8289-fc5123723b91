package com.bmh.project.evaluation.dst.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.dst.model.YcxDst;
import com.bmh.project.evaluation.dst.service.YcxDstDictService;
import com.bmh.project.evaluation.dst.service.YcxDstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.util.List;


/**
 * DST 0-6岁儿童发育筛选测验
 */
@RestController
@RequestMapping("dst")
public class DstController {

    @Resource
    private YcxDstService dstService;
    @Autowired
    private YcxDstDictService dstDictService;


    /**
     * 根据分类获取题库
     * @param type 类型(1运动，2社会适应，3智力)
     * @return
     */
    @RequestMapping("getDstByType")
    public Result getDstByType(Integer type){
        List<YcxDst> list = dstService.getDdstByType(type);
        return ResultUtil.success(list);
    }



    /**
     * 获取真实分数
     * @param type 类型(1MI,2DQ)
     * @param month 月份
     * @param origScore 原始分
     * @return
     */
    @RequestMapping("getScoreByDict")
    public Result getScoreByDict(Integer type, Integer month, String origScore){
        if(type==null||month==null|| StringUtil.isEmpty(origScore)){
            return ResultUtil.error("参数错误");
        }
        String realScore = dstDictService.getScoreByDict(type, month, origScore);
        return ResultUtil.success(realScore);
    }


}
