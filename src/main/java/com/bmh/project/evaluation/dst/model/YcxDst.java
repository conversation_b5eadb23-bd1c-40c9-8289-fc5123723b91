package com.bmh.project.evaluation.dst.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_dst")
public class YcxDst implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1运动，2社会适应，3智力)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 操作说明
     */
    @Column(name = "tips")
    private String tips;

    /**
     * 评分标准
     */
    @Column(name = "grading")
    private String grading;

    /**
     * 月
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 是否空节点(0不是，1是）
     */
    @Column(name = "is_blank")
    private Integer isBlank;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}