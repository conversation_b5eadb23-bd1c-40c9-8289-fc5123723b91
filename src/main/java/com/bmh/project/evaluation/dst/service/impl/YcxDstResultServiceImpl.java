package com.bmh.project.evaluation.dst.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.dst.mapper.YcxDstResultMapper;
import com.bmh.project.evaluation.dst.model.YcxDstResult;
import com.bmh.project.evaluation.dst.model.YcxDstSelected;
import com.bmh.project.evaluation.dst.service.YcxDstResultService;
import com.bmh.project.evaluation.dst.service.YcxDstSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxDstResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月17日 10:09:32
 */
@Service("8")
public class YcxDstResultServiceImpl extends BaseServiceImpl<YcxDstResult> implements YcxDstResultService, YcxResultService<YcxDstResult> {
    @Resource
    private YcxDstResultMapper ycxDstResultMapper;
    @Resource
    private YcxDstSelectedService dstSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxDstResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxDstResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        List<YcxDstSelected> selecteds = result.getDstSelecteds();
        dstSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }


    @Override
    public YcxDstResult getResult(Integer recordId) {
        Example example = new Example(YcxDstResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxDstResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxDstResult result = new YcxDstResult ();
        result.setRecom (recom);
        Example example = new Example (YcxDstResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxDstResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}