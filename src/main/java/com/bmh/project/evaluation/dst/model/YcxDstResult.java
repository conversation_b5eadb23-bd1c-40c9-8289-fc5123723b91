package com.bmh.project.evaluation.dst.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_dst_result")
public class YcxDstResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄(周)
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 运动得分(1,2,3)对应状态的数量
     */
    @Column(name = "res_type1")
    private String resType1;

    /**
     * 社会适应(1,2,3)对应状态的数量
     */
    @Column(name = "res_type2")
    private String resType2;

    /**
     * 智力(1,2,3)对应状态的数量
     */
    @Column(name = "res_type3")
    private String resType3;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 智力原始分
     */
    @Column(name = "res_mi_score")
    private String resMiScore;

    /**
     * 智力指数
     */
    @Column(name = "res_mi")
    private String resMi;

    /**
     * 发育商原始分
     */
    @Column(name = "res_dq_score")
    private String resDqScore;

    /**
     * 发育商
     */
    @Column(name = "res_dq")
    private String resDq;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;



    /**
     * dst 选中项
     */
    @Transient
    private List<YcxDstSelected> dstSelecteds;

}