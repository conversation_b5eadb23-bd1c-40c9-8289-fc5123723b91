package com.bmh.project.evaluation.dst.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ddst.model.YcxDdstSelected;
import com.bmh.project.evaluation.dst.mapper.YcxDstSelectedMapper;
import com.bmh.project.evaluation.dst.model.YcxDstSelected;
import com.bmh.project.evaluation.dst.service.YcxDstSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxDstSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月17日 10:09:32
 */
@Service
public class YcxDstSelectedServiceImpl extends BaseServiceImpl<YcxDstSelected> implements YcxDstSelectedService {
    @Resource
    private YcxDstSelectedMapper ycxDstSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxDstSelected> dstSelecteds, Integer recordId) {
        dstSelecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(dstSelecteds);
    }




}