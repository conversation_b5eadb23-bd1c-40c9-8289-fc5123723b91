package com.bmh.project.evaluation.hamd.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.hamd.model.YcxHamd;
import com.bmh.project.evaluation.hamd.service.YcxHamdService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 *  HAMD 汉密尔顿抑郁量表
 */
@RestController
@RequestMapping("/hamd")
public class YcxHamdController {

    @Resource
    private YcxHamdService ycxHamdService;

    /**
     * 获取所有问题
     * @return
     */
    @RequestMapping("/getHamd")
    public Result<List<YcxHamd>> getHamd(){
        List<YcxHamd> list = ycxHamdService.getHamd();
        return ResultUtil.success(list);
    }
}
