package com.bmh.project.evaluation.hamd.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.ame.doc.YcxAmeSelectedDoc;
import com.bmh.project.evaluation.hamd.doc.YcxHamdDoc;
import com.bmh.project.evaluation.hamd.doc.YcxHamdSelectedDoc;
import com.bmh.project.evaluation.hamd.mapper.YcxHamdResultMapper;
import com.bmh.project.evaluation.hamd.model.YcxHamdResult;
import com.bmh.project.evaluation.hamd.service.YcxHamdResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.hamd.service.YcxHamdSelectedService;
import com.bmh.project.evaluation.iat.doc.YcxIatDoc;
import com.bmh.project.evaluation.iat.doc.YcxIatSelectedDoc;
import com.bmh.project.record.model.YcxChildrenRecord;
//import com.bmh.project.record.service.ChildrenRecordDocService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * YcxHamdResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 10:12:20
 */
@Service("36")
public class YcxHamdResultServiceImpl extends BaseServiceImpl<YcxHamdResult> implements YcxHamdResultService, YcxResultService<YcxHamdResult> {
    @Resource
    private YcxHamdResultMapper ycxHamdResultMapper;
    @Resource
    private YcxHamdSelectedService selectedService;
//    @Resource
//    private ChildrenRecordDocService recordDocService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxHamdResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxHamdResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChaildrenAge(childrenRecord.getChildrenAge());
        result.setChaildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setCreateTime(new Date());
        ycxHamdResultMapper.insert(result);

        selectedService.saveSelected(result.getSelecteds(),result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxHamdResult getResult(Integer recordId) {
        Example example = new Example(YcxHamdResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxHamdResult> list = selectByExample(example);
        if (list != null && list.size() > 0){
            YcxHamdResult result = list.get(0);
            result.setSelecteds(selectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxHamdResult result = new YcxHamdResult();
        result.setRecom(recom);
        Example example = new Example(YcxHamdResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxHamdResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {
//        List<YcxHamdSelectedDoc> selectedList = recordDocService.getSelectedList(evaluatingId, projectId, YcxHamdSelectedDoc.class);
//        if (CollectionUtil.isNotEmpty(selectedList)) {
//            Map<Integer, YcxHamdSelectedDoc> map = selectedList.stream()
//                    .collect(Collectors.toMap(YcxHamdSelectedDoc::getSubjectId, e -> e, (v1, v2) -> v1));
//            for (Object o : list) {
//                YcxHamdDoc doc = (YcxHamdDoc) o;
//                YcxHamdSelectedDoc selected = map.get(doc.getId());
//                if (selected != null) {
//                    doc.setAnswer(selected.getAnswer());
//                }
//            }
//        }
    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {
//        List<YcxHamdSelectedDoc> selectedList = recordDocService.getSelectedList(
//                record.getEvaluatingId(), record.getProjectId(), YcxIatSelectedDoc.class);
//        if (CollectionUtil.isNotEmpty(selectedList)) {
//            Integer result = selectedList.stream().mapToInt(YcxHamdSelectedDoc::getScore).sum();
//            if(result==null){
//                result=-1;
//            }
//            record.setResult(result.toString());
//            record.setResultStr(getResultStr(result));
//        }
    }
    private String getResultStr(Integer result) {
        if (result >= 0 && result < 9) {
            return StrUtil.format("{}分，正常",result);
        } else if (result >= 9 && result < 20) {
            return  StrUtil.format("{}分，可能有抑郁",result);
        } else if (result >= 20 && result < 35) {
            return  StrUtil.format("{}分，肯定有抑郁",result);
        } else if (result >= 35) {
            return  StrUtil.format("{}分，严重抑郁症",result);
        }
        return "分数异常";
    }
}