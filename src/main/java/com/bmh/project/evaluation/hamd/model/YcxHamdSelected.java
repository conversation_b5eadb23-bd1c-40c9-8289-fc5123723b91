package com.bmh.project.evaluation.hamd.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_hamd_selected")
public class YcxHamdSelected implements Serializable {
    /**
     * 主键id
     */
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题id
     */
    @Column(name = "hamd_id")
    private Integer hamdId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态(0.有效 1无效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}