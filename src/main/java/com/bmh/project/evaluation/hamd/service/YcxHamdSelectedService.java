package com.bmh.project.evaluation.hamd.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.hamd.model.YcxHamdSelected;

import java.util.List;

/**
 * ycx_hamd_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 10:12:20
 */
public interface YcxHamdSelectedService extends BaseService<YcxHamdSelected> {

    void saveSelected(List<YcxHamdSelected> selecteds, Integer recordId);

    List<YcxHamdSelected> getSelecteds(Integer recordId);
}