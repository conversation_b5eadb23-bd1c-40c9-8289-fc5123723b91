package com.bmh.project.evaluation.hamd.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

@Data
@Table(name = "ycx_hamd_result")
public class YcxHamdResult extends YcxResult implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "chaildren_age")
    private BigDecimal chaildrenAge;

    /**
     * 孩子id
     */
    @Column(name = "chaildren_id")
    private Integer chaildrenId;

    /**
     * 机构id
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 评测结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人新姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxHamdSelected> selecteds;
}