package com.bmh.project.evaluation.hamd.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.hamd.mapper.YcxHamdSelectedMapper;
import com.bmh.project.evaluation.hamd.model.YcxHamdSelected;
import com.bmh.project.evaluation.hamd.service.YcxHamdSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxHamdSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 10:12:20
 */
@Service
public class YcxHamdSelectedServiceImpl extends BaseServiceImpl<YcxHamdSelected> implements YcxHamdSelectedService {
    @Resource
    private YcxHamdSelectedMapper ycxHamdSelectedMapper;

    @Override
    public void saveSelected(List<YcxHamdSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreatedTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        insertList(selecteds);
    }

    @Override
    public List<YcxHamdSelected> getSelecteds(Integer recordId) {
        return ycxHamdSelectedMapper.getSelecteds(recordId);
    }
}