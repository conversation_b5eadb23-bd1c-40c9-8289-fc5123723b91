package com.bmh.project.evaluation.hamd.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.common.service.BaseProjectService;
import com.bmh.project.evaluation.ame.service.YcxAmeOptionService;
import com.bmh.project.evaluation.hamd.doc.YcxHamdDoc;
import com.bmh.project.evaluation.hamd.doc.YcxHamdOptionDoc;
import com.bmh.project.evaluation.hamd.mapper.YcxHamdMapper;
import com.bmh.project.evaluation.hamd.model.YcxHamd;
import com.bmh.project.evaluation.hamd.model.YcxHamdOption;
import com.bmh.project.evaluation.hamd.service.YcxHamdOptionService;
import com.bmh.project.evaluation.hamd.service.YcxHamdService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.iat.doc.YcxIatDoc;
import com.bmh.project.evaluation.iat.doc.YcxIatOptionDoc;
import com.bmh.project.evaluation.iat.model.YcxIat;
import com.bmh.project.evaluation.iat.model.YcxIatOption;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * YcxHamdService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 10:12:20
 */
@Service("Pro_36")
public class YcxHamdServiceImpl extends BaseServiceImpl<YcxHamd> implements YcxHamdService, BaseProjectService {
    @Resource
    private YcxHamdMapper ycxHamdMapper;
    @Resource
    private YcxHamdOptionService optionService;

    /**
     * 获取所有问题
     * @return
     */
    @Override
    public List<YcxHamd> getHamd() {
        return ycxHamdMapper.getHamd();
    }

    @Override
    public List getProjectList() {
        List<YcxHamdOption> optionList = optionService.selectAll();
        Map<Integer, List<YcxHamdOption>> map = optionList.stream().collect(Collectors.groupingBy(YcxHamdOption::getHamdId));
        Example example = new Example(YcxIat.class);
        example.createCriteria().andEqualTo("status",1);
        example.orderBy("orderNum").asc();
        List<YcxHamd> hamdList = this.selectByExample(example);
        List<YcxHamdDoc> list = new ArrayList<>();
        for (YcxHamd hamd : hamdList) {
            YcxHamdDoc doc = BeanUtil.copyProperties(hamd,YcxHamdDoc.class);
            List<YcxHamdOption> options = map.get(hamd.getId());
            options.sort(Comparator.comparingInt(YcxHamdOption::getScore));
            List<YcxHamdOptionDoc> optionDocs = BeanUtil.copyToList(options,YcxHamdOptionDoc.class);
            doc.setOptions(optionDocs);
            list.add(doc);
        }
        return list;
    }
}