package com.bmh.project.evaluation.chat.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.chat.model.YcxChatSelected;

import java.util.List;
import java.util.Map;

/**
 * ycx_chat_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年06月22日 15:37:21
 */
public interface YcxChatSelectedService extends BaseService<YcxChatSelected> {

    void saveSelecteds(List<YcxChatSelected> selecteds, Integer recordId);

    Map<String, Object> getResultSelected(Integer recordId);

}