package com.bmh.project.evaluation.chat.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.chat.model.YcxChat;
import com.bmh.project.evaluation.chat.service.YcxChatService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * chat-23 孤独症早期筛查量表(一级筛查)
 */
@RestController
@RequestMapping("chat")
public class ChatController {

    @Resource
    private YcxChatService chatService;

    /**
     * 根据类型获取问题
     * @param type 类型(1家庭一般情况，2行为及沟通能力，3医生观察项目)
     * @return
     */
    @RequestMapping("getChatListByType")
    public Result getChatListByType(Integer type){
        List<YcxChat> list = chatService.getChatListByType(type);
        return ResultUtil.success(list);
    }


}
