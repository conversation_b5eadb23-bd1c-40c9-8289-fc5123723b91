package com.bmh.project.evaluation.chat.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_chat")
public class YcxChat implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1家庭一般情况，2行为及沟通能力，3医生观察项目)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 是否核心项目(0否，1是)
     */
    @Column(name = "is_core")
    private Integer isCore;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    @Column(name = "status")
    private Integer status;

    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;


    /**
     * 选项
     */
    @Transient
    private List<YcxChatOption> options;


}