package com.bmh.project.evaluation.chat.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.chat.mapper.YcxChatMapper;
import com.bmh.project.evaluation.chat.model.YcxChat;
import com.bmh.project.evaluation.chat.service.YcxChatService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxChatService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 15:37:21
 */
@Service
public class YcxChatServiceImpl extends BaseServiceImpl<YcxChat> implements YcxChatService {
    @Resource
    private YcxChatMapper ycxChatMapper;

    @Override
    public List<YcxChat> getChatListByType(Integer type) {
        return this.ycxChatMapper.getChatListByType(type);
    }
}