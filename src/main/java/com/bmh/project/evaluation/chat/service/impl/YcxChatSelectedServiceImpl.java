package com.bmh.project.evaluation.chat.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.chat.mapper.YcxChatSelectedMapper;
import com.bmh.project.evaluation.chat.model.YcxChatSelected;
import com.bmh.project.evaluation.chat.service.YcxChatSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * YcxChatSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 15:37:21
 */
@Service
public class YcxChatSelectedServiceImpl extends BaseServiceImpl<YcxChatSelected> implements YcxChatSelectedService {
    @Resource
    private YcxChatSelectedMapper ycxChatSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxChatSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            if(op!=null) {
                op.setRecordId(recordId);
                op.setCreateTime(new Date());
                op.setStatus(1);
                op.setUpdateTime(new Date());
            }
        });
        this.insertList(selecteds);
    }

    @Override
    public Map<String, Object> getResultSelected(Integer recordId) {
        Map<String, Object> resultSelected = new HashMap<>();
        List<YcxChatSelected> selecteds1 = this.ycxChatSelectedMapper.selectSelectedByType(1,recordId);
        List<YcxChatSelected> selecteds2 = this.ycxChatSelectedMapper.selectSelectedByType(2,recordId);
        List<YcxChatSelected> selecteds3 = this.ycxChatSelectedMapper.selectSelectedByType(3,recordId);
        resultSelected.put("selecteds1", selecteds1);
        resultSelected.put("selecteds2", selecteds2);
        resultSelected.put("selecteds3", selecteds3);
        return resultSelected;
    }


}