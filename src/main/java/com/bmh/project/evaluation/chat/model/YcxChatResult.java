package com.bmh.project.evaluation.chat.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Table(name = "ycx_chat_result")
public class YcxChatResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private String childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 行为及沟通能力结果
     */
    @Column(name = "result2")
    private String result2;

    /**
     * 医生观察项目结果
     */
    @Column(name = "result3")
    private String result3;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;


    /**
     * 选中项
     */
    @Transient
    private List<YcxChatSelected> selecteds;


    /**
     * 查询结果单
     */
    @Transient
    private Map<String, Object> resultSelect;


}