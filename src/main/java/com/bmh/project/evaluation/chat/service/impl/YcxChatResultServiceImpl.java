package com.bmh.project.evaluation.chat.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.chat.mapper.YcxChatResultMapper;
import com.bmh.project.evaluation.chat.model.YcxChatResult;
import com.bmh.project.evaluation.chat.model.YcxChatSelected;
import com.bmh.project.evaluation.chat.service.YcxChatResultService;
import com.bmh.project.evaluation.chat.service.YcxChatSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxChatResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月22日 15:37:21
 */
@Service("12")
public class YcxChatResultServiceImpl extends BaseServiceImpl<YcxChatResult> implements YcxChatResultService, YcxResultService<YcxChatResult> {

    @Resource
    private YcxChatResultMapper ycxChatResultMapper;
    @Resource
    private YcxChatSelectedService ycxChatSelectedService;


    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxChatResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxChatResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        List<YcxChatSelected> selecteds = result.getSelecteds();

        ycxChatSelectedService.saveSelecteds(selecteds, result.getRecordId());

    }

    @Override
    public YcxChatResult getResult(Integer recordId) {
        Example example = new Example(YcxChatResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxChatResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            YcxChatResult ycxChatResult = list.get(0);
            ycxChatResult.setResultSelect(ycxChatSelectedService.getResultSelected(recordId));
            return ycxChatResult;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxChatResult result = new YcxChatResult ();
        result.setRecom (recom);
        Example example = new Example (YcxChatResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxChatResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}