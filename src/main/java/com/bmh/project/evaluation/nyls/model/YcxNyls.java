package com.bmh.project.evaluation.nyls.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.evaluation.iat.model.YcxIatOption;
import lombok.Data;

@Data
@Table(name = "ycx_nyls")
public class YcxNyls implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1水平活动，2养成型，3趋避性，4适应度，5反应强度，6情绪本质，7坚持度，8注意分散度，9反应阈)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 排序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态
     */
    @Column(name = "status")

    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxNylsOption> options;
}