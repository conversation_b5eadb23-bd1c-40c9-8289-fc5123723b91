package com.bmh.project.evaluation.nyls.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.nyls.mapper.YcxNylsResultMapper;
import com.bmh.project.evaluation.nyls.model.YcxNylsResult;
import com.bmh.project.evaluation.nyls.service.YcxNylsResultService;

import javax.annotation.Resource;

import com.bmh.project.evaluation.nyls.service.YcxNylsSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxNylsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 16:55:53
 */
@Service("31")
public class YcxNylsResultServiceImpl extends BaseServiceImpl<YcxNylsResult> implements YcxNylsResultService, YcxResultService<YcxNylsResult> {
    @Resource
    private YcxNylsResultMapper ycxNylsResultMapper;
    @Resource
    private YcxNylsSelectedService nylsSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxNylsResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxNylsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setCreateTime(new Date());
        ycxNylsResultMapper.insert(result);

        nylsSelectedService.saveSelected(result.getSelecteds(), result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxNylsResult getResult(Integer recordId) {
        Example example = new Example(YcxNylsResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxNylsResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0) {
            YcxNylsResult result = list.get(0);
            result.setSelecteds(nylsSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxNylsResult result = new YcxNylsResult();
        result.setRecom(recom);
        Example example = new Example(YcxNylsResult.class);
        Example.Criteria criteria = example.createCriteria();;
        criteria.andEqualTo("recordId",recordId);
        ycxNylsResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}