package com.bmh.project.evaluation.nyls.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.nyls.model.YcxNyls;
import com.bmh.project.evaluation.nyls.model.YcxNylsDict;
import com.bmh.project.evaluation.nyls.service.YcxNylsDictService;
import com.bmh.project.evaluation.nyls.service.YcxNylsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * NYLS 3-7岁儿童气质问卷
 */
@RestController
@RequestMapping("/nyls")
public class YcxNylsController {

    @Resource
    private YcxNylsService nylsService;
    @Resource
    private YcxNylsDictService nylsDictService;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1水平活动，2养成型，3趋避性，4适应度，5反应强度，6情绪本质，7坚持度，8注意分散度，9反应阈)
     * @return
     */
    @RequestMapping("/getNylsByType")
    public Result<List<YcxNyls>> getNylsByType(Integer type) {
        List<YcxNyls> nylsList = nylsService.getNylsByType(type);
        return ResultUtil.success(nylsList);
    }

    /**
     * 获取气质纬度得分
     *
     * @param sex (1:男 2: 女)
     * @param age
     * @return
     */
    @RequestMapping("/getNylsDict")
    public Result<List<YcxNylsDict>> getNylsDict(Integer sex, Integer age) {
        List<YcxNylsDict> list = nylsDictService.getNylsDict(sex, age);
        return ResultUtil.success(list);
    }
}
