package com.bmh.project.evaluation.nyls.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_nyls_dict")
public class YcxNylsDict implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 维度/标题类型
     */
    @Column(name = "nyls_type")
    private Integer nylsType;

    /**
     * 性别(1:男 2:女)
     */
    @Column(name = "sex")
    private Integer sex;

    /**
     * 年龄
     */
    @Column(name = "age")
    private Integer age;

    /**
     * 平均分
     */
    @Column(name = "avg")
    private Double avg;

    /**
     * 平均分差值
     */
    @Column(name = "differ")
    private Double differ;

    private static final long serialVersionUID = 1L;
}