package com.bmh.project.evaluation.nyls.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nyls.mapper.YcxNylsDictMapper;
import com.bmh.project.evaluation.nyls.model.YcxNylsDict;
import com.bmh.project.evaluation.nyls.service.YcxNylsDictService;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * YcxNylsDictService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月13日 16:02:03
 */
@Service
public class YcxNylsDictServiceImpl extends BaseServiceImpl<YcxNylsDict> implements YcxNylsDictService {
    @Resource
    private YcxNylsDictMapper ycxNylsDictMapper;

    /**
     * 获取气质纬度得分
     *
     * @param sex (1:男，2:女)
     * @param age
     * @return
     */
    @Override
    public List<YcxNylsDict> getNylsDict(Integer sex, Integer age) {
        List<YcxNylsDict> list = ycxNylsDictMapper.getNylsDict(sex, age);
        return list;
    }
}