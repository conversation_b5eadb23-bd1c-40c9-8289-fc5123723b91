package com.bmh.project.evaluation.nyls.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nyls.mapper.YcxNylsSelectedMapper;
import com.bmh.project.evaluation.nyls.model.YcxNylsSelected;
import com.bmh.project.evaluation.nyls.service.YcxNylsSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxNylsSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 16:55:53
 */
@Service
public class YcxNylsSelectedServiceImpl extends BaseServiceImpl<YcxNylsSelected> implements YcxNylsSelectedService {
    @Resource
    private YcxNylsSelectedMapper ycxNylsSelectedMapper;

    @Override
    public void saveSelected(List<YcxNylsSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public List<YcxNylsSelected> getSelecteds(Integer recordId) {
        return ycxNylsSelectedMapper.getSelecteds(recordId);
    }
}