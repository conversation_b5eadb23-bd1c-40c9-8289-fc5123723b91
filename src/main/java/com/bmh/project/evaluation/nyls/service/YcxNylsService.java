package com.bmh.project.evaluation.nyls.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.nyls.model.YcxNyls;

import java.util.List;

/**
 * ycx_nyls表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月12日 16:55:53
 */
public interface YcxNylsService extends BaseService<YcxNyls> {

    /**
     * 根据类型获取问题(1水平活动，2养成型，3趋避性，4适应度，5反应强度，6情绪本质，7坚持度，8注意分散度，9反应阈)
     * @param type
     * @return
     */
    List<YcxNyls> getNylsByType(Integer type);

}