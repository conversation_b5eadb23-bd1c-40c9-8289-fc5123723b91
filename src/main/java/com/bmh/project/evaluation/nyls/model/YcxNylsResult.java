package com.bmh.project.evaluation.nyls.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import lombok.Data;

@Data
@Table(name = "ycx_nyls_result")
public class YcxNylsResult extends YcxResult implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录Id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子Id
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位Id
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 评测结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 气质问卷调查数据
     */
    @Column(name = "major")
    private String major;
    /**
     * 操作人Id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选中项
     */
    @Transient
    private List<YcxNylsSelected> selecteds;

    private static final long serialVersionUID = 1L;
}