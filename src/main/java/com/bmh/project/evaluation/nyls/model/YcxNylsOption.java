package com.bmh.project.evaluation.nyls.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_nyls_option")
public class YcxNylsOption implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "nyls_id")
    private Integer nylsId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}