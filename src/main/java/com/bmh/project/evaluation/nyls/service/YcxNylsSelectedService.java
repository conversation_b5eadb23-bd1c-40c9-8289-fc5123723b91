package com.bmh.project.evaluation.nyls.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import com.bmh.project.evaluation.nyls.model.YcxNylsSelected;

import java.util.List;

/**
 * ycx_nyls_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月12日 16:55:53
 */
public interface YcxNylsSelectedService extends BaseService<YcxNylsSelected> {

    void saveSelected(List<YcxNylsSelected> selecteds, Integer recordId);

    List<YcxNylsSelected> getSelecteds(Integer recordId);
}