package com.bmh.project.evaluation.nyls.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.nyls.model.YcxNylsDict;

import java.util.List;

/**
 * ycx_nyls_dict表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月13日 16:02:03
 */
public interface YcxNylsDictService extends BaseService<YcxNylsDict> {
    /**
     * 获取气质纬度得分
     *
     * @param sex (1:男，2:女)
     * @param age
     * @return
     */
    List<YcxNylsDict> getNylsDict(Integer sex, Integer age);
}