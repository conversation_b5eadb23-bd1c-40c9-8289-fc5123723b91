package com.bmh.project.evaluation.nyls.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nyls.mapper.YcxNylsMapper;
import com.bmh.project.evaluation.nyls.model.YcxNyls;
import com.bmh.project.evaluation.nyls.service.YcxNylsService;
import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxNylsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月12日 16:55:53
 */
@Service
public class YcxNylsServiceImpl extends BaseServiceImpl<YcxNyls> implements YcxNylsService {
    @Resource
    private YcxNylsMapper ycxNylsMapper;

    /**
     * 根据类型获取问题(1水平活动，2养成型，3趋避性，4适应度，5反应强度，6情绪本质，7坚持度，8注意分散度，9反应阈)
     * @param type
     * @return
     */
    @Override
    public List<YcxNyls> getNylsByType(Integer type) {
        return ycxNylsMapper.getNylsByType(type);
    }

}