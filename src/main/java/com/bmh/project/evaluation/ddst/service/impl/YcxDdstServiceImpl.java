package com.bmh.project.evaluation.ddst.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ddst.mapper.YcxDdstMapper;
import com.bmh.project.evaluation.ddst.model.YcxDdst;
import com.bmh.project.evaluation.ddst.service.YcxDdstService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * YcxDdstService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月17日 10:00:12
 */
@Service
public class YcxDdstServiceImpl extends BaseServiceImpl<YcxDdst> implements YcxDdstService {
    @Resource
    private YcxDdstMapper ycxDdstMapper;

    @Override
    public List<YcxDdst> getDdstByType(Integer type) {
        Example example = new Example(YcxDdst.class);
        example.orderBy("minMonth").asc();
        example.createCriteria().andEqualTo("type", type).andEqualTo("status", 1);
        List<YcxDdst> list = this.selectByExample(example);
        return list;
    }

}