package com.bmh.project.evaluation.ddst.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.ddst.mapper.YcxDdstResultMapper;
import com.bmh.project.evaluation.ddst.model.YcxDdstResult;
import com.bmh.project.evaluation.ddst.model.YcxDdstSelected;
import com.bmh.project.evaluation.ddst.service.YcxDdstResultService;
import com.bmh.project.evaluation.ddst.service.YcxDdstSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxDdstResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月17日 10:00:12
 */
@Service("5")
public class YcxDdstResultServiceImpl extends BaseServiceImpl<YcxDdstResult> implements YcxDdstResultService, YcxResultService<YcxDdstResult> {
    @Resource
    private YcxDdstResultMapper ycxDdstResultMapper;
    @Resource
    private YcxDdstSelectedService ddstSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {

        YcxDdstResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxDdstResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        List<YcxDdstSelected> selecteds = result.getDdstSelecteds();
        ddstSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }


    @Override
    public YcxDdstResult getResult(Integer recordId) {
        Example example = new Example(YcxDdstResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxDdstResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxDdstResult result = new YcxDdstResult ();
        result.setRecom (recom);
        Example example = new Example (YcxDdstResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxDdstResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}