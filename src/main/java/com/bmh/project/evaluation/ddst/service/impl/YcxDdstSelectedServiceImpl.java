package com.bmh.project.evaluation.ddst.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ddst.mapper.YcxDdstSelectedMapper;
import com.bmh.project.evaluation.ddst.model.YcxDdstSelected;
import com.bmh.project.evaluation.ddst.service.YcxDdstSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxDdstSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月17日 10:00:12
 */
@Service
public class YcxDdstSelectedServiceImpl extends BaseServiceImpl<YcxDdstSelected> implements YcxDdstSelectedService {
    @Resource
    private YcxDdstSelectedMapper ycxDdstSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxDdstSelected> ddstSelecteds, Integer recordId) {
        ddstSelecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(ddstSelecteds);
    }



}