package com.bmh.project.evaluation.ddst.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_ddst")
public class YcxDdst implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1个人社会，2精细动作，3语言，4大运动)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 操作说明
     */
    @Column(name = "tips")
    private String tips;

    /**
     * 最小月份
     */
    @Column(name = "min_month")
    private String minMonth;

    /**
     * 最大月份
     */
    @Column(name = "max_month")
    private String maxMonth;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}