package com.bmh.project.evaluation.ddst.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.ddst.model.YcxDdst;
import com.bmh.project.evaluation.ddst.service.YcxDdstService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.annotation.Resource;
import java.util.List;


/**
 * DDST 丹佛小儿只能发育筛选
 */
@RestController
@RequestMapping("ddst")
public class DdstController {


    @Resource
    private YcxDdstService ddstService;


    /**
     * 根据分类获取题库
     * @param type 类型(1个人社会，2精细动作，3语言，4大运动)
     * @return
     */
    @RequestMapping("getDdstByType")
    public Result getDdstByType(Integer type){
        List<YcxDdst> list = ddstService.getDdstByType(type);
        return ResultUtil.success(list);
    }



}
