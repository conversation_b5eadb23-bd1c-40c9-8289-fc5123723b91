package com.bmh.project.evaluation.pdms.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDicta;

public interface YcxPdmsDictaMapper extends BaseMapper<YcxPdmsDicta> {
    /**
     *  根据类型和原始分查询标准分
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param age (年龄)
     * @param origScore (原始分)
     * @return
     */
    Integer getRealScore(Integer type, Double age, Integer origScore);
}