package com.bmh.project.evaluation.pdms.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDictb;

/**
 * ycx_pdms_dictb表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
public interface YcxPdmsDictbService extends BaseService<YcxPdmsDictb> {
    /**
     * 根据总运动之和转换为商数
     * @param sum (5个功能区标准分之和)
     * @return
     */
    Integer getTmq(Integer sum);
}