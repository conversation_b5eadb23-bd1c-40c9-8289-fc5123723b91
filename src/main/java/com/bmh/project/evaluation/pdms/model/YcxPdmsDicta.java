package com.bmh.project.evaluation.pdms.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_pdms_dict_a")
public class YcxPdmsDicta implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 对应的父表类型(1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     */
    @Column(name = "pdms_type")
    private Integer pdmsType;

    /**
     * 最小年龄(单位是月)
     */
    @Column(name = "min_age")
    private Double minAge;

    /**
     * 最大年龄(单位是月)
     */
    @Column(name = "max_age")
    private Double maxAge;

    /**
     * 最小分数(原始分)
     */
    @Column(name = "min_score")
    private Integer minScore;

    /**
     * 最大分数(原始分)
     */
    @Column(name = "max_score")
    private Integer maxScore;

    /**
     * 标准分
     */
    @Column(name = "real_score")
    private Integer realScore;

    private static final long serialVersionUID = 1L;
}