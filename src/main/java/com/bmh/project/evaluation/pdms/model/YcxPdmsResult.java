package com.bmh.project.evaluation.pdms.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

@Data
@Table(name = "ycx_pdms_result")
public class YcxPdmsResult extends YcxResult implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 年龄
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子id
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位id
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 评测结果(总运动商)
     */
    @Column(name = "result_TMQ")
    private String resultTmq;

    /**
     * 评测结果(粗大运动商)
     */
    @Column(name = "result_GMQ")
    private String resultGmq;

    /**
     * 评测结果(精细运动商)
     */
    @Column(name = "result_FMQ")
    private String resultFmq;

    /**
     * 建议
     */
    @Column(name = "recome")
    private String recome;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 评测结果(年龄柱状图)
     */
    @Column(name = "result_column")
    private String resultColumn;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    private static final long serialVersionUID = 1L;

    /**
     * 选中项
     */
    @Transient
    private List<YcxPdmsSelected> selecteds;
}