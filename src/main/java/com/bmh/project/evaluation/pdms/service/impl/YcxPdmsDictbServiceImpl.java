package com.bmh.project.evaluation.pdms.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.pdms.mapper.YcxPdmsDictbMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDictb;
import com.bmh.project.evaluation.pdms.service.YcxPdmsDictbService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

/**
 * YcxPdmsDictbService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
@Service
public class YcxPdmsDictbServiceImpl extends BaseServiceImpl<YcxPdmsDictb> implements YcxPdmsDictbService {
    @Resource
    private YcxPdmsDictbMapper ycxPdmsDictbMapper;

    /**
     * 根据总运动之和转换为商数
     * @param sum (5个功能区标准分之和)
     * @return
     */
    @Override
    public Integer getTmq(Integer sum) {
        //当5个功能区之和大于95转为96
        if (sum > 95){
            sum = 96;
        }
        return ycxPdmsDictbMapper.getTmq(sum);
    }
}