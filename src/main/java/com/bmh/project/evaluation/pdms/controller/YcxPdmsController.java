package com.bmh.project.evaluation.pdms.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.pdms.model.YcxPdms;
import com.bmh.project.evaluation.pdms.service.YcxPdmsDictaService;
import com.bmh.project.evaluation.pdms.service.YcxPdmsDictbService;
import com.bmh.project.evaluation.pdms.service.YcxPdmsDictcService;
import com.bmh.project.evaluation.pdms.service.YcxPdmsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * PDMS-\\ peabody 运动发育量表
 */
@RestController
@RequestMapping("/pdms")
public class YcxPdmsController {

    @Resource
    private YcxPdmsService ycxPdmsService;
    @Resource
    private YcxPdmsDictaService dictAService;
    @Resource
    private YcxPdmsDictbService dictBService;
    @Resource
    private YcxPdmsDictcService dictCService;

    /**
     * 根据类型查询问题
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @return
     */
    @RequestMapping("/getPdms")
    public Result<List<YcxPdms>> getPdms(Integer type){
        List<YcxPdms> pdmsList = ycxPdmsService.getPdms(type);
        return ResultUtil.success(pdmsList);
    }

    /**
     *  根据类型和原始分查询标准分
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param age (年龄)
     * @param origScore (原始分)
     * @return
     */
    @RequestMapping("/getRealScore")
    public Result<Integer> getRealScore(Integer type,Double age,Integer origScore){
        return ResultUtil.success(dictAService.getRealScore(type,age,origScore));
    }

    /**
     * 根据总运动之和转换为商数
     * @param sum (5个功能区标准分之和)
     * @return
     */
    @RequestMapping("/getTmq")
    public Result<Integer> getTmq(Integer sum){
        return ResultUtil.success(dictBService.getTmq(sum));
    }

    /**
     * 根据类型和原始分数找出相当年龄
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param origScore (原始分数)
     * @return
     */
    @RequestMapping("/getEqualAge")
    public Result<Integer> getEqualAge(Integer type,Integer origScore){
        return ResultUtil.success(dictCService.getEqualAge(type,origScore));
    }
}

