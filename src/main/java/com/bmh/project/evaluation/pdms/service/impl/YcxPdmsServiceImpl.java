package com.bmh.project.evaluation.pdms.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.pdms.mapper.YcxPdmsMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdms;
import com.bmh.project.evaluation.pdms.service.YcxPdmsService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxPdmsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
@Service
public class YcxPdmsServiceImpl extends BaseServiceImpl<YcxPdms> implements YcxPdmsService {
    @Resource
    private YcxPdmsMapper ycxPdmsMapper;

    /**
     * 根据类型查询问题
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @return
     */
    @Override
    public List<YcxPdms> getPdms(Integer type) {
        return ycxPdmsMapper.getPdms(type);
    }
}