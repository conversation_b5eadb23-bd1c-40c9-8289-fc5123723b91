package com.bmh.project.evaluation.pdms.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDicta;
import com.bmh.project.evaluation.pdms.mapper.YcxPdmsDictaMapper;
import com.bmh.project.evaluation.pdms.service.YcxPdmsDictaService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * YcxPdmsDictAService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 18:23:27
 */
@Service
public class YcxPdmsDictaServiceImpl extends BaseServiceImpl<YcxPdmsDicta> implements YcxPdmsDictaService {
    @Resource
    private YcxPdmsDictaMapper ycxPdmsDictAMapper;

    /**
     *  根据类型和原始分查询标准分
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param age (年龄)
     * @param origScore (原始分)
     * @return
     */
    @Override
    public Integer getRealScore(Integer type, Double age, Integer origScore) {
        return ycxPdmsDictAMapper.getRealScore(type,age,origScore);
    }
}