package com.bmh.project.evaluation.pdms.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDictc;

public interface YcxPdmsDictcMapper extends BaseMapper<YcxPdmsDictc> {
    /**
     * 根据类型和原始分数找出相当年龄
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param score (原始分数)
     * @return
     */
    Integer getEqualAge(Integer type, Integer score);
}