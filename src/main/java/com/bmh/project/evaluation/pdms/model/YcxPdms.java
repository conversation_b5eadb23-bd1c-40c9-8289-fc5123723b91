package com.bmh.project.evaluation.pdms.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_pdms")
public class YcxPdms implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 题目类型(1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目
     */
    @Column(name = "content")
    private String content;

    /**
     * 适合月龄
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 体位
     */
    @Column(name = "operation")
    private String operation;

    /**
     * 步骤
     */
    @Column(name = "step")
    private String step;

    /**
     * 示例图片
     */
    @Column(name = "imag")
    private String imag;

    /**
     * 刺激物
     */
    @Column(name = "stimulate")
    private String stimulate;

    /**
     * 排序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态(0.无效 1.有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 开始月龄
     */
    @Column(name = "begin_month")
    private Integer beginMonth;

    /**
     * 结止月龄
     */
    @Column(name = "end_month")
    private Integer endMonth;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxPdmsOption> options;
}