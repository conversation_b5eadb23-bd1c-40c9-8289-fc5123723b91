package com.bmh.project.evaluation.pdms.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDicta;

/**
 * ycx_pdms_dict_a表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 18:23:27
 */
public interface YcxPdmsDictaService extends BaseService<YcxPdmsDicta> {
    /**
     *  根据类型和原始分查询标准分
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param age (年龄)
     * @param origScore (原始分)
     * @return
     */
    Integer getRealScore(Integer type, Double age, Integer origScore);
}