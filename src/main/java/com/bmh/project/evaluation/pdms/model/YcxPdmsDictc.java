package com.bmh.project.evaluation.pdms.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_pdms_dict_c")
public class YcxPdmsDictc implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 对应的父类标题
     */
    @Column(name = "pdms_type")
    private Integer pdmsType;

    /**
     * 原始分(最小分)
     */
    @Column(name = "min_score")
    private Integer minscore;

    /**
     * 原始分(最大分)
     */
    @Column(name = "max_score")
    private Integer maxscore;

    /**
     * 相当年龄
     */
    @Column(name = "equal_age")
    private Integer equalAge;

    private static final long serialVersionUID = 1L;
}