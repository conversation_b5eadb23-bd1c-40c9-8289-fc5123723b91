package com.bmh.project.evaluation.pdms.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.pdms.mapper.YcxPdmsSelectedMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdmsSelected;
import com.bmh.project.evaluation.pdms.service.YcxPdmsSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxPdmsSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
@Service
public class YcxPdmsSelectedServiceImpl extends BaseServiceImpl<YcxPdmsSelected> implements YcxPdmsSelectedService {
    @Resource
    private YcxPdmsSelectedMapper ycxPdmsSelectedMapper;

    @Override
    public void saveSelected(List<YcxPdmsSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public List<YcxPdmsSelected> getSelected(Integer recordId) {
        return ycxPdmsSelectedMapper.getSelected(recordId);
    }
}