package com.bmh.project.evaluation.pdms.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.pdms.model.YcxPdms;

import java.util.List;

/**
 * ycx_pdms表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
public interface YcxPdmsService extends BaseService<YcxPdms> {
    /**
     * 根据类型查询问题
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @return
     */
    List<YcxPdms> getPdms(Integer type);
}