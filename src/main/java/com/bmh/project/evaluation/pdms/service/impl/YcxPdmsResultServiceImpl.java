package com.bmh.project.evaluation.pdms.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.pdms.mapper.YcxPdmsResultMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdmsResult;
import com.bmh.project.evaluation.pdms.service.YcxPdmsResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.pdms.service.YcxPdmsSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxPdmsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
@Service("35")
public class YcxPdmsResultServiceImpl extends BaseServiceImpl<YcxPdmsResult> implements YcxPdmsResultService, YcxResultService<YcxPdmsResult> {
    @Resource
    private YcxPdmsResultMapper ycxPdmsResultMapper;
    @Resource
    private YcxPdmsSelectedService selectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxPdmsResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxPdmsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setCreatedTime(new Date());
        ycxPdmsResultMapper.insert(result);

        selectedService.saveSelected(result.getSelecteds(),result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxPdmsResult getResult(Integer recordId) {
        Example example = new Example(YcxPdmsResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxPdmsResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0){
            YcxPdmsResult result = list.get(0);
            result.setSelecteds(selectedService.getSelected(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxPdmsResult result = new YcxPdmsResult();
        result.setRecome(recom);
        Example example = new Example(YcxPdmsResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxPdmsResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}