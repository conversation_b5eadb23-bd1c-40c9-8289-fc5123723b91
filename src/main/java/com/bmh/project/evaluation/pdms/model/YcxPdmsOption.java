package com.bmh.project.evaluation.pdms.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_pdms_option")
public class YcxPdmsOption implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "pdms_id")
    private Integer pdmsId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}