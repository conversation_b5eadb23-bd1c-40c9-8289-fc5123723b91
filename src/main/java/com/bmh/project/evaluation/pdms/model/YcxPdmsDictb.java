package com.bmh.project.evaluation.pdms.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_pdms_dict_b")
public class YcxPdmsDictb implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 总运动(5个分测验的和)
     */
    @Column(name = "sum")
    private Integer sum;

    /**
     * 粗大运动
     */
    @Column(name = "gmq")
    private Integer gmq;

    /**
     * 精细运动
     */
    @Column(name = "fmq")
    private Integer fmq;

    /**
     * 总运动商( 5个功能区标准分之和转换的商数)
     */
    @Column(name = "tmq")
    private Integer tmq;

    private static final long serialVersionUID = 1L;
}