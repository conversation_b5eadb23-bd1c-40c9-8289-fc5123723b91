package com.bmh.project.evaluation.pdms.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.pdms.mapper.YcxPdmsDictcMapper;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDictc;
import com.bmh.project.evaluation.pdms.service.YcxPdmsDictcService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * YcxPdmsDictcService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
@Service
public class YcxPdmsDictcServiceImpl extends BaseServiceImpl<YcxPdmsDictc> implements YcxPdmsDictcService {
    @Resource
    private YcxPdmsDictcMapper ycxPdmsDictcMapper;

    /**
     * 根据类型和原始分数找出相当年龄
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param score (原始分数)
     * @return
     */
    @Override
    public Integer getEqualAge(Integer type, Integer score) {
        return ycxPdmsDictcMapper.getEqualAge(type,score);
    }
}