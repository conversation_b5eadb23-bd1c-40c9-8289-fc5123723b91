package com.bmh.project.evaluation.pdms.service;

import com.bmh.common.base.BaseService;
import com.bmh.common.base.Result;
import com.bmh.project.evaluation.pdms.model.YcxPdmsDictc;

/**
 * ycx_pdms_dictc表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 15:06:04
 */
public interface YcxPdmsDictcService extends BaseService<YcxPdmsDictc> {

    /**
     * 根据类型和原始分数找出相当年龄
     * @param type (1.反射 2.姿势 3.移动 4.实物操作 5.抓握 6.视觉—运动整合)
     * @param score (原始分数)
     * @return
     */
    Integer getEqualAge(Integer type, Integer score);
}