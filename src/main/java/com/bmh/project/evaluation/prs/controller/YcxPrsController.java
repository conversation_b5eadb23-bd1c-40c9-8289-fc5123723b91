package com.bmh.project.evaluation.prs.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.prs.model.YcxPrs;
import com.bmh.project.evaluation.prs.service.YcxPrsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 学习障碍筛查量表5-15岁(PRS)
 *
 * <AUTHOR>
 * @since 2021/7/10 16:05
 */
@RestController
@RequestMapping("/prs")
public class YcxPrsController {

    @Resource
    private YcxPrsService prsService;

    /**
     * 获取问题
     * @return
     */
    @RequestMapping("/getPrsList")
    public Result<List<YcxPrs>>getPrsList(){
        List<YcxPrs> prsList = prsService.getPrsList ();
        return ResultUtil.success (prsList);
    }
}
