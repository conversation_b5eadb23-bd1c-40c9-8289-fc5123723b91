package com.bmh.project.evaluation.prs.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.prs.mapper.YcxPrsMapper;
import com.bmh.project.evaluation.prs.model.YcxPrs;
import com.bmh.project.evaluation.prs.service.YcxPrsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxPrsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:04:17
 */
@Service
public class YcxPrsServiceImpl extends BaseServiceImpl<YcxPrs> implements YcxPrsService {
    @Resource
    private YcxPrsMapper ycxPrsMapper;

    /**
     * 获取问题
     *
     * @return
     */
    @Override
    public List<YcxPrs> getPrsList () {
        return ycxPrsMapper.getPrsList ();
    }
}