package com.bmh.project.evaluation.prs.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.prs.mapper.YcxPrsSelectedMapper;
import com.bmh.project.evaluation.prs.model.YcxPrsSelected;
import com.bmh.project.evaluation.prs.service.YcxPrsSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxPrsSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:04:17
 */
@Service
public class YcxPrsSelectedServiceImpl extends BaseServiceImpl<YcxPrsSelected> implements YcxPrsSelectedService {
    @Resource
    private YcxPrsSelectedMapper ycxPrsSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxPrsSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxPrsSelectedMapper.insertList(selecteds);
    }
}