package com.bmh.project.evaluation.prs.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.prs.mapper.YcxPrsResultMapper;
import com.bmh.project.evaluation.prs.model.YcxPrsResult;
import com.bmh.project.evaluation.prs.model.YcxPrsSelected;
import com.bmh.project.evaluation.prs.service.YcxPrsResultService;
import com.bmh.project.evaluation.prs.service.YcxPrsSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxPrsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:04:17
 */
@Service("23")
public class YcxPrsResultServiceImpl extends BaseServiceImpl<YcxPrsResult> implements YcxPrsResultService, YcxResultService<YcxPrsResult> {
    @Resource
    private YcxPrsResultMapper ycxPrsResultMapper;
    @Resource
    private YcxPrsSelectedService prsSelectedService;

    /**
     * 保存结果
     *
     * @param childrenRecord
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxPrsResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxPrsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date ());
        ycxPrsResultMapper.insert(result);

        List<YcxPrsSelected> selecteds = result.getSelecteds ();
        prsSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public YcxPrsResult getResult (Integer recordId) {
        Example example = new Example(YcxPrsResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxPrsResult> list = this.selectByExample(example);
        return CollectionUtil.isNotEmpty (list)?list.get(0):null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxPrsResult result = new YcxPrsResult ();
        result.setRecom (recom);
        Example example = new Example (YcxPrsResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxPrsResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}