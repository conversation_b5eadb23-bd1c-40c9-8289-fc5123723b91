package com.bmh.project.evaluation.weiss.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.weiss.mapper.YcxWeissMapper;
import com.bmh.project.evaluation.weiss.model.YcxWeiss;
import com.bmh.project.evaluation.weiss.service.YcxWeissService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxWeissService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:42:05
 */
@Service
public class YcxWeissServiceImpl extends BaseServiceImpl<YcxWeiss> implements YcxWeissService {
    @Resource
    private YcxWeissMapper ycxWeissMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1家庭 2学习和学校 3生活技能 4自我管理 5社会活动 6冒险活动)
     * @return
     */
    @Override
    public List<YcxWeiss> getWeissListByType (Integer type) {
        return ycxWeissMapper.getWeissListByType (type);
    }
}