package com.bmh.project.evaluation.weiss.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.weiss.model.YcxWeiss;

import java.util.List;

/**
 * ycx_weiss表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:42:05
 */
public interface YcxWeissService extends BaseService<YcxWeiss> {

    /**
     * 根据类型获取问题
     * @param type 类型(1家庭 2学习和学校 3生活技能 4自我管理 5社会活动 6冒险活动)
     * @return
     */
    List<YcxWeiss> getWeissListByType(Integer type);
}