package com.bmh.project.evaluation.weiss.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.weiss.mapper.YcxWeissResultMapper;
import com.bmh.project.evaluation.weiss.model.YcxWeissResult;
import com.bmh.project.evaluation.weiss.model.YcxWeissSelected;
import com.bmh.project.evaluation.weiss.service.YcxWeissResultService;
import com.bmh.project.evaluation.weiss.service.YcxWeissSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxWeissResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:42:05
 */
@Service("24")
public class YcxWeissResultServiceImpl extends BaseServiceImpl<YcxWeissResult> implements YcxWeissResultService, YcxResultService<YcxWeissResult> {
    @Resource
    private YcxWeissResultMapper ycxWeissResultMapper;
    @Resource
    private YcxWeissSelectedService weissSelectedService;

    /**
     * 保存结果
     *
     * @param childrenRecord
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxWeissResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxWeissResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date ());
        ycxWeissResultMapper.insert(result);

        List<YcxWeissSelected> selecteds = result.getSelecteds ();
        weissSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public YcxWeissResult getResult (Integer recordId) {
        Example example = new Example(YcxWeissResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxWeissResult> list = this.selectByExample(example);
        if(CollectionUtil.isNotEmpty (list)){
            YcxWeissResult result = list.get (0);
            result.setSelectedResult (weissSelectedService.getSelecteds (recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxWeissResult result = new YcxWeissResult ();
        result.setRecom (recom);
        Example example = new Example (YcxWeissResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxWeissResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}