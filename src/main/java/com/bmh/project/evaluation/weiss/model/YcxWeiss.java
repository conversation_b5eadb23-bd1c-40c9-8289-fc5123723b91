package com.bmh.project.evaluation.weiss.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_weiss")
public class YcxWeiss implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1家庭 2学习和学校 3生活技能 4自我管理 5社会活动 6冒险活动)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态(0无效 1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选项
     */
    @Transient
    private List<YcxWeissOption> options;

    private static final long serialVersionUID = 1L;
}