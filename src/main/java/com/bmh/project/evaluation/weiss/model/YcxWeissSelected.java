package com.bmh.project.evaluation.weiss.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_weiss_selected")
public class YcxWeissSelected implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题ID
     */
    @Column(name = "weiss_id")
    private Integer weissId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 问题类型（1家庭 2学习和学校 3生活技能 4自我管理 5社会活动 6冒险活动）
     */
    @Transient
    private Integer weissType;

    private static final long serialVersionUID = 1L;
}