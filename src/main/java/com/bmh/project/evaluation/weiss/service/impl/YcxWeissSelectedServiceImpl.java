package com.bmh.project.evaluation.weiss.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.weiss.mapper.YcxWeissSelectedMapper;
import com.bmh.project.evaluation.weiss.model.YcxWeissSelected;
import com.bmh.project.evaluation.weiss.service.YcxWeissSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * YcxWeissSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:42:05
 */
@Service
public class YcxWeissSelectedServiceImpl extends BaseServiceImpl<YcxWeissSelected> implements YcxWeissSelectedService {
    @Resource
    private YcxWeissSelectedMapper ycxWeissSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxWeissSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxWeissSelectedMapper.insertList(selecteds);
    }

    /**
     * 获取选择项
     *
     * @param recordId 记录ID
     */
    @Override
    public Map<Integer, List<YcxWeissSelected>> getSelecteds (Integer recordId) {
        List<YcxWeissSelected> selecteds = ycxWeissSelectedMapper.getSelecteds (recordId);
        return selecteds.stream ().collect (Collectors.groupingBy (YcxWeissSelected::getWeissType));
    }
}