package com.bmh.project.evaluation.iat.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.iat.mapper.YcxIatSelectedMapper;
import com.bmh.project.evaluation.iat.model.YcxIatSelected;
import com.bmh.project.evaluation.iat.service.YcxIatSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxIatSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月09日 15:33:28
 */
@Service
public class YcxIatSelectedServiceImpl extends BaseServiceImpl<YcxIatSelected> implements YcxIatSelectedService {
    @Resource
    private YcxIatSelectedMapper ycxIatSelectedMapper;

    @Override
    public void saveSelected(List<YcxIatSelected> ycxIatSelected, Integer recordId) {
        ycxIatSelected.forEach(op ->{
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdatTime(new Date());
        });
        this.insertList(ycxIatSelected);
    }

    @Override
    public List<YcxIatSelected> getSelecteds(Integer recordId) {
        return this.ycxIatSelectedMapper.getSelecteds(recordId);
    }
}