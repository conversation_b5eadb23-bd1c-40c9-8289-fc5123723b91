package com.bmh.project.evaluation.iat.controller;


import cn.hutool.core.util.StrUtil;
import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.iat.doc.YcxIatSelectedDoc;
import com.bmh.project.evaluation.iat.model.YcxIat;
import com.bmh.project.evaluation.iat.service.YcxIatService;
//import com.bmh.project.record.service.ChildrenRecordDocService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * IAT 网络成瘾诊断量表
 */
@RestController
@RequestMapping("iat")
public class YcxIatController {

    @Resource
    private YcxIatService iatService;

//    @Resource
//    private ChildrenRecordDocService recordDocService;

    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("getList")
    public Result<List<YcxIat>> getList(Integer recordId){
        List<YcxIat> iatList = iatService.getList(recordId);
        return ResultUtil.success(iatList);
    }

    /**
     * 保存选项答案
     * @param doc
     * @return
     */
    @PostMapping("/saveSelected")
    public Result<?> getProjectDoc(@RequestBody YcxIatSelectedDoc doc){
//        String id = StrUtil.format("{}_{}_{}",doc.getEvaluatingId(),doc.getProjectId(),doc.getSubjectId());
//        doc.setId(id);
//        Integer i = recordDocService.saveSelected(doc);
//        return ResultUtil.success (i);
        return ResultUtil.success ();
    }
}
