package com.bmh.project.evaluation.iat.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.iat.doc.YcxIatDoc;
import com.bmh.project.evaluation.iat.doc.YcxIatSelectedDoc;
import com.bmh.project.evaluation.iat.mapper.YcxIatResultMapper;
import com.bmh.project.evaluation.iat.model.YcxIatResult;
import com.bmh.project.evaluation.iat.model.YcxIatSelected;
import com.bmh.project.evaluation.iat.service.YcxIatResultService;
import com.bmh.project.evaluation.iat.service.YcxIatSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
//import com.bmh.project.record.service.ChildrenRecordDocService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * YcxIatResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月09日 15:33:28
 */
@Service("27")
public class YcxIatResultServiceImpl extends BaseServiceImpl<YcxIatResult> implements YcxIatResultService, YcxResultService<YcxIatResult> {
    @Resource
    private YcxIatResultMapper ycxIatResultMapper;
    @Resource
    private YcxIatSelectedService ycxIatSelectedService;
//    @Resource
//    private ChildrenRecordDocService recordDocService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxIatResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxIatResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge() + "");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        ycxIatResultMapper.insert(result);
        List<YcxIatSelected> ycxIatSelected = result.getSelecteds();
        ycxIatSelectedService.saveSelected(ycxIatSelected, result.getRecordId());
    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {
//        List<YcxIatSelectedDoc> selectedList = recordDocService.getSelectedList(
//                record.getEvaluatingId(), record.getProjectId(), YcxIatSelectedDoc.class);
//        if (CollectionUtil.isNotEmpty(selectedList)) {
//            Integer result = selectedList.stream().mapToInt(YcxIatSelectedDoc::getScore).sum();
//            if(result==null){
//                result=-1;
//            }
//            record.setResult(result.toString());
//            record.setResultStr(getResultStr(result));
//        }
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxIatResult getResult(Integer recordId) {
        Example example = new Example(YcxIatResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxIatResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0) {
            YcxIatResult result = list.get(0);
            result.setSelecteds(ycxIatSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxIatResult result = new YcxIatResult();
        result.setRecom(recom);
        Example example = new Example(YcxIatResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId", recordId);
        ycxIatResultMapper.updateByExampleSelective(result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {
//        List<YcxIatSelectedDoc> selectedList = recordDocService.getSelectedList(evaluatingId, projectId, YcxIatSelectedDoc.class);
//        if (CollectionUtil.isNotEmpty(selectedList)) {
//            Map<Integer, YcxIatSelectedDoc> map = selectedList.stream()
//                    .collect(Collectors.toMap(YcxIatSelectedDoc::getSubjectId, e -> e, (v1, v2) -> v1));
//            for (Object o : list) {
//                YcxIatDoc doc = (YcxIatDoc) o;
//                YcxIatSelectedDoc selected = map.get(doc.getId());
//                if (selected != null) {
//                    doc.setAnswer(selected.getAnswer());
//                }
//            }
//        }
    }

    private String getResultStr(Integer result) {
        if (result >= 0 && result < 20) {
            return StrUtil.format("{}分，你属于正常上网群体。",result);
        } else if (result >= 20 && result < 50) {
            return  StrUtil.format("{}分，你是个一般上网者,只是有时会上的多一些,但总体上是能够自我控制的,尚未沉溺于此。",result);
        } else if (result >= 50 && result < 80) {
            return  StrUtil.format("{}分，你由于上网似乎开始引起了一些问题,应该谨慎对待上网给你带来的影响,以及对家庭和其他成员带来的影响。",result);
        } else if (result >= 80 && result <= 100) {
            return  StrUtil.format("{}分，上网已经给你和你的家庭带来了很多问题,你必须马上正视并解决这些问题。",result);
        }
        return "分数异常";
    }
}