package com.bmh.project.evaluation.iat.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_iat_selected")
public class YcxIatSelected implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "record_id")
    private Integer recordId;

    @Column(name = "iat_id")
    private Integer iatId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "updat_time")
    private Date updatTime;

    private static final long serialVersionUID = 1L;
}