package com.bmh.project.evaluation.iat.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.common.service.BaseProjectService;
import com.bmh.project.evaluation.iat.doc.YcxIatDoc;
import com.bmh.project.evaluation.iat.doc.YcxIatOptionDoc;
import com.bmh.project.evaluation.iat.mapper.YcxIatMapper;
import com.bmh.project.evaluation.iat.model.YcxIat;
import com.bmh.project.evaluation.iat.model.YcxIatOption;
import com.bmh.project.evaluation.iat.service.YcxIatOptionService;
import com.bmh.project.evaluation.iat.service.YcxIatService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * YcxIatService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月09日 15:33:28
 */
@Service("Pro_27")
public class YcxIatServiceImpl extends BaseServiceImpl<YcxIat> implements YcxIatService, BaseProjectService {
    @Resource
    private YcxIatMapper ycxIatMapper;

    @Resource
    private YcxIatOptionService optionService;

    /**
     * 获取问题列表
     * @return
     */
    @Override
    public List<YcxIat> getList(Integer recordId) {
        List<YcxIat> list = this.ycxIatMapper.getList();
        return list;
    }

    @Override
    public List<YcxIatDoc> getProjectList() {
        List<YcxIatOption> optionList = optionService.selectAll();
        Map<Integer, List<YcxIatOption>> map = optionList.stream().collect(Collectors.groupingBy(YcxIatOption::getIatId));
        Example example = new Example(YcxIat.class);
        example.createCriteria().andEqualTo("status",1);
        example.orderBy("orderNum").asc();
        List<YcxIat> ycxIats = this.selectByExample(example);
        List<YcxIatDoc> list = new ArrayList<>();
        for (YcxIat iat : ycxIats) {
            YcxIatDoc doc = BeanUtil.copyProperties(iat,YcxIatDoc.class);
            List<YcxIatOption> iatOptions = map.get(iat.getId());
            List<YcxIatOptionDoc> optionDocs = BeanUtil.copyToList(iatOptions,YcxIatOptionDoc.class);
            doc.setOptions(optionDocs);
            list.add(doc);
        }
        return list;
    }

}