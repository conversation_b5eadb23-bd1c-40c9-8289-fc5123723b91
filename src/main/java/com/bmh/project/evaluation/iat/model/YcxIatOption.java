package com.bmh.project.evaluation.iat.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Table(name = "ycx_iat_option")
public class YcxIatOption implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "iat_id")
    private Integer iatId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}