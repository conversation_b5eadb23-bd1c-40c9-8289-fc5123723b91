package com.bmh.project.evaluation.adir.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_adir_option")
public class YcxAdirOption implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "adir_id")
    private Integer adirId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    private static final long serialVersionUID = 1L;
}