package com.bmh.project.evaluation.adir.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.abc.model.YcxAbcResult;
import com.bmh.project.evaluation.adir.mapper.YcxAdirResultMapper;
import com.bmh.project.evaluation.adir.model.YcxAdirResult;
import com.bmh.project.evaluation.adir.service.YcxAdirResultService;
import com.bmh.project.evaluation.adir.service.YcxAdirSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxAdirResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:58:12
 */
@Service("15")
public class YcxAdirResultServiceImpl extends BaseServiceImpl<YcxAdirResult> implements YcxAdirResultService, YcxResultService<YcxAdirResult> {
    @Resource
    private YcxAdirResultMapper ycxAdirResultMapper;
    @Resource
    private YcxAdirSelectedService adirSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxAdirResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxAdirResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge() + "");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        adirSelectedService.saveSelecteds(result.getSelecteds(), result.getRecordId());
    }

    @Override
    public YcxAdirResult getResult(Integer recordId) {
        Example example = new Example(YcxAdirResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxAdirResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0) {
            YcxAdirResult result = list.get(0);
            result.setResultSelecteds(adirSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxAdirResult result = new YcxAdirResult();
        result.setRecom(recom);
        Example example = new Example(YcxAbcResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId", recordId);
        ycxAdirResultMapper.updateByExampleSelective(result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}