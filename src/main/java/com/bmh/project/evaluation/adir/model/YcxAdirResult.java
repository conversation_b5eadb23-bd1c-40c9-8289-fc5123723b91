package com.bmh.project.evaluation.adir.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@Table(name = "ycx_adir_result")
public class YcxAdirResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private String childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 社会交互作用评测结果
     */
    @Column(name = "res_type1")
    private String resType1;

    /**
     * 语言及交流-言辞评测结果
     */
    @Column(name = "res_type2")
    private String resType2;

    /**
     * 刻板、局限、重复的兴趣与行为方式评测结果
     */
    @Column(name = "res_type3")
    private String resType3;

    /**
     * 语言及交流-非言辞评测结果
     */
    @Column(name = "res_type4")
    private String resType4;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 行为及沟通能力结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;


    /**
     * 选中项
     */
    @Transient
    private List<YcxAdirSelected> selecteds;


    /**
     * 结果选项
     */
    @Transient
    private Map<String, Object> resultSelecteds;


}