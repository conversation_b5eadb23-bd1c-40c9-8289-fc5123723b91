package com.bmh.project.evaluation.adir.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.adir.mapper.YcxAdirMapper;
import com.bmh.project.evaluation.adir.model.YcxAdir;
import com.bmh.project.evaluation.adir.service.YcxAdirService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxAdirService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:58:12
 */
@Service
public class YcxAdirServiceImpl extends BaseServiceImpl<YcxAdir> implements YcxAdirService {
    @Resource
    private YcxAdirMapper ycxAdirMapper;

    @Override
    public List<YcxAdir> getAdirListByType(Integer type) {
        return this.ycxAdirMapper.getAdirListByType(type);
    }


}