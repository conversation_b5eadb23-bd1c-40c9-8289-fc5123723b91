package com.bmh.project.evaluation.adir.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.adir.model.YcxAdirSelected;

import java.util.List;
import java.util.Map;

/**
 * ycx_adir_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:58:12
 */
public interface YcxAdirSelectedService extends BaseService<YcxAdirSelected> {

    void saveSelecteds(List<YcxAdirSelected> selecteds, Integer recordId);

    Map<String, Object> getSelecteds(Integer recordId);

}