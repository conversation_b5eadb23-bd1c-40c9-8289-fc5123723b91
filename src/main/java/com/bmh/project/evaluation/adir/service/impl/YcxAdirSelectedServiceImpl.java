package com.bmh.project.evaluation.adir.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.adir.mapper.YcxAdirSelectedMapper;
import com.bmh.project.evaluation.adir.model.YcxAdirSelected;
import com.bmh.project.evaluation.adir.service.YcxAdirSelectedService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.chat.model.YcxChatSelected;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * YcxAdirSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 10:58:12
 */
@Service
public class YcxAdirSelectedServiceImpl extends BaseServiceImpl<YcxAdirSelected> implements YcxAdirSelectedService {
    @Resource
    private YcxAdirSelectedMapper ycxAdirSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxAdirSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public Map<String, Object> getSelecteds(Integer recordId) {
        Map<String, Object> resultSelected = new HashMap<>();
        List<YcxChatSelected> selecteds1 = this.ycxAdirSelectedMapper.selectSelectedByType(1,recordId);
        List<YcxChatSelected> selecteds2 = this.ycxAdirSelectedMapper.selectSelectedByType(2,recordId);
        List<YcxChatSelected> selecteds3 = this.ycxAdirSelectedMapper.selectSelectedByType(3,recordId);
        resultSelected.put("selecteds1", selecteds1);
        resultSelected.put("selecteds2", selecteds2);
        resultSelected.put("selecteds3", selecteds3);
        return resultSelected;
    }


}