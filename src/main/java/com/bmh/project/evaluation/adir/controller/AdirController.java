package com.bmh.project.evaluation.adir.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.adir.model.YcxAdir;
import com.bmh.project.evaluation.adir.service.YcxAdirService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * ADI-R 孤独症诊断访谈量表
 */
@RestController
@RequestMapping("adir")
public class AdirController {


    @Resource
    private YcxAdirService adirService;


    /**
     * 根据类型获取题目列表
     * @param type 类型(1社会交互作用，2语言及交流，3刻板局限重要的兴趣与行为)
     * @return
     */
    @RequestMapping("getAdirListByType")
    public Result getAdirListByType(Integer type){
        List<YcxAdir> list = adirService.getAdirListByType(type);
        return ResultUtil.success(list);
    }



}
