package com.bmh.project.evaluation.tas.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import com.bmh.project.evaluation.iat.model.YcxIatSelected;
import lombok.Data;

@Data
@Table(name = "ycx_tas_result")
public class YcxTasResult extends YcxResult implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private String childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 考试焦虑结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    private static final long serialVersionUID = 1L;

    /**
     * 选中项
     */
    @Transient
    private List<YcxTasSelected> selecteds;
}