package com.bmh.project.evaluation.tas.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_tas_option")
public class YcxTasOption implements Serializable {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "tas_id")
    private Integer tasId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}