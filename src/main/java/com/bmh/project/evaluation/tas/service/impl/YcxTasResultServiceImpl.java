package com.bmh.project.evaluation.tas.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.tas.mapper.YcxTasResultMapper;
import com.bmh.project.evaluation.tas.model.YcxTasResult;
import com.bmh.project.evaluation.tas.model.YcxTasSelected;
import com.bmh.project.evaluation.tas.service.YcxTasResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.tas.service.YcxTasSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxTasResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月10日 11:35:49
 */
@Service("29")
public class YcxTasResultServiceImpl extends BaseServiceImpl<YcxTasResult> implements YcxTasResultService, YcxResultService<YcxTasResult> {
    @Resource
    private YcxTasResultMapper ycxTasResultMapper;
    @Resource
    private YcxTasSelectedService ycxTasSelectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxTasResult result = JSONUtil.toBean(childrenRecord.getResultStr(),YcxTasResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        ycxTasResultMapper.insert(result);
        List<YcxTasSelected> ycxTasSelecteds = result.getSelecteds();
        ycxTasSelectedService.saveSelected(ycxTasSelecteds,result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxTasResult getResult(Integer recordId) {
        Example example = new Example(YcxTasResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxTasResult> list = this.selectByExample(example);
        if (list!=null && list.size()>0){
            YcxTasResult result = list.get(0);
            result.setSelecteds(ycxTasSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxTasResult result = new YcxTasResult();
        result.setRecom(recom);
        Example example = new Example(YcxTasResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxTasResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}