package com.bmh.project.evaluation.tas.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.tas.mapper.YcxTasSelectedMapper;
import com.bmh.project.evaluation.tas.model.YcxTasSelected;
import com.bmh.project.evaluation.tas.service.YcxTasSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxTasSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月10日 11:01:17
 */
@Service
public class YcxTasSelectedServiceImpl extends BaseServiceImpl<YcxTasSelected> implements YcxTasSelectedService {
    @Resource
    private YcxTasSelectedMapper ycxTasSelectedMapper;

    @Override
    public void saveSelected(List<YcxTasSelected> ycxTasSelecteds, Integer recordId) {
        ycxTasSelecteds.forEach(op ->{
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(ycxTasSelecteds);
    }

    @Override
    public List<YcxTasSelected> getSelecteds(Integer recordId) {
        return this.ycxTasSelectedMapper.getSelecteds(recordId);
    }
}