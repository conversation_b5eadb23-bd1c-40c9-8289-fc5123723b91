package com.bmh.project.evaluation.tas.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.tas.mapper.YcxTasMapper;
import com.bmh.project.evaluation.tas.model.YcxTas;
import com.bmh.project.evaluation.tas.service.YcxTasService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxTasService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月10日 11:35:49
 */
@Service
public class YcxTasServiceImpl extends BaseServiceImpl<YcxTas> implements YcxTasService {
    @Resource
    private YcxTasMapper ycxTasMapper;

    /**
     * 获取问题列表
     * @return
     */
    @Override
    public List<YcxTas> getList() {
        return this.ycxTasMapper.getList();
    }
}