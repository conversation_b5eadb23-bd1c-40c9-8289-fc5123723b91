package com.bmh.project.evaluation.tas.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.ame.model.YcxAme;
import com.bmh.project.evaluation.tas.model.YcxTas;
import com.bmh.project.evaluation.tas.service.YcxTasService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * TAS Sarason考试焦虑量表
 */
@RestController
@RequestMapping("tas")
public class YcxTasController {
    @Autowired
    private YcxTasService ycxTasService;

    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("getList")
    public Result<List<YcxTas>> getList(){
        List<YcxTas> tasList = ycxTasService.getList();
        return ResultUtil.success(tasList);
    }
}
