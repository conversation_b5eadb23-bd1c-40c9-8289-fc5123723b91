package com.bmh.project.evaluation.cmt.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cmt.mapper.YcxCmtSelectedMapper;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import com.bmh.project.evaluation.cmt.service.YcxCmtSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxCmtSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月10日 13:40:34
 */
@Service
public class YcxCmtSelectedServiceImpl extends BaseServiceImpl<YcxCmtSelected> implements YcxCmtSelectedService {
    @Resource
    private YcxCmtSelectedMapper ycxCmtSelectedMapper;

    @Override
    public void saveSelected(List<YcxCmtSelected> ycxCmtSelecteds, Integer recordId) {
        ycxCmtSelecteds.forEach(op ->{
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
    }

    @Override
    public List<YcxCmtSelected> getSelecteds(Integer recordId) {
        return this.ycxCmtSelectedMapper.getSelecteds(recordId);
    }
}