package com.bmh.project.evaluation.cmt.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.cmt.mapper.YcxCmtResultMapper;
import com.bmh.project.evaluation.cmt.model.YcxCmtResult;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;
import com.bmh.project.evaluation.cmt.service.YcxCmtResultService;
import com.bmh.project.evaluation.cmt.service.YcxCmtSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCmtResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月10日 13:40:34
 */
@Service("30")
public class YcxCmtResultServiceImpl extends BaseServiceImpl<YcxCmtResult> implements YcxCmtResultService, YcxResultService<YcxCmtResult> {
    @Resource
    private YcxCmtResultMapper ycxCmtResultMapper;
    @Resource
    private YcxCmtSelectedService ycxCmtSelectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxCmtResult result = JSONUtil.toBean(childrenRecord.getResultStr(),YcxCmtResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        ycxCmtResultMapper.insert(result);
        List<YcxCmtSelected> ycxCmtSelecteds = result.getSelecteds();
        ycxCmtSelectedService.saveSelected(ycxCmtSelecteds,result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxCmtResult getResult(Integer recordId) {
        Example example = new Example(YcxCmtResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxCmtResult> list = this.selectByExample(example);
        if (list!=null&&list.size()>0){
            YcxCmtResult result = list.get(0);
            result.setSelecteds(ycxCmtSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxCmtResult result = new YcxCmtResult();
        result.setRecom(recom);
        Example example = new Example(YcxCmtResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxCmtResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}