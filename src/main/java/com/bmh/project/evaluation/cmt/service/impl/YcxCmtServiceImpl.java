package com.bmh.project.evaluation.cmt.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cmt.mapper.YcxCmtMapper;
import com.bmh.project.evaluation.cmt.model.YcxCmt;
import com.bmh.project.evaluation.cmt.service.YcxCmtService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxCmtService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月10日 13:40:34
 */
@Service
public class YcxCmtServiceImpl extends BaseServiceImpl<YcxCmt> implements YcxCmtService {
    @Resource
    private YcxCmtMapper ycxCmtMapper;

    /**
     * 获取问题列表
     * @return
     */
    @Override
    public List<YcxCmt> getList() {
        return this.ycxCmtMapper.getList();
    }
}