package com.bmh.project.evaluation.cmt.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cmt.model.YcxCmtSelected;

import java.util.List;

/**
 * ycx_cmt_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月10日 13:40:34
 */
public interface YcxCmtSelectedService extends BaseService<YcxCmtSelected> {
    
    void saveSelected(List<YcxCmtSelected> ycxCmtSelecteds, Integer recordId);

    List<YcxCmtSelected> getSelecteds(Integer recordId);
}