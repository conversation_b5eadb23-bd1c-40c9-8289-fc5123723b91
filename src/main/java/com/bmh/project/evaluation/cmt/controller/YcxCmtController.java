package com.bmh.project.evaluation.cmt.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.cmt.model.YcxCmt;
import com.bmh.project.evaluation.cmt.service.YcxCmtService;
import com.bmh.project.evaluation.iat.model.YcxIat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * CMT 3-6岁儿童心理健康测试
 */
@RestController
@RequestMapping("cmt")
public class YcxCmtController {
    @Autowired
    private YcxCmtService ycxCmtService;

    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("getList")
    public Result<List<YcxCmt>> getList(){
        List<YcxCmt> cmtList = ycxCmtService.getList();
        return ResultUtil.success(cmtList);
    }
}
