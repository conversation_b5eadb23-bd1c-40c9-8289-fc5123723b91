package com.bmh.project.evaluation.nbna.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.nbna.model.YcxNbna;
import com.bmh.project.evaluation.nbna.service.YcxNbnaService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * NBNA 新生儿20项行为神经评定心理量表
 *
 * <AUTHOR>
 * @since 2021/7/19 14:49
 */
@RestController
@RequestMapping("/nbna")
public class YcxNbnaController {

    @Resource
    private YcxNbnaService nbnaService;

    /**
     * 根据类型获取问题
     * @param type 类型(1行为能力，2被动肌张力，3主动肌张力，4原始反射，5一般估价)
     * @return
     */
    @RequestMapping("getNbnaListByType")
    public Result<List<YcxNbna>> getNbnaListByType(Integer type){
        List<YcxNbna> list = nbnaService.getNbnaListByType(type);
        return ResultUtil.success(list);
    }
}
