package com.bmh.project.evaluation.nbna.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nbna.mapper.YcxNbnaMapper;
import com.bmh.project.evaluation.nbna.model.YcxNbna;
import com.bmh.project.evaluation.nbna.service.YcxNbnaService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxNbnaService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月19日 14:41:24
 */
@Service
public class YcxNbnaServiceImpl extends BaseServiceImpl<YcxNbna> implements YcxNbnaService {
    @Resource
    private YcxNbnaMapper ycxNbnaMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1行为能力，2被动肌张力，3主动肌张力，4原始反射，5一般估价)
     * @return
     */
    @Override
    public List<YcxNbna> getNbnaListByType (Integer type) {
        return ycxNbnaMapper.getNbnaListByType (type);
    }
}