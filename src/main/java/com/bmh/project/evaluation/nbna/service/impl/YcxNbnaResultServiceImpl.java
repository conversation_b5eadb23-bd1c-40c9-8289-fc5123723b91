package com.bmh.project.evaluation.nbna.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.nbna.mapper.YcxNbnaResultMapper;
import com.bmh.project.evaluation.nbna.model.YcxNbnaResult;
import com.bmh.project.evaluation.nbna.model.YcxNbnaSelected;
import com.bmh.project.evaluation.nbna.service.YcxNbnaResultService;
import com.bmh.project.evaluation.nbna.service.YcxNbnaSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxNbnaResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月19日 14:41:24
 */
@Service("9")
public class YcxNbnaResultServiceImpl extends BaseServiceImpl<YcxNbnaResult> implements YcxNbnaResultService, YcxResultService<YcxNbnaResult> {
    @Resource
    private YcxNbnaResultMapper ycxNbnaResultMapper;
    @Resource
    private YcxNbnaSelectedService nbnaSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxNbnaResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxNbnaResult.class);
        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge ());
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxNbnaResultMapper.insert (result);

        List<YcxNbnaSelected> selecteds = result.getSelecteds ();
        nbnaSelectedService.saveSelecteds (selecteds, result.getRecordId ());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxNbnaResult getResult (Integer recordId) {
        Example example = new Example (YcxNbnaResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxNbnaResult> list = this.selectByExample (example);
        return CollectionUtil.isNotEmpty (list)?list.get (0):null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxNbnaResult result = new YcxNbnaResult ();
        result.setRecom (recom);
        Example example = new Example (YcxNbnaResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxNbnaResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}