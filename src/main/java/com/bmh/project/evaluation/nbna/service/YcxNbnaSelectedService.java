package com.bmh.project.evaluation.nbna.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.nbna.model.YcxNbnaSelected;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;

import java.util.List;

/**
 * ycx_nbna_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月19日 14:41:24
 */
public interface YcxNbnaSelectedService extends BaseService<YcxNbnaSelected> {

    /**
     * 保存选择项
     * @param selecteds 选择项
     * @param recordId 记录ID
     */
    void saveSelecteds(List<YcxNbnaSelected> selecteds, Integer recordId);
}