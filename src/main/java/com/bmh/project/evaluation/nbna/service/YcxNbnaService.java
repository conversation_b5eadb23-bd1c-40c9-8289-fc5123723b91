package com.bmh.project.evaluation.nbna.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.nbna.model.YcxNbna;

import java.util.List;

/**
 * ycx_nbna表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月19日 14:41:24
 */
public interface YcxNbnaService extends BaseService<YcxNbna> {

    /**
     * 根据类型获取问题
     * @param type 类型(1行为能力，2被动肌张力，3主动肌张力，4原始反射，5一般估价)
     * @return
     */
    List<YcxNbna> getNbnaListByType(Integer type);
}