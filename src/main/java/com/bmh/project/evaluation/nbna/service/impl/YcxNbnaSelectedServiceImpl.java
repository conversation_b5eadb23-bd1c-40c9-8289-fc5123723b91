package com.bmh.project.evaluation.nbna.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.nbna.mapper.YcxNbnaSelectedMapper;
import com.bmh.project.evaluation.nbna.model.YcxNbnaSelected;
import com.bmh.project.evaluation.nbna.service.YcxNbnaSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxNbnaSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月19日 14:41:24
 */
@Service
public class YcxNbnaSelectedServiceImpl extends BaseServiceImpl<YcxNbnaSelected> implements YcxNbnaSelectedService {
    @Resource
    private YcxNbnaSelectedMapper ycxNbnaSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxNbnaSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxNbnaSelectedMapper.insertList(selecteds);
    }
}