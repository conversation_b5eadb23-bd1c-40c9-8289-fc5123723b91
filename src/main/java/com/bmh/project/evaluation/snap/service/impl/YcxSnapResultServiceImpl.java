package com.bmh.project.evaluation.snap.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.snap.mapper.YcxSnapResultMapper;
import com.bmh.project.evaluation.snap.model.YcxSnapResult;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;
import com.bmh.project.evaluation.snap.service.YcxSnapResultService;
import com.bmh.project.evaluation.snap.service.YcxSnapSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxSnapResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 10:02:07
 */
@Service ("19")
public class YcxSnapResultServiceImpl extends BaseServiceImpl<YcxSnapResult> implements YcxSnapResultService, YcxResultService<YcxSnapResult> {
    @Resource
    private YcxSnapResultMapper ycxSnapResultMapper;
    @Resource
    private YcxSnapSelectedService snapSelectedService;

    /**
     * 保存结果
     *
     * @param childrenRecord
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {

        YcxSnapResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxSnapResult.class);
        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge ());
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxSnapResultMapper.insert (result);

        List<YcxSnapSelected> selecteds = result.getSelecteds ();
        snapSelectedService.saveSelecteds (selecteds, result.getRecordId ());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public YcxSnapResult getResult (Integer recordId) {
        Example example = new Example (YcxSnapResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxSnapResult> list = this.selectByExample (example);
        if (CollectionUtil.isNotEmpty (list)) {
            YcxSnapResult result = list.get (0);
            result.setSelecteds (snapSelectedService.getSelecteds (recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxSnapResult result = new YcxSnapResult ();
        result.setRecom (recom);
        Example example = new Example (YcxSnapResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxSnapResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}