package com.bmh.project.evaluation.snap.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.snap.mapper.YcxSnapSelectedMapper;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;
import com.bmh.project.evaluation.snap.service.YcxSnapSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxSnapSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 10:02:07
 */
@Service
public class YcxSnapSelectedServiceImpl extends BaseServiceImpl<YcxSnapSelected> implements YcxSnapSelectedService {
    @Resource
    private YcxSnapSelectedMapper ycxSnapSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxSnapSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxSnapSelectedMapper.insertList(selecteds);
    }

    /**
     * 获取结果项
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public List<YcxSnapSelected> getSelecteds (Integer recordId) {
        Example example = new Example (YcxSnapSelected.class);
        example.createCriteria ().andEqualTo ("recordId",recordId);
        return ycxSnapSelectedMapper.selectByExample (example);
    }
}