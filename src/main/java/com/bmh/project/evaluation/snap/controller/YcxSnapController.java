package com.bmh.project.evaluation.snap.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.snap.model.YcxSnap;
import com.bmh.project.evaluation.snap.service.YcxSnapService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * SNAP-IV注意缺陷多动障碍筛查量表
 *
 * <AUTHOR>
 * @since 2021/7/10 10:02
 */
@RestController
@RequestMapping("/snap")
public class YcxSnapController {

    @Resource
    private YcxSnapService snapService;

    /**
     * 根据类型获取问题
     * @param type 类型(1注意力不足，2多动/冲动，3对立违抗)
     * @return
     */
    @RequestMapping("getSnapListByType")
    public Result<List<YcxSnap>> getSnapListByType(Integer type){
        List<YcxSnap> list = snapService.getSnapListByType(type);
        return ResultUtil.success(list);
    }

}
