package com.bmh.project.evaluation.snap.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.snap.model.YcxSnap;

import java.util.List;

/**
 * ycx_snap表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月10日 10:02:07
 */
public interface YcxSnapService extends BaseService<YcxSnap> {

    /**
     * 根据类型获取问题
     * @param type 类型(1注意力不足，2多动/冲动，3对立违抗)
     * @return
     */
    List<YcxSnap> getSnapListByType(Integer type);
}