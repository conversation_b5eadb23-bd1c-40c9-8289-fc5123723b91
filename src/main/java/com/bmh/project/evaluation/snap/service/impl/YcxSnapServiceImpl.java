package com.bmh.project.evaluation.snap.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.snap.mapper.YcxSnapMapper;
import com.bmh.project.evaluation.snap.model.YcxSnap;
import com.bmh.project.evaluation.snap.service.YcxSnapService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxSnapService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 10:02:07
 */
@Service
public class YcxSnapServiceImpl extends BaseServiceImpl<YcxSnap> implements YcxSnapService {
    @Resource
    private YcxSnapMapper ycxSnapMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1注意力不足，2多动/冲动，3对立违抗)
     * @return
     */
    @Override
    public List<YcxSnap> getSnapListByType (Integer type) {
        return ycxSnapMapper.getSnapListByType (type);
    }
}