package com.bmh.project.evaluation.snap.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.snap.model.YcxSnapSelected;

import java.util.List;

/**
 * ycx_snap_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月10日 10:02:07
 */
public interface YcxSnapSelectedService extends BaseService<YcxSnapSelected> {

    /**
     * 保存选择项
     * @param selecteds 选择项
     * @param recordId 记录ID
     */
    void saveSelecteds(List<YcxSnapSelected> selecteds, Integer recordId);

    /**
     * 获取结果项
     * @param recordId 记录ID
     * @return
     */
    List<YcxSnapSelected> getSelecteds(Integer recordId);
}