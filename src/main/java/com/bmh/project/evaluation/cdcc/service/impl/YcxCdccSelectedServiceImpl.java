package com.bmh.project.evaluation.cdcc.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cdcc.mapper.YcxCdccSelectedMapper;
import com.bmh.project.evaluation.cdcc.model.YcxCdccSelected;
import com.bmh.project.evaluation.cdcc.service.YcxCdccSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCdccSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月23日 16:36:10
 */
@Service
public class YcxCdccSelectedServiceImpl extends BaseServiceImpl<YcxCdccSelected> implements YcxCdccSelectedService {
    @Resource
    private YcxCdccSelectedMapper ycxCdccSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxCdccSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxCdccSelectedMapper.insertList(selecteds);
    }
}