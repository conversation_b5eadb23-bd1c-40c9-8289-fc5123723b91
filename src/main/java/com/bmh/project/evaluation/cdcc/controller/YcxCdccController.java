package com.bmh.project.evaluation.cdcc.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.cdcc.model.YcxCdcc;
import com.bmh.project.evaluation.cdcc.service.YcxCdccDictService;
import com.bmh.project.evaluation.cdcc.service.YcxCdccService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.util.StringUtil;

import javax.annotation.Resource;
import java.util.List;

/**
 * CDCC-0-3岁婴幼儿发育量表
 *
 * <AUTHOR>
 * @since 2021/7/23 17:45
 */
@RestController
@RequestMapping ("/cdcc")
public class YcxCdccController {

    @Resource
    private YcxCdccService cdccService;
    @Resource
    private YcxCdccDictService dictService;


    /**
     * 根据类型获取问题
     *
     * @param type 类型(1智力量表，2运动量表)
     * @return
     */
    @RequestMapping ("getCdccListByType")
    public Result<List<YcxCdcc>> getCdccListByType (Integer type) {
        List<YcxCdcc> list = cdccService.getCdccListByType (type);
        return ResultUtil.success (list);
    }

    /**
     * 查询真实分
     * @param type 类型(1MDI，2PDI)
     * @param day 天
     * @param origScore 原始分
     * @return
     */
    @RequestMapping ("/getScoreByDict")
    public Result<String> getScoreByDict (Integer type, Integer day, String origScore) {
        if (type == null || StringUtil.isEmpty (origScore)) {
            return ResultUtil.error ("参数错误");
        }
        String realScore = dictService.getRealScore (type, day, origScore);
        return ResultUtil.success (realScore);
    }
}
