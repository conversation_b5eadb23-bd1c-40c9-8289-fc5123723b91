package com.bmh.project.evaluation.cdcc.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cdcc.model.YcxCdccDict;

/**
 * ycx_cdcc_dict表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月02日 11:13:14
 */
public interface YcxCdccDictService extends BaseService<YcxCdccDict> {

    /**
     * 查询真实分
     * @param type 类型(1MDI，2PDI)
     * @param day 天
     * @param origScore 原始分
     * @return
     */
    String getRealScore(Integer type, Integer day, String origScore);
}