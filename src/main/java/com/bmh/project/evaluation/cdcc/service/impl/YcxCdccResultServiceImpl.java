package com.bmh.project.evaluation.cdcc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.cdcc.mapper.YcxCdccResultMapper;
import com.bmh.project.evaluation.cdcc.model.YcxCdccResult;
import com.bmh.project.evaluation.cdcc.model.YcxCdccSelected;
import com.bmh.project.evaluation.cdcc.service.YcxCdccResultService;
import com.bmh.project.evaluation.cdcc.service.YcxCdccSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCdccResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月23日 16:36:10
 */
@Service("26")
public class YcxCdccResultServiceImpl extends BaseServiceImpl<YcxCdccResult> implements YcxCdccResultService, YcxResultService<YcxCdccResult> {
    @Resource
    private YcxCdccResultMapper ycxCdccResultMapper;
    @Resource
    private YcxCdccSelectedService cdccSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxCdccResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxCdccResult.class);
        result.setRecordId (childrenRecord.getId ());
        result.setChildrenAge (childrenRecord.getChildrenAge ());
        result.setChildrenId (childrenRecord.getChildrenId ());
        result.setOrgId (childrenRecord.getOrgId ());
        result.setOperatorId (SecurityUtil.getUserId ());
        result.setOperatorName (SecurityUtil.getNickName ());
        result.setCreateTime (new Date ());
        ycxCdccResultMapper.insert (result);

        List<YcxCdccSelected> selecteds = result.getSelecteds ();
        cdccSelectedService.saveSelecteds (selecteds, result.getRecordId ());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxCdccResult getResult (Integer recordId) {
        Example example = new Example (YcxCdccResult.class);
        example.createCriteria ().andEqualTo ("recordId", recordId);
        List<YcxCdccResult> list = this.selectByExample (example);
        return CollectionUtil.isNotEmpty (list)?list.get (0):null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxCdccResult result = new YcxCdccResult ();
        result.setRecom (recom);
        Example example = new Example (YcxCdccResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxCdccResultMapper.updateByExampleSelective (result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}