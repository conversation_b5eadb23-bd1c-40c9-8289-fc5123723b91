package com.bmh.project.evaluation.cdcc.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cdcc.mapper.YcxCdccMapper;
import com.bmh.project.evaluation.cdcc.model.YcxCdcc;
import com.bmh.project.evaluation.cdcc.service.YcxCdccService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxCdccService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月23日 16:36:10
 */
@Service
public class YcxCdccServiceImpl extends BaseServiceImpl<YcxCdcc> implements YcxCdccService {
    @Resource
    private YcxCdccMapper ycxCdccMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1智力量表，2运动量表)
     * @return
     */
    @Override
    public List<YcxCdcc> getCdccListByType (Integer type) {
        return ycxCdccMapper.getCdccListByType (type);
    }
}