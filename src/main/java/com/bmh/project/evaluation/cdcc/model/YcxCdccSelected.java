package com.bmh.project.evaluation.cdcc.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_cdcc_selected")
public class YcxCdccSelected implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题ID
     */
    @Column(name = "cdcc_id")
    private Integer cdccId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}