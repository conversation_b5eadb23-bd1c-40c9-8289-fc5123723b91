package com.bmh.project.evaluation.cdcc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cdcc.mapper.YcxCdccDictMapper;
import com.bmh.project.evaluation.cdcc.model.YcxCdccDict;
import com.bmh.project.evaluation.cdcc.service.YcxCdccDictService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxCdccDictService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月02日 11:13:14
 */
@Service
public class YcxCdccDictServiceImpl extends BaseServiceImpl<YcxCdccDict> implements YcxCdccDictService {
    @Resource
    private YcxCdccDictMapper ycxCdccDictMapper;

    /**
     * 查询真实分
     *
     * @param type      类型(1MDI，2PDI)
     * @param day       天
     * @param origScore 原始分
     * @return
     */
    @Override
    public String getRealScore (Integer type, Integer day, String origScore) {
        Example example = new Example (YcxCdccDict.class);
        example.createCriteria ().andEqualTo ("type", type).andEqualTo ("origScore", origScore)
                .andLessThanOrEqualTo ("minDay", day).andGreaterThanOrEqualTo ("maxDay", day);
        List<YcxCdccDict> list = ycxCdccDictMapper.selectByExample (example);
        if (CollectionUtil.isNotEmpty (list)) {
            return list.get (0).getRealScore ();
        }
        return null;
    }
}