package com.bmh.project.evaluation.cdcc.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.cdcc.model.YcxCdcc;

import java.util.List;

/**
 * ycx_cdcc表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月23日 16:36:10
 */
public interface YcxCdccService extends BaseService<YcxCdcc> {

    /**
     * 根据类型获取问题
     * @param type 类型(1智力量表，2运动量表)
     * @return
     */
    List<YcxCdcc> getCdccListByType(Integer type);
}