package com.bmh.project.evaluation.cdcc.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Table(name = "ycx_cdcc_dict")
public class YcxCdccDict implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1MDI，2PDI)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 月份
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 月龄最小天
     */
    @Column(name = "min_day")
    private Integer minDay;

    /**
     * 月龄最大天
     */
    @Column(name = "max_day")
    private Integer maxDay;

    /**
     * 原始分数
     */
    @Column(name = "orig_score")
    private String origScore;

    /**
     * 真实分数
     */
    @Column(name = "real_score")
    private String realScore;

    private static final long serialVersionUID = 1L;
}