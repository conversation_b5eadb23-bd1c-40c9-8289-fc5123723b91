package com.bmh.project.evaluation.cdcc.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_cdcc")
public class YcxCdcc implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1智力量表，2运动量表)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 月
     */
    @Column(name = "month")
    private Double month;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 顺序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 操作说明
     */
    @Column(name = "tips")
    private String tips;

    /**
     * 评分标准
     */
    @Column(name = "grading")
    private String grading;

    /**
     * 状态(0无效 1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选项
     */
    @Transient
    private List<YcxCdccOption> options;

    private static final long serialVersionUID = 1L;
}