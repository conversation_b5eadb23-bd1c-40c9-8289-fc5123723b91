package com.bmh.project.evaluation.cars.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cabs.model.YcxCabs;
import com.bmh.project.evaluation.cars.mapper.YcxCarsMapper;
import com.bmh.project.evaluation.cars.model.YcxCars;
import com.bmh.project.evaluation.cars.service.YcxCarsService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxCarsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 09:42:50
 */
@Service
public class YcxCarsServiceImpl extends BaseServiceImpl<YcxCars> implements YcxCarsService {
    @Resource
    private YcxCarsMapper ycxCarsMapper;

    @Override
    public List<YcxCars> getList() {
        return this.ycxCarsMapper.getList();
    }
}