package com.bmh.project.evaluation.cars.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.cars.mapper.YcxCarsSelectedMapper;
import com.bmh.project.evaluation.cars.model.YcxCarsSelected;
import com.bmh.project.evaluation.cars.service.YcxCarsSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxCarsSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 09:42:50
 */
@Service
public class YcxCarsSelectedServiceImpl extends BaseServiceImpl<YcxCarsSelected> implements YcxCarsSelectedService {
    @Resource
    private YcxCarsSelectedMapper ycxCarsSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxCarsSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    @Override
    public List<YcxCarsSelected> getSelecteds(Integer recordId) {
        return this.ycxCarsSelectedMapper.getSelecteds(recordId);
    }
}