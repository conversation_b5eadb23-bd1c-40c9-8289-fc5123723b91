package com.bmh.project.evaluation.cars.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.cars.mapper.YcxCarsResultMapper;
import com.bmh.project.evaluation.cars.model.YcxCarsResult;
import com.bmh.project.evaluation.cars.model.YcxCarsSelected;
import com.bmh.project.evaluation.cars.service.YcxCarsResultService;
import com.bmh.project.evaluation.cars.service.YcxCarsSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxCarsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月23日 09:42:50
 */
@Service("17")
public class YcxCarsResultServiceImpl extends BaseServiceImpl<YcxCarsResult> implements YcxCarsResultService, YcxResultService<YcxCarsResult> {
    @Resource
    private YcxCarsResultMapper ycxCarsResultMapper;
    @Resource
    private YcxCarsSelectedService carsSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxCarsResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxCarsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge()+"");
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        List<YcxCarsSelected> selecteds = result.getSelecteds();
        carsSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }

    @Override
    public YcxCarsResult getResult(Integer recordId) {
        Example example = new Example(YcxCarsResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxCarsResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            YcxCarsResult result = list.get(0);
            result.setSelecteds(carsSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxCarsResult result = new YcxCarsResult ();
        result.setRecom (recom);
        Example example = new Example (YcxCarsResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxCarsResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}