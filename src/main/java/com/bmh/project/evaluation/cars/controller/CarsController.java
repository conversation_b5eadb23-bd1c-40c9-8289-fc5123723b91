package com.bmh.project.evaluation.cars.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.cars.model.YcxCars;
import com.bmh.project.evaluation.cars.service.YcxCarsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * CARS 自闭症评定量表
 */
@RestController
@RequestMapping("cars")
public class CarsController {


    @Autowired
    private YcxCarsService carsService;

    /**
     * 获取问题列表
     * @return
     */
    @RequestMapping("getList")
    public Result getList(){
        List<YcxCars> list = carsService.getList();
        return ResultUtil.success(list);
    }




}
