package com.bmh.project.analysis.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.fastjson.JSON;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.util.DateUtil;
import com.bmh.project.aba.dto.AbaTargetDto;
import com.bmh.project.aba.mapper.AbaPlanProjectHistoryMapper;
import com.bmh.project.aba.model.AbaDailyGoalData;
import com.bmh.project.aba.service.AbaDailyGoalDataService;
import com.bmh.project.analysis.mapper.AbaAnalysisDomainMapper;
import com.bmh.project.analysis.model.AbaAnalysisDomain;
import com.bmh.project.analysis.service.AbaAnalysisDomainService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * AbaAnalysisDomainService对应的实现类
 *
 * <AUTHOR>
 * @date 2024年07月11日 17:07:44
 */
@Service
public class AbaAnalysisDomainServiceImpl extends BaseServiceImpl<AbaAnalysisDomain> implements AbaAnalysisDomainService {
    @Resource
    private AbaAnalysisDomainMapper abaAnalysisDomainMapper;
    @Resource
    private AbaDailyGoalDataService dailyDataService;
    @Resource
    private AbaPlanProjectHistoryMapper planProjectHistoryMapper;

    /**
     * 构建分析数据
     *
     * @param childId 儿童ID
     * @param date    日期
     */
    @Override
    public void buildAnalysisDomainData (Integer childId, Date date) {
        List<AbaDailyGoalData> dailyDataList = dailyDataService.getChildDataByDate (childId, date, 1);

        Map<Integer, List<AbaDailyGoalData>> dailyDataMap = dailyDataList.stream ().collect (Collectors.groupingBy (AbaDailyGoalData::getDomainId));
        dailyDataMap.forEach ((domainId, dataList) -> {
            Date lastModifyDate = planProjectHistoryMapper.getDomainLastModifyDate (domainId, date);
            AbaAnalysisDomain analysisDomain = new AbaAnalysisDomain ();
            analysisDomain.setDomainId (domainId);
            analysisDomain.setDomainName (dataList.get (0).getDomainName ());
            analysisDomain.setChildId (childId);
            analysisDomain.setDate (date);
            analysisDomain.setIsAdjust (DateUtil.isSameDate (lastModifyDate, date) ? 1 : 0);
            double score = 0 ;// dataList.stream ().mapToDouble (dailyData -> ((AbaTargetDto)JSON.parse(dailyData.getTargetResult())).getPassRate () * dailyData.getProjectLevel ()).sum ();
            int levelCoefCount = dataList.stream ().mapToInt (AbaDailyGoalData::getProjectLevel).sum ();
            analysisDomain.setDomainRate (NumberUtil.div (score, levelCoefCount, 2, RoundingMode.DOWN));
            abaAnalysisDomainMapper.insert (analysisDomain);
        });

    }

    /**
     * 以领域维度统计数据
     *
     * @param childId 儿童
     * @param range   近期天数，不传则为全部
     * @return
     */
    @Override
    public List<AbaAnalysisDomain> getList (Integer childId, Integer range) {

        Date firstDate = null;
        if (range != null) {
            List<Date> recentlyDate = abaAnalysisDomainMapper.getRecentlyDate (childId, range);
            if(CollectionUtil.isEmpty (recentlyDate)){
                return new ArrayList<> ();
            }
            firstDate = recentlyDate.get (recentlyDate.size () - 1);
        }

        return abaAnalysisDomainMapper.getList (childId, firstDate);
    }
}