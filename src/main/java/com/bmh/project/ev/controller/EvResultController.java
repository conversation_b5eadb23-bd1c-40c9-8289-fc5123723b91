package com.bmh.project.ev.controller;

import cn.hutool.core.util.ObjectUtil;
import com.bmh.common.base.BaseQuery;
import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.ev.dto.*;
import com.bmh.project.ev.model.EvResultVbmapp;
import com.bmh.project.ev.service.EvResultService;
import com.bmh.project.ev.service.EvResultVbmappService;
import com.bmh.project.ev.vo.*;
import com.github.pagehelper.PageInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 评估接口
 */
@RestController
@RequestMapping("/ev/result")
public class EvResultController {
    @Resource
    private EvResultService evResultService;
    @Resource
    private EvResultVbmappService vbmappService;

    /**
     * 创建/修改评估
     * @param evaResultAddDto 评估参数
     * @return 评估id 评估模板
     */
    @PostMapping("/add")
    public Result<?> addEvResult(@RequestBody EvResultAddDto evaResultAddDto) {
        //操作人员所在机构
        evaResultAddDto.setOrgId(SecurityUtil.getOrgId ());
        return evResultService.addEvResult(evaResultAddDto);
    }

    /**
     * 分页获得评估记录
     * @param query 分页数据
     * @param childrenId 儿童id
     * @return 评估结果列表
     */
    @GetMapping("/pageList")
    public Result<PageInfo<ResultPageListVo>>  pageList(BaseQuery query, Integer childrenId){
        PageInfo<ResultPageListVo> pageInfo =  evResultService.pageList(query,childrenId);
        return ResultUtil.success(pageInfo);
    }

    /**
     * 创建IEP评估 (第1步)
     * @param resultCreateDto 评估参数
     * @return 评估id 评估模板
     */
    @PostMapping("/createResult")
    public Result<?> createResult(@RequestBody EvResultCreateDto resultCreateDto) {
        //操作人员所在机构
        resultCreateDto.setOrgId(SecurityUtil.getOrgId ());
        Integer resultId =  evResultService.createResult(resultCreateDto);
        return ResultUtil.success(resultId);
    }

    /**
     * 记录IEP评估测评信息  (第2步 ： 如果是vbmapp ，录入测评信息，如果以前存在结果 回显相关录入的结果)
     * @param resultId
     * @return 评估id 评估模板+记录的结果
     */
    @PostMapping("/recordVbResult")
    public Result<?> recordVbResult(Integer resultId) {
        if (ObjectUtil.isEmpty(resultId)){
           return  ResultUtil.error("参数resultId不能为空");
        }
        return evResultService.recordVbResult(resultId);
    }

    /**
     * 选择分数项保存 (第3步 ： 如果是vbmapp ，每次保存或修改选题结果)
     * @param scoreDto
     * @return 评估档案id 评估模板
     */
    @PostMapping("/recordVbScore")
    public Result<?> recordVbScore(@Validated @RequestBody EvResultVbScoreDto scoreDto) {
        Integer projectAnswerId =  evResultService.recordVbScore(scoreDto);
        //自动计算转衔中的前5项分数
        //404	VB-MAPP里程碑评估得分
        //405	VB-MAPP障碍评估总分
        //406	VB-MAPP障碍评估中不良行为和教学控制的得分
        //407	VB-MAPP里程碑评估中教室常规和集体技能的得分
        //408	VB-MAPP里程碑评估中社会行为和社会游戏的得分
        evResultService.autoCalcVbTransferScore(scoreDto);
        return ResultUtil.success(projectAnswerId);
    }

    /**
     * 录入评估测评结果后提交AI (第4步 ： 除障碍外，未选项目给0分，同时把草稿修改为发布ev_result ->status = 1 )
     * @param resultDto 评估参数
     * @return 评估id 评估模板
     */
    @PostMapping("/addOrUpdateVbResult")
    public Result<?> addOrUpdateVbResult(@RequestBody EvResultVbDto resultDto) {
        return evResultService.addOrUpdateVbResult(resultDto);
    }

    /**
     * 创建Vb评估报告(第5步 ：有自身计算的，有需要AI计算后保存的数据)
     * @param resultId 评估参数
     * @return 评估id 评估模板
     */
    @PostMapping("/createVbReport")
    public Result<?> createVbReport(Integer resultId){
        if (ObjectUtil.isEmpty(resultId)){
            return  ResultUtil.error("参数resultId不能为空");
        }
        //result_id 和 报告的result_id 是一一对应的
        Integer reportId = evResultService.createVbReport(resultId);
        return ResultUtil.success(reportId);
    }

    /**
     * 修改报告补充信息，(post，json 格式)
     * @param complementDto 补充信息参数
     * @return 报告id
     */
    @PostMapping("/complementVbReport")
    public Result<?> complementVbReport(@RequestBody EvResultVbComplementDto complementDto){
        Integer reportId = evResultService.complementVbReport(complementDto);
        return ResultUtil.success(reportId);
    }

    /**
     * 评估报告展示的栏目 (显示可以展示的栏目标签，如果不选择障碍题目，整个都不要了)
     * @param reportId 报告id
     * @return 需要展示的栏目
     */
    @GetMapping("/showReportColumn")
    public Result<List<VbReportColumnVo>> showReportColumn(Integer reportId){
        if (ObjectUtil.isEmpty(reportId)){
            return  ResultUtil.error("参数reportId不能为空");
        }
        List<VbReportColumnVo> columnList  =  vbmappService.showReportColumn(reportId);
        return ResultUtil.success(columnList);
    }

    /**
     * 评估报告概况
     * @param reportId 报告id
     * @return VbReportOverviewVo
     */
    @GetMapping("/showReportOverview")
    public Result<VbReportOverviewVo> showReportOverview(Integer reportId){
        if (ObjectUtil.isEmpty(reportId)){
            return  ResultUtil.error("参数reportId不能为空");
        }
        VbReportOverviewVo reportOverview  =  vbmappService.showReportOverview(reportId);
        return ResultUtil.success(reportOverview);
    }

    /**
     * 里程碑评估报告评分
     * @param reportId 报告id
     * @return EvAddResultVo
     */
    @GetMapping("/showReportMileStoneScore")
    public Result<List<MilestoneScoreVo>> showReportMileStoneScore(Integer reportId){
        if (ObjectUtil.isEmpty(reportId)){
            return  ResultUtil.error("参数reportId不能为空");
        }
        List<MilestoneScoreVo>  scoreVoList  =  vbmappService.showReportMileStoneScore(reportId);
        return ResultUtil.success(scoreVoList);
    }

    /**
     * 评估报告评分
     * @param reportId 报告id
     * @param columnId 栏目id
     * @return EvAddResultVo
     */
    @GetMapping("/showReportScore")
    public Result<List<EvDomainTemplateVo>> showReportScore(Integer reportId, Integer columnId){
        if (ObjectUtil.isEmpty(reportId)){
            return  ResultUtil.error("参数reportId不能为空");
        }
        if (ObjectUtil.isEmpty(columnId)){
            return  ResultUtil.error("参数columnId不能为空");
        }
        List<EvDomainTemplateVo>  resultListVo  =  vbmappService.showReportScore(reportId,columnId);
        return ResultUtil.success(resultListVo);
    }

    /**
     * 评估报告
     * @param reportId 报告id
     * @param columnId 栏目id
     * @return VbReportContentVo
     */
    @GetMapping("/showReportContent")
    public Result<VbReportContentVo> showReportContent(Integer reportId, Integer columnId){
        if (ObjectUtil.isEmpty(reportId)){
            return  ResultUtil.error("参数reportId不能为空");
        }
        if (ObjectUtil.isEmpty(columnId)){
            return  ResultUtil.error("参数columnId不能为空");
        }
        VbReportContentVo  contentVot  =  vbmappService.showReportContent(reportId,columnId);
        return ResultUtil.success(contentVot);
    }

    /**
     * 修改报告中指定内容 (post，json 格式)
     * @param reportDto 补充信息参数
     * @return 报告id
     */
    @PostMapping("/updateVbReport")
    public Result<?> updateVbReport(@RequestBody EvResultVbUpdateDto reportDto) {
        if (ObjectUtil.isNull(reportDto)) {
            return ResultUtil.error("参数reportDto不能为空");
        }
        if (ObjectUtil.isEmpty(reportDto.getReportId())) {
            return ResultUtil.error("参数reportId不能为空");
        }
        if (ObjectUtil.isEmpty(reportDto.getColumnId())) {
            return ResultUtil.error("参数columnId不能为空");
        }
        String result = evResultService.updateVbReport(reportDto);
        return ResultUtil.success(result);
    }

    /**
     * 修改报告中总览里程碑指定内容 (post，json 格式)
     * @param reportDto 总览里程碑指定内容
     * @return 报告id
     */
    @PostMapping("/updateSmAssessResult")
    public Result<?> updateSmAssessResult(@RequestBody VbReportUpdateSmDto reportDto) {
        if (ObjectUtil.isNull(reportDto)) {
            return ResultUtil.error("参数reportDto不能为空");
        }
        if (ObjectUtil.isEmpty(reportDto.getReportId())) {
            return ResultUtil.error("参数reportId不能为空");
        }
        String result = evResultService.updateSmAssessResult(reportDto);
        return ResultUtil.success(result);
    }

    /**
     * 修改报告中总览里转衔指定内容 (post，json 格式)
     * @param reportDto 总览里程碑指定内容
     * @return 报告id
     */
    @PostMapping("/updateTransitionAssessResult")
    public Result<?> updateTransitionAssessResult(@RequestBody VbReportUpdateTransDto reportDto) {
        if (ObjectUtil.isNull(reportDto)) {
            return ResultUtil.error("参数reportDto不能为空");
        }
        if (ObjectUtil.isEmpty(reportDto.getReportId())) {
            return ResultUtil.error("参数reportId不能为空");
        }
        String result = evResultService.updateTransitionAssessResult(reportDto);
        return ResultUtil.success(result);
    }

    /**
     * 获取补充信息
     * @param reportId 报告ID
     * @return 补充信息
     */
    @GetMapping("/supplement")
    public Result<?> getSupplementInfo(Integer reportId){
        EvResultVbmapp evResultVbmapp = evResultService.getSupplementInfo(reportId);
        return ResultUtil.success(evResultVbmapp);
    }

    /**
     * 创建VbMAPP的IEP  (post 方式 )
     * @param reportId 报告ID
     * @return IEP生成的参数，并放入Redis
     */
    @PostMapping("/createVbIEP")
    public Result<?> createVbIEP(Integer reportId) {
        return evResultService.createVbIEP(reportId);
    }

    /**
     * 下载报告
     * @param reportId 报告id
     * @param type 0 下载报告 1 合并报告
     * @return
     */
    @PostMapping("/downloadVbReport")
    public Result<?> downloadVbReport(Integer reportId,Integer type){
        String s = (String) evResultService.exportVBMAPPReport (reportId,type);
        return ResultUtil.success (s);
    }

}
