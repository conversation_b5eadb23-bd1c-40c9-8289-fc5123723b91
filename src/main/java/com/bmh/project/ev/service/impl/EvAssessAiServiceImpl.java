package com.bmh.project.ev.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.common.util.JsonHelper;
import com.bmh.project.ev.dto.EvResultAddDto;
import com.bmh.project.ev.dto.VbReportUpdateDto;
import com.bmh.project.ev.mapper.EvResultVbmappMapper;
import com.bmh.project.ev.model.EvResultVbmapp;
import com.bmh.project.ev.service.EvAssessAiService;
import com.bmh.project.ev.vo.*;
import io.github.pigmesh.ai.deepseek.core.DeepSeekClient;
import io.github.pigmesh.ai.deepseek.core.chat.ChatCompletionRequest;
import io.github.pigmesh.ai.deepseek.core.chat.ChatCompletionResponse;
import io.github.pigmesh.ai.deepseek.core.chat.ResponseFormatType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EvAssessAiServiceImpl implements EvAssessAiService {

    @Value ("${deepseek.temperature}")
    private Double temperature;

    @Value ("${deepseek.token.v3}")
    private Integer maxCompletionTokensV3;

    @Value (("${deepseek.model.v3}"))
    private String deepSeekModelV3;

    @Resource
    private DeepSeekClient deepSeekClient;

    @Resource
    private EvResultVbmappMapper evResultVbmappMapper;

    private static final String[] DIGITS = {"一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二", "十三", "十四", "十五", "十六"};

    /**
     * AI生成VBMAPP评估结果
     *
     * @param evResultAddDto VBMAPP评估详情
     */
    @Override
    public JSONArray createVBMAPPAssessResult (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getAssessInfo (evResultAddDto);
        assessInfo = assessInfo
            + "你将收到儿童 VB-MAPP 评估得分数据，每道题附有得分和阶段。\n" +
            "\n" +
            "你必须基于下述【唯一标准】判断每个领域的发育阶段，并输出一个规范 JSON。\n" +
            "\n" +
            "---\n" +
            "\n" +
            "【发育阶段划分】（只能使用以下表述）：\n" +
            "- 第1阶段（0～18个月）\n" +
            "- 第2阶段（18～30个月）\n" +
            "- 第3阶段（30～48个月）\n" +
            "\n" +
            "---\n" +
            "\n" +
            "【领域所属初始阶段】：\n" +
            "- 第1阶段起始领域（9个）：提要求、命名、听着反应、视觉配对、独立游戏、社交、模仿、仿说、发音\n" +
            "- 第2阶段起始领域（4个）：LRCFFC、对话、集体技能、语言结构\n" +
            "- 第3阶段起始领域（3个）：阅读、书写、算数\n" +
            "- 发音：只有第1阶段\n" +
            "- 模仿、仿说：没有第3阶段\n" +
            "\n" +
            "---\n" +
            "\n" +
            "【发育阶段判断规则】：\n" +
            "- 若第一阶段为满分（5分），第二阶段 > 2.5，则处于第2阶段\n" +
            "- 若第1和第2阶段满分，第3阶段 = 0，则处于第2阶段\n" +
            "- 若第1和第2阶段满分，第3阶段 > 0，则处于第3阶段\n" +
            "- 若三阶段都未满分，得分最高的阶段为该领域阶段\n" +
            "- 其余情况根据上述规则推导\n" +
            "\n" +
            "---\n" +
            "\n" +
            "【输出要求】：\n" +
            "1. 输出完整合法 JSON 数组，格式如下：\n" +
            "[\n" +
            "  {\n" +
            "    \"domainId\": \"0\",\n" +
            "    \"domainName\": \"总体发育水平\",\n" +
            "    \"level\": \"第X阶段（X～X个月）\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"domainId\": \"原始ID\",\n" +
            "    \"domainName\": \"原始名称\",\n" +
            "    \"level\": \"第X阶段（X～X个月）\"\n" +
            "  }\n" +
            "]\n";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }

    /**
     * AI生成转衔评估结果
     *
     * @param evResultAddDto 转衔评估详情
     */
    @Override
    public JSONArray createTransitionAssessResult (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getTransitionAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "请根据上述转衔评估的具体内容分析儿童在下述三类转衔的结果：\n\n"
            + "三类转衔划分标准如下：\n"
            + "1. 第一类转衔（题目1-6）：VB-MAPP得分和学业独立性\n"
            + "   - 评估等级：优秀、中等、不足\n\n"
            + "2. 第二类转衔（题目7-12）：学习模式\n"
            + "   - 评估等级：优秀、中等、不足\n\n"
            + "3. 第三类转衔（题目13-18）：自我帮助、自发性和自我指导\n"
            + "   - 评估等级：优秀、中等、不足\n\n"
            + "请按照以下格式返回最终结果：\n"
            + "[{'one':'评估结果'},{'two':'评估结果'},{'three':'评估结果'}]\n"
            + "说明：\n"
            + "- 'one'代表'第一类转衔'\n"
            + "- 'two'代表'第二类转衔'\n"
            + "- 'three'代表'第三类转衔'\n"
            + "注意："
            + "- 只需返回JSON格式数据，不要包含任何其他内容或解释。"
            + "- 最终输出必须是完整、合法、规范的JSON";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }

    /**
     * AI生成VBMAPP里程碑目标
     *
     * @param evResultAddDto VBMAPP评估详情
     */
    @Override
    public JSONArray createVBMAPPSMTarget (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "请根据上述VB-MAPP评估结果，为每个评估领域制定3-6个未来三个月的干预目标。要求如下：\n\n"
            + "1. 目标制定原则：\n"
            + "   - 基于当前评估结果\n"
            + "   - 符合儿童发展阶段\n"
            + "   - 具体可测量\n"
            + "   - 3个月内可实现\n\n"
            + "2. 数据格式要求：\n"
            + "[\n"
            + "  {\n"
            + "    \"domainId\": 领域编号,\n"
            + "    \"domainName\": \"领域名称\",\n"
            + "    \"target\": [\"目标1\",\"目标2\",\"目标3\",\"目标4\",\"目标5\",\"目标6\"]\n"
            + "  }\n"
            + "]\n\n"
            + "3. 注意事项：\n"
            + "   - 仿说领域生成发音相关的目标，例：孩子能够根据指令仿说至少5个元音、复合元音或辅音"
            + "   - 每个领域至少3个目标，最多6个目标\n"
            + "   - 目标描述要简洁明确，使用动宾短语\n"
            + "   - 严格使用双引号，不要包含任何注释\n"
            + "   - 仅返回标准JSON格式数据"
            + "   - 最终输出必须是完整、合法、规范的JSON";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }

    /**
     * AI生成VBMAPP里程碑建议
     *
     * @param evResultAddDto VBMAPP评估详情
     */
    @Override
    public JSONArray createVBMAPPSMPropose (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "根据VB-MAPP评估结果，请为每个领域提供专业、具体的干预建议。要求如下：\n\n"
            + "1. 建议内容要求：\n"
            + "   - 基于该领域评估结果\n"
            + "   - 符合儿童当前发展水平\n"
            + "   - 具有可操作性\n"
            + "   - 包含具体干预方向\n\n"
            + "2. 数据格式规范：\n"
            + "[\n"
            + "  {\n"
            + "    \"domainId\": 领域编号,\n"
            + "    \"domainName\": \"领域名称\",\n"
            + "    \"propose\": \"200-300字的详细干预建议，包含具体方法和预期目标\"\n"
            + "  }\n"
            + "]\n\n"
            + "3. 专业要求：\n"
            + "   - 使用专业术语但表述清晰\n"
            + "   - 建议需包含：\n"
            + "     * 当前能力分析\n"
            + "     * 具体干预策略\n"
            + "4. 输出要求：\n"
            + "   - 仅返回标准JSON格式\n"
            + "   - 不使用任何注释或说明文字\n"
            + "   - 最终输出必须是完整、合法、规范的JSON";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }

    /**
     * AI生成障碍评估题目建议
     *
     * @param evResultAddDto 障碍评估详情
     */
    @Override
    public JSONArray createObstacleAssessSubjectPropose (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getObstacleAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "根据障碍评估结果，请为每个评估项目生成现状分析及专业干预建议。具体要求如下：\n\n"
            + "1. 数据内容要求：\n"
            + "   - 现状描述(statusSuggest)：\n"
            + "     * 客观陈述孩子在该项目的表现\n"
            + "     * 不超过50字\n"
            + "   - 教学建议(statusSuggest)：\n"
            + "     * 针对下季度(未来三个月)个别化教学建议\n"
            + "     * 包含具体可操作的干预策略\n"
            + "     * 200字以内\n\n"
            + "2. 专业规范：\n"
            + "   - 符合特殊教育原则\n"
            + "3. 数据格式：\n"
            + "[{\n"
            + "  \"projectId\": \"项目编号\",\n"
            + "  \"projectName\": \"项目名称\",\n"
            + "  \"statusSuggest\": \"现状描述(表现)...教学建议：1. 具体方法... 2. 重点目标...\"\n"
            + "}]\n\n"
            + "4. 输出要求：\n"
            + "   - 严格符合JSON格式规范\n"
            + "   - 不使用任何注释/说明\n"
            + "   - 确保所有字段使用双引号\n"
            + "   - 最终输出必须是完整、合法、规范的JSON";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }


    /**
     * AI生成障碍评估建议
     *
     * @param evResultAddDto 障碍评估详情
     */
    @Override
    public JSONArray createObstacleAssessPropose (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getObstacleAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "根据障碍评估结果，请生成一份结构化专业报告。要求如下：\n\n"
            + "1. 报告结构要求：\n"
            + "   [\n"
            + "     {\n"
            + "       \"title\": \"1. 总体概述\",\n"
            + "       \"content\": \"①简明概括主要障碍类型 ②按严重程度排序 ③突出亟待解决问题\"\n"
            + "     },\n"
            + "     {\n"
            + "       \"title\": \"2. 分项描述\",\n"
            + "       \"content\": \"①分领域说明障碍表现 ②每个描述必须包含：\n"
            + "                   - 具体行为指标（频率/强度/持续时间）\n"
            + "                   - 评估中观察到的真实实例\n"
            + "                   - 对日常生活的影响程度\"\n"
            + "     },\n"
            + "     {\n"
            + "       \"title\": \"3. 干预建议\",\n"
            + "       \"content\": \"①每个建议应包含：\n"
            + "                   - 专业术语的通俗化解释\n"
            + "                   - 可分步骤说明的操作方法（3-5步）\n"
            + "                   - 家庭可实施的简易训练示例\n"
            + "                   - 预期改善指标\"\n"
            + "     },\n"
            + "     {\n"
            + "       \"title\": \"4. 训练优先级\",\n"
            + "       \"content\": \"①按紧急程度排序的干预项目\n"
            + "                   ②排序依据（安全/发展/社交等维度）\n"
            + "                   3个月内的重点目标\"\n"
            + "     }\n"
            + "   ]\n\n"
            + "2. 专业规范：\n"
            + "   - 建议需符合循证实践原则\n\n"
            + "3. 内容要求：\n"
            + "   - 总体概述：300字以内\n"
            + "   - 分项描述：每项200-300字\n"
            + "   - 干预建议：每项提供2-3个具体方案\n"
            + "   - 结语：100-150字\n\n"
            + "4. 输出要求：\n"
            + "   - 严格使用指定JSON格式\n"
            + "   - content字段为纯文本（不含markdown格式）\n"
            + "   - 确保JSON语法正确\n"
            + "   - 仅返回数据结构，无额外说明"
            + "   - 最终输出必须是完整、合法、规范的JSON";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }

    /**
     * AI生成转衔评估题目建议
     *
     * @param evResultAddDto 转衔评估详情
     */
    @Override
    public JSONArray createTransitionAssessSubjectPropose (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getTransitionAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "根据转衔评估结果，请为每个评估项目提供专业转衔安置建议。具体要求如下：\n\n"
            + "1. 建议内容要求：\n"
            + "   - 基于项目得分分析当前能力水平\n"
            + "   - 提出具体、可操作的转衔安置方案\n"
            + "   - 包含短期（3个月内）过渡目标\n"
            + "   - 说明建议安置环境（如普通班级/资源教室/特教班级等）\n"
            + "   - 第一类转衔（1-6题）\n"
            + "   - 第一类转衔（7-12题）\n"
            + "   - 第一类转衔（13-18题）\n\n"
            + "2. 专业规范：\n"
            + "   - 符合IEP转衔计划要求\n"
            + "   - 参考融合教育最佳实践\n"
            + "   - 建议需包含：\n"
            + "     * 当前能力基准\n"
            + "     * 推荐安置环境\n"
            + "     * 必要支持措施\n"
            + "     * 预期适应指标\n\n"
            + "3. 数据格式：\n"
            + "[{\n"
            + "  \"projectId\": \"评估项目编号\",\n"
            + "  \"projectName\": \"评估项目名称\",\n"
            + "  \"suggest\": \"①当前水平说明（50字）→②推荐安置环境→③具体支持策略（3-5条）\"\n"
            + "}]\n\n"
            + "4. 输出要求：\n"
            + "   - 只有第一类转衔需要生成建议，其他不需要\n"
            + "   - 严格符合JSON格式规范\n"
            + "   - 每条建议控制在150-200字\n"
            + "   - 使用专业但易懂的表述\n"
            + "   - 仅返回有效JSON数据，不含任何注释";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }


    /**
     * AI生成转衔评估建议
     *
     * @param evResultAddDto 转衔评估详情
     */
    @Override
    public JSONArray createTransitionAssessPropose (EvResultAddDto evResultAddDto) {
        //评估信息
        String assessInfo = getTransitionAssessInfo (evResultAddDto);
        assessInfo = assessInfo + "根据转衔评估结果，请生成一份结构化转衔评估报告。具体要求如下：\n\n"
            + "1. 报告结构要求：\n"
            +"[\n" +
            "  {\n" +
            "    \"title\": \"1. 总体概述\",\n" +
            "    \"content\": \"①概括转衔评估总体表现 ②明确优势领域 ③指出需调整的教育环境/教学方式 ④使用具体评估数据支持结论\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"title\": \"2. 分项描述\",\n" +
            "    \"content\": \"①按VB-MAPP转衔领域分类说明 ②每个领域必须包含：\\n- 具体评估得分和百分位\\n- 实际观察到的典型行为实例\\n- 该领域对转衔的影响分析\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"title\": \"3. 转衔建议\",\n" +
            "    \"content\": \"①每个建议应包含：\\n- 具体可操作的转衔准备活动\\n- 环境调整建议（物理环境/教学支持）\\n- 分步骤的家庭实施方案（3-5步）\\n- 预期转衔时间节点\"\n" +
            "  },\n" +
            "  {\n" +
            "    \"title\": \"4. 优先级建议\",\n" +
            "    \"content\": \"①按转衔准备紧迫性排序\\n②排序依据（社交/学业/适应等维度）\\n③3-6个月的转衔路线图\"\n" +
            "  }\n" +
            "]\n"
            + "2. 专业规范：\n"
            + "   - 使用正向行为支持策略\n\n"
            + "3. 内容要求：\n"
            + "   - 总体概述：300字以内，包含具体评估数据\n"
            + "   - 分项描述：每个转衔领域200-300字\n"
            + "   - 转衔建议：每项提供2-3个具体方案\n"
            + "   - 优先级建议：150-200字，包含时间节点\n\n"
            + "4. 输出要求：\n"
            + "   - 严格使用指定JSON格式\n"
            + "   - content字段为纯文本（不含markdown格式）\n"
            + "   - 确保JSON语法完全正确\n"
            + "   - 仅返回数据结构，无额外说明或注释";
        JSONArray jsonArray = deepSeekSend (assessInfo, deepSeekModelV3);
        return jsonArray;
    }

    /**
     * AI生成VBMAPP里程碑框架
     *
     * @param evResultAddDto VBMAPP里程碑评估内容
     */
    @Override
    @Transactional
    public VBMAPPReportVo createVBMAPPResult (EvResultAddDto evResultAddDto){
        VBMAPPReportVo vbmappReportVo = new VBMAPPReportVo ();
        if (CollectionUtil.isNotEmpty (evResultAddDto.getEvDomainTemplateVoList ())){
            //获取评估领域分数
            Map<Integer, BigDecimal> domainTotalScore = evResultAddDto
                .getEvDomainTemplateVoList ()
                .stream ()
                .collect (Collectors.toMap (EvDomainTemplateVo::getDomainId, EvDomainTemplateVo::getTotalScore));
            //获取评估领域总分
            Map<Integer, BigDecimal> domainScore = evResultAddDto
                .getEvDomainTemplateVoList ()
                .stream ()
                .collect (Collectors.toMap (EvDomainTemplateVo::getDomainId, EvDomainTemplateVo::getScore));
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            List<SmAssessResultVo> smAssessResultVoList = JSONUtil.toList (evResultVbmapp.getMilestoneSummary (), SmAssessResultVo.class);
            String resultLevel = "";
            for (SmAssessResultVo smAssessResultVo : smAssessResultVoList) {
                if (smAssessResultVo.getId () == 0){
                    resultLevel = smAssessResultVo.getLevel ();
                }
            }
            String first = "· VB-MAPP里程碑评估包含了170个关键里程碑，涵盖了包括提要求、命名等16类学习和语言技能。" +
                "根据正常孩子的发展进程，里程碑们被划分为三个阶段（0～18个月、18～30个月和30～48月）。\n\n·";
            String second = StrUtil.format (" {}在此次VB-MAPP里程碑评估中得分 {}分（总分为170分）。就总体的语言发育来说，目前相当于正常孩子{}的水平。"
                , evResultAddDto.getChildrenName (), evResultAddDto.getTotalScore (), resultLevel);
            String third = "详情请看VB-MAPP里程碑计分总表。此份报告会对被评估的技能做单项分析，并推荐下一季度（未来三个月）的个别化教学目标（IEP Goals）。" +
                "最终落实的教学目标需要由家长和老师视孩子的实际情况而定。";
            VBMAPPReportHeadVo vbmappReportHeadVo = new VBMAPPReportHeadVo ();
            vbmappReportHeadVo.setFirst (first);
            vbmappReportHeadVo.setSecond (second);
            vbmappReportHeadVo.setThird (third);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = new ArrayList<> ();
            for (EvDomainTemplateVo evDomainTemplateVo : evResultAddDto.getEvDomainTemplateVoList ()) {
                VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo = new VBMAPPAssessTargetSuggestVo ();
                vbmappAssessTargetSuggestVo.setId (evDomainTemplateVo.getDomainId ());
                vbmappAssessTargetSuggestVo.setName (evDomainTemplateVo.getDomainName ());
                vbmappAssessTargetSuggestVo.setScore ("得分："+domainTotalScore.get (evDomainTemplateVo.getDomainId ())+"分（满分"+domainScore.get (evDomainTemplateVo.getDomainId ())+"分）");
                vbmappAssessTargetSuggestVo.setTargetName ("目标（未来三个月）");
                vbmappAssessTargetSuggestVo.setTargetContent ("");
                vbmappAssessTargetSuggestVo.setOpinion ("");
                vbmappAssessTargetSuggestVoList.add (vbmappAssessTargetSuggestVo);
            }
            vbmappReportVo.setHead (vbmappReportHeadVo);
            vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
            vbmappReportVo.setSuggest ("");
            evResultVbmapp.setMilestoneReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmappMapper.updateByPrimaryKeySelective (evResultVbmapp);
        }
        return vbmappReportVo;
    }

    /**
     * AI生成VBMAPP转衔框架
     *
     * @param evResultAddDto VBMAPP转衔评估内容
     */
    @Override
    @Transactional
    public VBMAPPReportVo createVBMAPPTransitionResult (EvResultAddDto evResultAddDto) {
        VBMAPPReportVo vbmappReportVo = new VBMAPPReportVo ();
        if (CollectionUtil.isNotEmpty (evResultAddDto.getEvDomainTemplateVoList ())) {
            //项目得分
            Map<Integer, Double> projectScore = evResultAddDto.getEvDomainTemplateVoList ().get (0).getEvDomainProjectTemplateVoList ().stream ().collect (Collectors.toMap (EvDomainProjectTemplateVo::getDomainProjectId, EvDomainProjectTemplateVo::getScore));
            //报告头
            String first = "· VB-MAPP转衔评估涵盖了18个领域，用于为孩子的总体技能和现有学习能力提供客观的评价。" +
                "孩子的个别化教育团队可以参考转衔评估中的信息，决定适合孩子的教育环境（比如说个训VS组训，是否安置于融合班）和教学模式（比如早期密集型教学，半日制技能特训）。\n\n"+
                "· 转衔评估中的项目可以分为三大类。" +
                "第一类（1～6项）涵盖了孩子的语言技能、社交技能、学习自主性，和潜在的学习和语言障碍。" +
                "第二类（7～12项）涵盖了孩子具体的学习形式。" +
                "第三类（13～18项）涵盖了自理能力，自发性和独立性。\n\n·";
            String second = StrUtil.format (" {}在此次VB-MAPP转衔评估中得分 {}分（总分为90分，每题5分）。", evResultAddDto.getChildrenName (), evResultAddDto.getTotalScore ());
            String third = "此报告会对转衔评估中的每个项目做单项分析，并推荐下一季度（未来三个月）的个别化教学中应该做出的考虑。" +
                "最终落实的教学方案需要由老师老师和家长视孩子的实际情况而定。";
            VBMAPPReportHeadVo vbmappReportHeadVo = new VBMAPPReportHeadVo ();
            vbmappReportHeadVo.setFirst (first);
            vbmappReportHeadVo.setSecond (second);
            vbmappReportHeadVo.setThird (third);
            //评估题目建议
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = new ArrayList<> ();
            for (EvDomainProjectTemplateVo evDomainProjectTemplateVo : evResultAddDto
                .getEvDomainTemplateVoList ().get (0)
                .getEvDomainProjectTemplateVoList ()) {
                VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo = new VBMAPPAssessTargetSuggestVo ();
                vbmappAssessTargetSuggestVo.setId (evDomainProjectTemplateVo.getDomainProjectId ());
                vbmappAssessTargetSuggestVo.setName (evDomainProjectTemplateVo.getShortTitle ());
                vbmappAssessTargetSuggestVo.setScore ("得分："+projectScore.get (evDomainProjectTemplateVo.getDomainProjectId ())+"分");
                vbmappAssessTargetSuggestVo.setOpinion (StrUtil.format ("就该单项而言：此次得分为{}分。" +
                        "单项最高分为5分；" +
                        "分数越高，说明孩子在里程碑评估中所表现出的能力越高， 在限制较少的教学环境中的学习能力越高。",
                    projectScore.get (evDomainProjectTemplateVo.getDomainProjectId ())));
                if(evDomainProjectTemplateVo.getDomainProjectId () == 404){
                    vbmappAssessTargetSuggestVo.setTargetContent ("如果孩子的里程碑得分相当于正常孩子0～18个月（第一阶段）的水平，则需要落实密集地、以早期言语能力为主的教学计划。和同龄人（特别是正常儿童）的互动可以穿插在日常教学活动里，但不建议拿大部分时间来做此类活动。密集型的言语训练才是目前教学计划的重中之重。 \n" +
                        "如果孩子的里程碑得分相当于正常孩子18～30个月（第二阶段）的水平，则需要落实集体训加个训、以贴近自然情景为主的教学计划。处于第二阶段的孩子需要参加一些形式较为松散的集体课和其它活动（如：美工课、音乐课、做游戏），尽管如此，大部分的教学时间和精力仍然应该投入到高强度、密集型的言语训练中。除此之外，建议增加孩子与同龄人（尤其是言语水平较好的同龄人）的互动机会。 \n" +
                        "如果孩子的里程碑得分相当于正常孩子30～48个月（第三阶段）的水平，则需要在限制较少的环境中（如：普校、融合班）落实教学计划。处于第三阶段的孩子已经具备了提问题（对信息提要求）、主动描述环境（命名）、对话（交互式语言）等高阶言语能力，适合在有极少限制、非密集教学的环境中学习。建议尽量还原普通幼儿园或者学前班的教学环境和活动内容，必要时可以加上适当辅助或行为管理系统（比如：视觉提示、代币制）");
                }else {
                    vbmappAssessTargetSuggestVo.setTargetContent ("");
                }
                if (evDomainProjectTemplateVo.getNo () >= 1 && evDomainProjectTemplateVo.getNo () <= 6){
                    vbmappAssessTargetSuggestVo.setType (1);
                    vbmappAssessTargetSuggestVo.setTargetName ("基于孩子得分的阶段性转衔安置建议如下：");
                }else if (evDomainProjectTemplateVo.getNo () >= 7 && evDomainProjectTemplateVo.getNo () <= 12){
                    vbmappAssessTargetSuggestVo.setType (2);
                }else {
                    vbmappAssessTargetSuggestVo.setType (3);
                }
                vbmappAssessTargetSuggestVoList.add (vbmappAssessTargetSuggestVo);
            }
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            vbmappReportVo.setHead (vbmappReportHeadVo);
            vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
            vbmappReportVo.setSuggest ("");
            evResultVbmapp.setTransferReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmapp.setUpdateTime (new Date ());
            //原来更新方式，当非空数据时存在不能更新的情况，原因未明，其他报告字段可以用下面方式更新
            //evResultVbmappMapper.updateByPrimaryKeySelective (evResultVbmapp);
            //新增加update
            evResultVbmappMapper.updateTransferReport(evResultVbmapp);
        }
        return vbmappReportVo;
    }

    /**
     * AI生成VBMAPP障碍框架
     *
     * @param evResultAddDto VBMAPP障碍评估内容
     */
    @Override
    @Transactional
    public VBMAPPReportVo createVBMAPPObstacleResult (EvResultAddDto evResultAddDto) {
        VBMAPPReportVo vbmappReportVo = new VBMAPPReportVo ();
        if (CollectionUtil.isNotEmpty (evResultAddDto.getEvDomainTemplateVoList ())) {
            //筛选需展示的题目
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVoList = evResultAddDto
                .getEvDomainTemplateVoList ()
                .get (0)
                .getEvDomainProjectTemplateVoList ()
                .stream ()
                .filter (vo->StrUtil.isNotBlank (vo.getOpinion ()))
                .collect(Collectors.toList());
            //获取每道题目得分
            Map<Integer, Double> projectScore = evDomainProjectTemplateVoList.stream ()
                .collect (Collectors
                    .toMap (EvDomainProjectTemplateVo::getDomainProjectId, EvDomainProjectTemplateVo::getScore));
            //评估报告头
            String first = "· 个别化教育（IEP）中，家长和老师在提示孩子技能的同时，也要扫除影响孩子进步的障碍。\n\n· VB-MAPP 障碍评估涵盖了24个可能影响孩子教学成的障碍，以便家长和老师了解孩子的需求，有针对性地教育和干预。"+
                "这些障碍主要来自以下因素——行为问题（例伤人、自伤、自我刺激行为），言语缺陷（例提要求能力不足），社交缺陷（例动机不足），学习障碍（例无法泛化、过分依赖辅助）和身体原因（例癫痫、睡眠不足）。\n\n"+
                "· 障碍评估有一些里程碑评估涉及到的领域，比如：障碍评估中“要求性语言的缺失、薄弱或不足”的得分为2～4分时，那么里程碑里的“提要求”的" +
                "得分往往也会相对较低（和命名、听者技能、仿说相比）。这时，制定教学计划时就需要对“提要求”做优先考虑，确定合适的教学目标。\n\n·";
            String second = StrUtil.format (" {}在此次VB-MAPP障碍评估中得分 {}分（总分为96分，每题最高分4分）。", evResultAddDto.getChildrenName (), evResultAddDto.getTotalScore ());
            String third = "分数越高，表明阻碍学习的障碍种类越多或者程度越重。" +
                "对里程碑评估中没有涉及到的领域，此报告会做单项分析，并推荐下一季度（未来三个月）的个别化教学中应该做出的考虑。" +
                "最终落实的教学方案需要由老师老师和家长视孩子的实际情况而定。";
            VBMAPPReportHeadVo vbmappReportHeadVo = new VBMAPPReportHeadVo ();
            vbmappReportHeadVo.setFirst (first);
            vbmappReportHeadVo.setSecond (second);
            vbmappReportHeadVo.setThird (third);
            //障碍评估项目建议
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = new ArrayList<> ();
            for (EvDomainProjectTemplateVo evDomainProjectTemplateVo : evDomainProjectTemplateVoList) {
                VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo = new VBMAPPAssessTargetSuggestVo ();
                vbmappAssessTargetSuggestVo.setId (evDomainProjectTemplateVo.getDomainProjectId ());
                vbmappAssessTargetSuggestVo.setName (evDomainProjectTemplateVo.getShortTitle ());
                vbmappAssessTargetSuggestVo.setScore ("得分："+projectScore.get (evDomainProjectTemplateVo.getDomainProjectId ())+"分");
                vbmappAssessTargetSuggestVo.setOpinion (evDomainProjectTemplateVo.getOpinion ());
                vbmappAssessTargetSuggestVo.setTargetName ("建议下季度（未来三个月）个别化教学计划中应该有如下方面的考虑：");
                vbmappAssessTargetSuggestVo.setTargetContent ("");
                vbmappAssessTargetSuggestVoList.add (vbmappAssessTargetSuggestVo);
            }
            vbmappReportVo.setHead (vbmappReportHeadVo);
            vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
            vbmappReportVo.setSuggest ("");
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            evResultVbmapp.setObstacleReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmappMapper.updateByPrimaryKeySelective (evResultVbmapp);
        }
        return vbmappReportVo;
    }

    /**
     * deepseek发起请求
     *
     * @param message 提示词
     * @param model   模型
     */
    private JSONArray deepSeekSend (String message, String model) {
        ChatCompletionRequest request = ChatCompletionRequest.builder ()
            .model (model)
            .addUserMessage (message)
            .maxCompletionTokens (maxCompletionTokensV3)
            .temperature (temperature)
            .responseFormat (ResponseFormatType.JSON_OBJECT)
            .build ();
        ChatCompletionResponse execute = deepSeekClient.chatCompletion (request).execute ();
        log.info (execute.choices ().get (0).message ().content ().replace ("```json", "").replace ("\n```", ""));
        return JSONUtil.parseArray (execute.choices ().get (0).message ().content ().replace ("```json", "").replace ("\n```", ""));
    }

    private String getAssessInfo (EvResultAddDto evResultAddDto) {
        //获取评估信息
        StringBuilder assessmentInfo = new StringBuilder ();
        evResultAddDto.getEvDomainTemplateVoList ().forEach (domainDto -> {
            assessmentInfo.append ("  ")
                .append ("domainId：")
                .append (domainDto.getDomainId ())
                .append ("\n")
                .append ("domainName：")
                .append (domainDto.getDomainName ())
                .append ("\n");
            domainDto.getEvDomainProjectTemplateVoList ().forEach (projectDto -> {
                if (projectDto.getScore () != null) {
                    assessmentInfo.append ("    ")
                        .append ("projectId：")
                        .append (projectDto.getDomainProjectId ())
                        .append ("\n")
                        .append ("projectName：")
                        .append (projectDto.getTitle ())
                        .append ("\n")
                        .append ("score：")
                        .append (projectDto.getScore ())
                        .append ("\n")
                        .append (projectDto.getStage ())
                        .append ("\n");
                }
            });
        });
        return assessmentInfo.toString ();
    }

    /**
     * 获取转衔评估详情文本
     *
     * @param evResultAddDto 转衔评估内容
     * @return 详情文本
     */
    private String getTransitionAssessInfo (EvResultAddDto evResultAddDto) {
        StringBuilder assessmentInfo = new StringBuilder ();
        evResultAddDto.getEvDomainTemplateVoList ().forEach (domainDto ->
            domainDto.getEvDomainProjectTemplateVoList ().forEach (projectDto -> {
                if (projectDto.getScore () != null) {
                    assessmentInfo
                        .append ("topic：")
                        .append (projectDto.getNo ())
                        .append ("\n")
                        .append ("projectId：")
                        .append (projectDto.getDomainProjectId ())
                        .append ("\n")
                        .append ("projectName：")
                        .append (projectDto.getTitle ())
                        .append ("\n")
                        .append ("score：")
                        .append (projectDto.getScore ())
                        .append ("\n");
                }
            }));
        return assessmentInfo.toString ();
    }

    /**
     * 获取障碍评估详情
     *
     * @param evResultAddDto 评估内容
     * @return 详情
     */
    private String getObstacleAssessInfo (EvResultAddDto evResultAddDto) {
        StringBuilder assessmentInfo = new StringBuilder ();
        evResultAddDto.getEvDomainTemplateVoList ().forEach (domainDto ->
            domainDto.getEvDomainProjectTemplateVoList ().forEach (projectDto -> {
                if (projectDto.getScore () != null) {
                    assessmentInfo.append ("projectId：")
                        .append (projectDto.getDomainProjectId ())
                        .append ("\n")
                        .append ("projectName：")
                        .append (projectDto.getTitle ())
                        .append ("\n")
                        .append ("得分：")
                        .append (projectDto.getScore ())
                        .append ("\n");
                }
            }));
        return assessmentInfo.toString ();
    }


    /**
     * 生成评估结果
     * @param evResultAddDto 评估数据
     * @return 结果详情
     */
    @Override
    public VbResultVo createVBMAPPAssessResultHome (EvResultAddDto evResultAddDto) {
        EvResultAddDto evResultAddDtoSelf = new EvResultAddDto ();
        BeanUtil.copyProperties (evResultAddDto, evResultAddDtoSelf);
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDtoSelf.getId ());
        VbResultVo vbResultVo = new VbResultVo ();
        //里程碑评估详情
        List<EvDomainTemplateVo> smAssessList = evResultAddDto
            .getEvDomainTemplateVoList ()
            .stream ()
            .filter (vo -> vo.getVbType () == 1)
            .collect (Collectors.toList ());
        evResultAddDtoSelf.setEvDomainTemplateVoList (smAssessList);
        if (CollectionUtil.isNotEmpty (smAssessList)) {
            //插入默认数据
            List<SmAssessResultVo> smAssessResultVoList = new ArrayList<> ();
            //领域得分
            Map<Integer, BigDecimal> groupByDomainId = smAssessList.stream ().collect (Collectors.toMap (EvDomainTemplateVo::getDomainId, EvDomainTemplateVo::getTotalScore));
            for (EvDomainTemplateVo evDomainTemplateVo : evResultAddDtoSelf.getEvDomainTemplateVoList ()) {
                SmAssessResultVo smAssessResultVo = new SmAssessResultVo ();
                smAssessResultVo.setId (evDomainTemplateVo.getDomainId ());
                smAssessResultVo.setTotalScore (groupByDomainId.get (evDomainTemplateVo.getDomainId ()));
                smAssessResultVo.setDomainName (evDomainTemplateVo.getDomainName ());
                smAssessResultVo.setLevel ("");
                smAssessResultVoList.add (smAssessResultVo);
            }
            smAssessResultVoList.clear ();
            //ai生成结果
            JSONArray vbmappAssessResult = this.createVBMAPPAssessResult (evResultAddDtoSelf);
            for (Object o : vbmappAssessResult) {
                JSONObject domain = (JSONObject) o;
                SmAssessResultVo smAssessResultVo = new SmAssessResultVo ();
                smAssessResultVo.setId (domain.getInt ("domainId"));
                smAssessResultVo.setDomainName (domain.getStr ("domainName"));
                if (domain.getInt ("domainId") == 0){
                    smAssessResultVo.setTotalScore (evResultAddDto.getTotalScore ());
                }else {
                    smAssessResultVo.setTotalScore (groupByDomainId.get (domain.getInt ("domainId")));
                }
                smAssessResultVo.setLevel (domain.getStr ("level"));
                smAssessResultVoList.add (smAssessResultVo);
            }
            evResultVbmapp.setMilestoneSummary (JsonHelper.toJsonStr (smAssessResultVoList));
            vbResultVo.setSmAssessResultList (smAssessResultVoList);
        }

        //障碍评估详情
        List<EvDomainTemplateVo> obstacleAssessList = evResultAddDto
            .getEvDomainTemplateVoList ()
            .stream ()
            .filter (vo -> vo.getVbType () == 2)
            .collect (Collectors.toList ());
        evResultAddDtoSelf.setEvDomainTemplateVoList (obstacleAssessList);
        //根据得分进行分组
        if (CollectionUtil.isNotEmpty (obstacleAssessList)){
            Map<Double, List<EvDomainProjectTemplateVo>> groupByScore = obstacleAssessList.get (0)
                .getEvDomainProjectTemplateVoList ()
                .stream ()
                .collect (Collectors.groupingBy (EvDomainProjectTemplateVo::getScore));
            List<ObstacleAssessResultVo> obstacleAssessResultVoList = new ArrayList<> ();
            groupByScore.forEach ((score, projectTemplateVoList) -> {
                ObstacleAssessResultVo obstacleAssessResultVo = new ObstacleAssessResultVo ();
                obstacleAssessResultVo.setScore (BigDecimal.valueOf (score));
                List<String> titleList = new ArrayList<> ();
                projectTemplateVoList.forEach (projectTemplateVo ->
                    titleList.add (projectTemplateVo.getTitle ()));
                obstacleAssessResultVo.setTitleList (titleList);
                obstacleAssessResultVoList.add (obstacleAssessResultVo);
            });
            vbResultVo.setObstacleAssessResultList (obstacleAssessResultVoList);
            evResultVbmapp.setObstacleSummary (JsonHelper.toJsonStr (obstacleAssessResultVoList));
        }

        //转衔评估详情
        List<EvDomainTemplateVo> transitionAssessList = evResultAddDto
            .getEvDomainTemplateVoList ()
            .stream ()
            .filter (vo -> vo.getVbType () == 3)
            .collect (Collectors.toList ());
        evResultAddDtoSelf.setEvDomainTemplateVoList (transitionAssessList);
        List<TransitionAssessResultVo> transitionAssessResultVoList = new ArrayList<> ();
        TransitionAssessResultVo transitionAssessResultVoTemplateOne = new TransitionAssessResultVo ();
        transitionAssessResultVoTemplateOne.setType ("one");
        transitionAssessResultVoTemplateOne.setLevel ("");
        TransitionAssessResultVo transitionAssessResultVoTemplateTwo = new TransitionAssessResultVo ();
        transitionAssessResultVoTemplateTwo.setType ("two");
        transitionAssessResultVoTemplateTwo.setLevel ("");
        TransitionAssessResultVo transitionAssessResultVoTemplateThree = new TransitionAssessResultVo ();
        transitionAssessResultVoTemplateThree.setType ("three");
        transitionAssessResultVoTemplateThree.setLevel ("");
        transitionAssessResultVoList.add (transitionAssessResultVoTemplateOne);
        transitionAssessResultVoList.add (transitionAssessResultVoTemplateTwo);
        transitionAssessResultVoList.add (transitionAssessResultVoTemplateThree);
        transitionAssessResultVoList.clear ();
        JSONArray transitionAssessResult = this.createTransitionAssessResult (evResultAddDtoSelf);
        if (CollectionUtil.isNotEmpty (transitionAssessResult)) {
            for (Object o : transitionAssessResult) {
                JSONObject type = (JSONObject) o;
                TransitionAssessResultVo transitionAssessResultVo = new TransitionAssessResultVo ();
                String next = type.keySet ().iterator ().next ();
                transitionAssessResultVo.setType (next);
                transitionAssessResultVo.setLevel (type.getStr (next));
                transitionAssessResultVoList.add (transitionAssessResultVo);
            }
        }
        vbResultVo.setTransitionAssessResultList (transitionAssessResultVoList);
        evResultVbmapp.setTransferSummary (JsonHelper.toJsonStr (transitionAssessResultVoList));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective (evResultVbmapp);
        return vbResultVo;
    }

    /**
     * 根据里程碑评估详情生成目标
     * @param evResultAddDto 评估详情 如果为 null , 不走AI生成， 如果走AI，需当前领域的分数信息和选题信息
     * @param vbReportUpdateDto 修改数据
     * @return 目标
     */
    @Override
    public String createSmTarget (EvResultAddDto evResultAddDto, VbReportUpdateDto vbReportUpdateDto) {
        if (ObjectUtil.isNotNull (evResultAddDto)){
            JSONArray vbmappSmTarget = this.createVBMAPPSMTarget (evResultAddDto);
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            String milestoneReport = evResultVbmapp.getMilestoneReport ();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (milestoneReport, VBMAPPReportVo.class);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList ();
            StringBuilder targetStr = new StringBuilder ();
            for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
                JSONObject targetJson = (JSONObject) vbmappSmTarget.get (0);
                if (vbmappAssessTargetSuggestVo.getId ().equals (targetJson.get ("domainId"))) {
                    JSONArray target = targetJson.getJSONArray ("target");
                    for (int i = 0; i < target.size (); i++) {
                        targetStr.append ((i + 1)).append ("、")
                            .append (target.get (i))
                            .append ("\n");
                    }
                    vbmappAssessTargetSuggestVo.setTargetContent (targetStr.toString ());
                }
            }
            vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
            evResultVbmapp.setMilestoneReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
            return targetStr.toString ();
        }
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (vbReportUpdateDto.getId ());
        String milestoneReport = evResultVbmapp.getMilestoneReport ();
        VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (milestoneReport, VBMAPPReportVo.class);
        List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList ();
        for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
            if (vbmappAssessTargetSuggestVo.getId ().equals (vbReportUpdateDto.getDomainId ())) {
                vbmappAssessTargetSuggestVo.setTargetContent (vbReportUpdateDto.getTargetContent ());
            }
        }
        vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
        evResultVbmapp.setMilestoneReport (JsonHelper.toJsonStr (vbmappReportVo));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return vbReportUpdateDto.getTargetContent ();
    }

    /**
     * 根据里程碑评估详情生成建议
     * @param evResultAddDto 评估详情 : 如果为 null , 不走AI生成， 如果走AI，需当前领域的分数信息和选题信息
     * @param vbReportUpdateDto 修改数据
     * @return 建议
     */
    @Override
    public String createSmPropose (EvResultAddDto evResultAddDto, VbReportUpdateDto vbReportUpdateDto) {
        if (ObjectUtil.isNotNull (evResultAddDto)){
            //里程碑阶段得分
            Map<Integer, List<EvDomainTemplateStageVo>> groupByDomainId = evResultAddDto.getEvDomainTemplateVoList ().stream ()
                .collect (Collectors.toMap (EvDomainTemplateVo::getDomainId, EvDomainTemplateVo::getStageList));
            //里程碑得分
            Map<Integer, BigDecimal> totalScoreByDomainId = evResultAddDto.getEvDomainTemplateVoList ().stream ()
                .collect (Collectors.toMap (EvDomainTemplateVo::getDomainId, EvDomainTemplateVo::getTotalScore));
            //里程碑满分
            Map<Integer, BigDecimal> scoreByDomainId = evResultAddDto.getEvDomainTemplateVoList ().stream ()
                .collect (Collectors.toMap (EvDomainTemplateVo::getDomainId, EvDomainTemplateVo::getScore));
            JSONArray vbmappSmPropose = this.createVBMAPPSMPropose (evResultAddDto);
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            String milestoneReport = evResultVbmapp.getMilestoneReport ();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (milestoneReport, VBMAPPReportVo.class);
            StringBuilder proposeStr = new StringBuilder ();
            for (int i = 0; i < vbmappSmPropose.size (); i++) {
                JSONObject proposeJson = (JSONObject) vbmappSmPropose.get (i);
                String domainName = proposeJson.getStr ("domainName");
                String propose = proposeJson.getStr ("propose");
                Integer domainId = proposeJson.getInt ("domainId");
                //获取阶段分数
                List<EvDomainTemplateStageVo> evDomainTemplateStageVoList = groupByDomainId.get (domainId);
                proposeStr.append (DIGITS[i])
                    .append ("、")
                    .append (domainName)
                    .append ("\n")
                    .append (StrUtil.format ("孩子在这个领域的得分为{}分（满分{}分），" +
                            "其中第一阶段得分为{}分（总分5分），" +
                            "第二阶段得分为{}分（总分5分），" +
                            "第三阶段得分为{}分（总分5分）。",
                        totalScoreByDomainId.get (domainId),
                        scoreByDomainId.get (domainId),
                        evDomainTemplateStageVoList.get (0).getTotalScore (),
                        evDomainTemplateStageVoList.get (1).getTotalScore (),
                        evDomainTemplateStageVoList.get (2).getTotalScore ()))
                    .append ("\n")
                    .append (propose)
                    .append ("\n\n");
            }
            vbmappReportVo.setSuggest (proposeStr.toString ());
            evResultVbmapp.setMilestoneReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmappMapper.updateByPrimaryKeySelective (evResultVbmapp);
            return proposeStr.toString ();
        }
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (vbReportUpdateDto.getId ());
        String milestoneReport = evResultVbmapp.getMilestoneReport ();
        VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (milestoneReport, VBMAPPReportVo.class);
        vbmappReportVo.setSuggest (vbReportUpdateDto.getSuggest ());
        evResultVbmapp.setMilestoneReport (JsonHelper.toJsonStr (vbmappReportVo));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective (evResultVbmapp);
        return vbReportUpdateDto.getSuggest ();
    }

    /**
     * 根据题目详情生成障碍评估项目建议
     * @param evResultAddDto 题目评估详情 如果为 null , 不走AI生成， 如果走AI，需当前障碍，那一个选择的分数信息和选题信息
     * @param vbReportUpdateDto 修改数据
     * @return 建议
     */
    @Override
    public String createObstacleProjectSuggest (EvResultAddDto evResultAddDto, VbReportUpdateDto vbReportUpdateDto) {
        if (ObjectUtil.isNotNull (evResultAddDto)){
            JSONArray obstacleAssessSubjectPropose = this.createObstacleAssessSubjectPropose (evResultAddDto);
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            String obstacleReport = evResultVbmapp.getObstacleReport ();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (obstacleReport, VBMAPPReportVo.class);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList ();
            JSONObject proposeJson = (JSONObject) obstacleAssessSubjectPropose.get (0);
            String statusSuggest = "";
            for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
                Integer projectId = proposeJson.getInt ("projectId");
                statusSuggest = proposeJson.getStr ("statusSuggest");
                if (Objects.equals (vbmappAssessTargetSuggestVo.getId (), projectId)) {
                    vbmappAssessTargetSuggestVo.setTargetContent (statusSuggest);
                }
            }
            vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
            evResultVbmapp.setObstacleReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
            return statusSuggest;
        }
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (vbReportUpdateDto.getId ());
        String obstacleReport = evResultVbmapp.getObstacleReport ();
        VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (obstacleReport, VBMAPPReportVo.class);
        List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList ();
        for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
            if (Objects.equals (vbmappAssessTargetSuggestVo.getId (), vbReportUpdateDto.getProjectId ())) {
                vbmappAssessTargetSuggestVo.setTargetContent (vbReportUpdateDto.getProjectSuggest ());
            }
        }
        vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
        evResultVbmapp.setObstacleReport (JsonHelper.toJsonStr (vbmappReportVo));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return vbReportUpdateDto.getProjectSuggest ();
    }

    /**
     * 生成障碍评估建议
     * @param evResultAddDto 题目评估详情 如果为 null , 不走AI生成， 如果走AI，需当前障碍选择的分数信息和选题信息
     * @param vbReportUpdateDto 修改数据
     * @return 建议
     */
    @Override
    public String createObstacleSuggest (EvResultAddDto evResultAddDto, VbReportUpdateDto vbReportUpdateDto) {
        if (ObjectUtil.isNotNull (evResultAddDto)){
            JSONArray obstacleAssessPropose = this.createObstacleAssessPropose (evResultAddDto);
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            StringBuilder suggest = new StringBuilder ();
            for (Object o : obstacleAssessPropose) {
                JSONObject obstacleAssessProposeJson = (JSONObject) o;
                String title = obstacleAssessProposeJson.getStr ("title");
                String content = obstacleAssessProposeJson.getStr ("content");
                suggest.append (title)
                    .append ("————")
                    .append (content)
                    .append ("\n");
            }
            String obstacleReport = evResultVbmapp.getObstacleReport ();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (obstacleReport, VBMAPPReportVo.class);
            vbmappReportVo.setSuggest (suggest.toString ());
            evResultVbmapp.setObstacleReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
            return suggest.toString ();
        }
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (vbReportUpdateDto.getId ());
        String obstacleReport = evResultVbmapp.getObstacleReport ();
        VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (obstacleReport, VBMAPPReportVo.class);
        vbmappReportVo.setSuggest (vbReportUpdateDto.getSuggest ());
        evResultVbmapp.setObstacleReport (JsonHelper.toJsonStr (vbmappReportVo));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return vbReportUpdateDto.getSuggest ();
    }

    /**
     * 生成转衔评估项目建议
     * @param evResultAddDto 题目评估详情 如果为 null , 不走AI生成， 如果走AI，需当前转衔，相应分数信息和选题信息
     * @param vbReportUpdateDto 修改数据
     * @return 建议
     */
    @Override
    public String createTransitionProjectSuggest (EvResultAddDto evResultAddDto, VbReportUpdateDto vbReportUpdateDto) {
        if (ObjectUtil.isNotNull (evResultAddDto)){
            JSONArray transitionAssessSubjectPropose = this.createTransitionAssessSubjectPropose (evResultAddDto);
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            String transferReport = evResultVbmapp.getTransferReport ();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (transferReport, VBMAPPReportVo.class);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList ();
            JSONObject proposeJson = (JSONObject) transitionAssessSubjectPropose.get (0);
            String suggest = "";
            for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
                Integer projectId = proposeJson.getInt ("projectId");
                suggest = proposeJson.getStr ("suggest");
                if (Objects.equals (vbmappAssessTargetSuggestVo.getId (), projectId)) {
                    vbmappAssessTargetSuggestVo.setTargetContent (suggest);
                }
            }
            vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
            evResultVbmapp.setTransferReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
            return suggest;
        }
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (vbReportUpdateDto.getId ());
        String transferReport = evResultVbmapp.getTransferReport ();
        VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (transferReport, VBMAPPReportVo.class);
        List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList ();
        for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
            if (Objects.equals (vbmappAssessTargetSuggestVo.getId (), vbReportUpdateDto.getProjectId ())) {
                vbmappAssessTargetSuggestVo.setTargetContent (vbReportUpdateDto.getProjectSuggest ());
            }
        }
        vbmappReportVo.setVbmappAssessTargetSuggestVoList (vbmappAssessTargetSuggestVoList);
        evResultVbmapp.setTransferReport (JsonHelper.toJsonStr (vbmappReportVo));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return vbReportUpdateDto.getProjectSuggest ();
    }

    /**
     * 生成转衔评估建议
     * @param evResultAddDto 题目评估详情 如果为 null , 不走AI生成， 如果走AI，需当前转衔，相应分数信息和选题信息
     * @param vbReportUpdateDto 修改数据
     * @return 建议
     */
    @Override
    public String createTransitionSuggest (EvResultAddDto evResultAddDto, VbReportUpdateDto vbReportUpdateDto) {
        if (ObjectUtil.isNotNull (evResultAddDto)){
            JSONArray transitionAssessPropose = this.createTransitionAssessPropose (evResultAddDto);
            EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (evResultAddDto.getId ());
            StringBuilder suggest = new StringBuilder ();
            for (Object o : transitionAssessPropose) {
                JSONObject transitionAssessProposeJson = (JSONObject) o;
                String title = transitionAssessProposeJson.getStr ("title");
                String content = transitionAssessProposeJson.getStr ("content");
                suggest.append (title)
                    .append ("————")
                    .append (content)
                    .append ("\n");
            }
            String transitionReport = evResultVbmapp.getTransferReport ();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (transitionReport, VBMAPPReportVo.class);
            vbmappReportVo.setSuggest (suggest.toString ());
            evResultVbmapp.setTransferReport (JsonHelper.toJsonStr (vbmappReportVo));
            evResultVbmapp.setUpdateTime (new Date ());
            evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
            evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
            return suggest.toString ();
        }
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (vbReportUpdateDto.getId ());
        String transitionReport = evResultVbmapp.getTransferReport ();
        VBMAPPReportVo vbmappReportVo = JSONUtil.toBean (transitionReport, VBMAPPReportVo.class);
        vbmappReportVo.setSuggest (vbReportUpdateDto.getSuggest ());
        evResultVbmapp.setTransferReport (JsonHelper.toJsonStr (vbmappReportVo));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return vbReportUpdateDto.getSuggest ();
    }

    /**
     * 修改里程碑评估结果
     * @param reportId 报告ID
     * @param id 里程碑id
     * @param level 评估结果
     * @return 结果
     */
    @Override
    public String updateSmAssessResult (Integer reportId,Integer id, String level) {
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (reportId);
        String milestoneSummary = evResultVbmapp.getMilestoneSummary ();
        List<SmAssessResultVo> smAssessResultVoList = JSONUtil.toList (milestoneSummary, SmAssessResultVo.class);
        for (SmAssessResultVo smAssessResultVo : smAssessResultVoList) {
            if (Objects.equals (smAssessResultVo.getId (), id)){
                smAssessResultVo.setLevel (level);
            }
        }
        evResultVbmapp.setMilestoneSummary (JsonHelper.toJsonStr (smAssessResultVoList));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return level;
    }

    /**
     * 修改转衔评估结果
     * @param reportId 报告ID
     * @param type 转衔类型
     * @param level 转衔结果
     * @return 结果
     */
    @Override
    public String updateTransitionAssessResult (Integer reportId,String type, String level) {
        EvResultVbmapp evResultVbmapp = evResultVbmappMapper.selectByPrimaryKey (reportId);
        String transferSummary = evResultVbmapp.getTransferSummary ();
        List<TransitionAssessResultVo> transitionAssessResultVoList = JSONUtil.toList (transferSummary, TransitionAssessResultVo.class);
        for (TransitionAssessResultVo transitionAssessResultVo : transitionAssessResultVoList) {
            if (transitionAssessResultVo.getType ().equals (type)){
                transitionAssessResultVo.setLevel (level);
            }
        }
        evResultVbmapp.setTransferSummary (JsonHelper.toJsonStr (transitionAssessResultVoList));
        evResultVbmapp.setUpdateTime (new Date ());
        evResultVbmapp.setUpdateUser (SecurityUtil.getNickName ());
        evResultVbmappMapper.updateByPrimaryKeySelective(evResultVbmapp);
        return level;
    }
}
