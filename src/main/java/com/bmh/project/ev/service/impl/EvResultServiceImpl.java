package com.bmh.project.ev.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseQuery;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.config.FileConfig;
import com.bmh.common.redis.RedisService;
import com.bmh.common.security.SecurityUtil;
import com.bmh.common.util.QiNiuFileUpLoadUtil;
import com.bmh.project.ev.dto.*;
import com.bmh.project.ev.mapper.*;
import com.bmh.project.ev.model.*;
import com.bmh.project.ev.service.EvAssessAiService;
import com.bmh.project.ev.service.EvResultService;
import com.bmh.project.ev.service.EvResultVbmappService;
import com.bmh.project.ev.vo.*;
import com.bmh.project.report.service.impl.ReportServiceImpl;
import com.bmh.project.user.mapper.SysUserMapper;
import com.bmh.project.user.mapper.YcxChildrenMapper;
import com.bmh.project.user.model.SysUser;
import com.bmh.project.user.model.YcxChildren;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.deepoove.poi.xwpf.NiceXWPFDocument;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.util.concurrent.AtomicDouble;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * EvResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2025年05月12日 15:57:45
 */
@Service
public class EvResultServiceImpl extends BaseServiceImpl<EvResult> implements EvResultService {
    private static final Logger log = LoggerFactory.getLogger (EvResultServiceImpl.class);
    @Resource
    private EvResultMapper evResultMapper;
    @Resource
    private EvDomainMapper evDomainMapper;
    @Resource
    private EvDomainProjectMapper evDomainProjectMapper;
    @Resource
    private EvDomainProjectAnswerMapper evDomainProjectAnswerMapper;
    @Resource
    private EvVbResultDomainMapper evVbResultDomainMapper;
    @Resource
    private EvVbResultDomainProjectMapper evVbResultDomainProjectMapper;
    @Resource
    private EvSxResultDomainMapper evSxResultDomainMapper;
    @Resource
    private EvSxResultDomainProjectMapper evSxResultDomainProjectMapper;
    @Resource
    private YcxChildrenMapper ycxChildrenMapper;
    @Resource
    private RedisService redisService;
    @Resource
    private EvResultVbmappMapper evResultVbMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private FileConfig fileConfig;
    @Resource
    private EvAssessAiService assessAiService;
    @Resource
    private EvResultVbmappService evResultVbmappService;
    /**
     * 创建评估
     *
     * @param evaResultAddDto 评估参数
     * @return 评估id 评估模板
     */
    @Override
    public Result<?> addEvResult(EvResultAddDto evaResultAddDto) {
        EvResult evResult = new EvResult();
        BeanUtils.copyProperties(evaResultAddDto, evResult);
        //判断当前是创建还是更新
        if (ObjectUtil.isEmpty(evResult.getId())) {
            //找到孩子和相应类型：'0 vb 1 pep 2 sx' ,最后一次选择项，赋值给前端，如果是null,表示还是第一次进来，没有选择过
            List<EvSelectResultVo> evSelectResultVoList = new ArrayList<>();
            if (0 == evaResultAddDto.getType()) {
                evSelectResultVoList = evVbResultDomainProjectMapper.getSelectResultList(evaResultAddDto.getChildrenId());
            } else if (2 == evaResultAddDto.getType()) {
                evSelectResultVoList = evSxResultDomainProjectMapper.getSelectResultList(evaResultAddDto.getChildrenId());
            }
            //障碍评估 24 ;转衔评估 25 ；2个领域不计算总分
            List<Integer> outTotal = new ArrayList<>();
            outTotal.add(24);
            outTotal.add(25);
            //构造一个Map
            Map<String, BigDecimal> scoreMap = evSelectResultVoList.stream().collect(HashMap::new,
                (map, item) -> map.put(item.getDomainProjectId() + "_" + item.getNo(), item.getScore()), HashMap::putAll);
            //然后再插入评价记录
            evResult.setStatus(0);
            evResult.setIsDelete(0);
            evResult.setTeacherId(SecurityUtil.getUserId());
            evResult.setCreateTime(new Date());
            evResult.setCreateUser(SecurityUtil.getNickName());
            evResult.setUpdateTime(new Date());
            evResult.setUpdateUser(SecurityUtil.getNickName());
            //创建评估
            evResultMapper.insert(evResult);
            EvAddResultVo evAddResultVo = new EvAddResultVo();
            evAddResultVo.setId(evResult.getId());
            //根据评估类型获取模板
            List<EvDomain> evDomainList = evDomainMapper.selectByType(evaResultAddDto.getType());
            List<EvDomainProject> evDomainProjectList = evDomainProjectMapper.selectByType(evaResultAddDto.getType());
            Map<Integer, List<EvDomainProject>> projectMap = evDomainProjectList.stream().collect(Collectors.groupingBy(EvDomainProject::getDomainId));
            List<EvDomainProjectAnswer> evDomainProjectAnswerList = evDomainProjectAnswerMapper.selectByType(evaResultAddDto.getType());
            Map<Integer, List<EvDomainProjectAnswer>> answerMap = evDomainProjectAnswerList.stream().collect(Collectors.groupingBy(EvDomainProjectAnswer::getDomainProjectId));
            List<EvDomainTemplateVo> evDomainTemplateVoList = new ArrayList<>();
            evDomainList.forEach(evDomain -> {
                EvDomainTemplateVo evDomainTemplateVo = new EvDomainTemplateVo();
                evDomainTemplateVo.setDomainId(evDomain.getId());
                evDomainTemplateVo.setDomainName(evDomain.getDomain());
                List<EvDomainProjectTemplateVo> evDomainProjectTemplateVoList = new ArrayList<>();
                projectMap.get(evDomain.getId()).forEach(project -> {
                    EvDomainProjectTemplateVo evDomainProjectTemplateVo = new EvDomainProjectTemplateVo();
                    evDomainProjectTemplateVo.setDomainProjectId(project.getId());
                    evDomainProjectTemplateVo.setTitle(project.getTitle());
                    evDomainProjectTemplateVo.setShortTitle(project.getShortTitle());
                    evDomainProjectTemplateVo.setNo(project.getNo());
                    //设置已经选中的分数
                    BigDecimal score = scoreMap.get(evDomainProjectTemplateVo.getDomainProjectId() + "_" + evDomainProjectTemplateVo.getNo());
                    if (ObjectUtil.isNotEmpty(score)) {
                        evDomainProjectTemplateVo.setScore(score.doubleValue());
                    }
                    List<EvDomainProjectAnswerTemplateVo> evDomainProjectAnswerTemplateVoList = new ArrayList<>();
                    answerMap.get(project.getId()).forEach(answer -> {
                        EvDomainProjectAnswerTemplateVo evDomainProjectAnswerTemplateVo = new EvDomainProjectAnswerTemplateVo();
                        evDomainProjectAnswerTemplateVo.setScore(answer.getScore());
                        evDomainProjectAnswerTemplateVo.setTxt(answer.getTxt());
                        if ((0 == evaResultAddDto.getType()) && !outTotal.contains(evDomain.getId())) {
                            if (ObjectUtil.isNotNull(evDomainProjectTemplateVo.getScore())) {
                                if (evDomainProjectTemplateVo.getScore() < 0.1) {
                                    evDomainProjectAnswerTemplateVo.setValue(0);
                                } else if (evDomainProjectTemplateVo.getScore() < 0.6) {
                                    if ("0.5".equals(evDomainProjectAnswerTemplateVo.getScore())) {
                                        evDomainProjectAnswerTemplateVo.setValue(0.5);
                                    }
                                } else {
                                    if (!"0".equals(evDomainProjectAnswerTemplateVo.getScore())) {
                                        evDomainProjectAnswerTemplateVo.setValue(0.5);
                                    }
                                }
                            }
                        }
                        evDomainProjectAnswerTemplateVoList.add(evDomainProjectAnswerTemplateVo);
                    });
                    evDomainProjectTemplateVo.setEvDomainProjectAnswerTemplateVoList(evDomainProjectAnswerTemplateVoList);
                    //重新计算总分
                    if ((0 == evaResultAddDto.getType()) && !outTotal.contains(evDomain.getId())) {
                        double totalScore = evDomainProjectAnswerTemplateVoList.stream().map(EvDomainProjectAnswerTemplateVo::getValue)
                            .filter(x -> x != null) // 过滤掉 null 值
                            .mapToDouble(Double::doubleValue)
                            .sum();
                        evDomainProjectTemplateVo.setScore(totalScore);
                    }
                    evDomainProjectTemplateVoList.add(evDomainProjectTemplateVo);
                });
                evDomainTemplateVo.setCount(evDomainProjectTemplateVoList.size());
                evDomainTemplateVo.setEvDomainProjectTemplateVoList(evDomainProjectTemplateVoList);
                //重新计算总分
                if ((0 == evaResultAddDto.getType()) && !outTotal.contains(evDomain.getId())) {
                    double totalScore = evDomainProjectTemplateVoList.stream().map(EvDomainProjectTemplateVo::getScore)
                        .filter(x -> x != null) // 过滤掉 null 值
                        .mapToDouble(Double::doubleValue)
                        .sum();

                    BigDecimal sumScore = Optional.ofNullable(totalScore)
                        .map(BigDecimal::valueOf)
                        .orElse(BigDecimal.ZERO); // null时返回0

                    evDomainTemplateVo.setScore(sumScore);
                }
                evDomainTemplateVoList.add(evDomainTemplateVo);
            });
            evAddResultVo.setEvDomainTemplateVoList(evDomainTemplateVoList);
            return ResultUtil.success(evAddResultVo);
        }
        //进行更新，并创建具体答题记录
        evResult.setUpdateTime(new Date());
        evResult.setStatus(1);
        evResult.setUpdateUser(SecurityUtil.getNickName());
        evResultMapper.updateByPrimaryKeySelective(evResult);
        redisService.set("ev:" + evaResultAddDto.getChildrenId().toString(), evaResultAddDto);
        List<EvDomainTemplateVo> evDomainDtoList = evaResultAddDto.getEvDomainTemplateVoList();
        if (0 == evaResultAddDto.getType()) {
            List<EvVbResultDomainProject> evVbResultDomainProjectList = new ArrayList<>();
            evDomainDtoList.forEach(evDomainTemplateVo -> {
                AtomicDouble domainScore = new AtomicDouble(0);
                EvVbResultDomain evVbResultDomain = new EvVbResultDomain();
                evVbResultDomain.setDomainId(evDomainTemplateVo.getDomainId());
                evVbResultDomain.setDomainName(evDomainTemplateVo.getDomainName());
                evVbResultDomain.setResultId(evaResultAddDto.getId());
                evVbResultDomain.setScore(evDomainTemplateVo.getScore());
                evVbResultDomain.setStatus(1);
                evVbResultDomain.setIsDelete(0);
                evVbResultDomain.setCreateTime(new Date());
                evVbResultDomain.setCreateUser(SecurityUtil.getNickName());
                evVbResultDomain.setUpdateTime(new Date());
                evVbResultDomain.setUpdateUser(SecurityUtil.getNickName());
                evVbResultDomainMapper.insertUseGeneratedKeys(evVbResultDomain);
                for (EvDomainProjectTemplateVo evDomainProjectTemplateVo : evDomainTemplateVo.getEvDomainProjectTemplateVoList()) {
                    EvVbResultDomainProject evVbResultDomainProject = new EvVbResultDomainProject();
                    evVbResultDomainProject.setResultDomainId(evVbResultDomain.getId());
                    if (evDomainProjectTemplateVo.getScore() == null){
                        continue;
                    }
                    evVbResultDomainProject.setScore(evDomainProjectTemplateVo.getScore());
                    domainScore.addAndGet(evDomainProjectTemplateVo.getScore());
                    evVbResultDomainProject.setDomainProjectId(evDomainProjectTemplateVo.getDomainProjectId());
                    evVbResultDomainProject.setNo(evDomainProjectTemplateVo.getNo());
                    evVbResultDomainProject.setStatus(1);
                    evVbResultDomainProject.setIsDelete(0);
                    evVbResultDomainProject.setCreateTime(new Date());
                    evVbResultDomainProject.setUpdateTime(new Date());
                    evVbResultDomainProject.setCreateUser(SecurityUtil.getNickName());
                    evVbResultDomainProject.setUpdateUser(SecurityUtil.getNickName());
                    evVbResultDomainProjectList.add(evVbResultDomainProject);
                }
                evVbResultDomain.setScore(BigDecimal.valueOf(domainScore.get()));
                evVbResultDomainMapper.updateByPrimaryKeySelective(evVbResultDomain);
            });
            evVbResultDomainProjectMapper.insertList(evVbResultDomainProjectList);
        } else {
            List<EvSxResultDomainProject> evSxResultDomainProjectList = new ArrayList<>();
            evDomainDtoList.forEach(evDomainTemplateVo -> {
                AtomicDouble domainScore = new AtomicDouble(0);
                EvSxResultDomain evSxResultDomain = new EvSxResultDomain();
                evSxResultDomain.setDomainId(evDomainTemplateVo.getDomainId());
                evSxResultDomain.setDomainName(evDomainTemplateVo.getDomainName());
                evSxResultDomain.setResultId(evaResultAddDto.getId());
                evSxResultDomain.setScore(evDomainTemplateVo.getScore());
                evSxResultDomain.setStatus(1);
                evSxResultDomain.setIsDelete(0);
                evSxResultDomain.setCreateTime(new Date());
                evSxResultDomain.setCreateUser(SecurityUtil.getNickName());
                evSxResultDomain.setUpdateTime(new Date());
                evSxResultDomain.setUpdateUser(SecurityUtil.getNickName());
                evSxResultDomainMapper.insertUseGeneratedKeys(evSxResultDomain);
                for (EvDomainProjectTemplateVo evDomainProjectTemplateVo : evDomainTemplateVo.getEvDomainProjectTemplateVoList()) {
                    EvSxResultDomainProject evSxResultDomainProject = new EvSxResultDomainProject();
                    evSxResultDomainProject.setResultDomainId(evSxResultDomain.getId());
                    if (evDomainProjectTemplateVo.getScore() == null){
                        continue;
                    }
                    evSxResultDomainProject.setScore(BigDecimal.valueOf(evDomainProjectTemplateVo.getScore()));
                    domainScore.addAndGet(evDomainProjectTemplateVo.getScore());
                    evSxResultDomainProject.setDomainProjectId(evDomainProjectTemplateVo.getDomainProjectId());
                    evSxResultDomainProject.setNo(evDomainProjectTemplateVo.getNo());
                    evSxResultDomainProject.setStatus(1);
                    evSxResultDomainProject.setIsDelete(0);
                    evSxResultDomainProject.setCreateTime(new Date());
                    evSxResultDomainProject.setUpdateTime(new Date());
                    evSxResultDomainProject.setCreateUser(SecurityUtil.getNickName());
                    evSxResultDomainProject.setUpdateUser(SecurityUtil.getNickName());
                    evSxResultDomainProjectList.add(evSxResultDomainProject);
                }
                evSxResultDomain.setScore(BigDecimal.valueOf(domainScore.get()));
                evSxResultDomainMapper.updateByPrimaryKeySelective(evSxResultDomain);
            });
            evSxResultDomainProjectMapper.insertList(evSxResultDomainProjectList);
        }
        return ResultUtil.success(evResult.getId());
    }

    /**
     * 分页获得评估记录
     * @param query 分页数据
     * @param childrenId 儿童id
     * @return
     */
    @Override
    public PageInfo<ResultPageListVo> pageList(BaseQuery query, Integer childrenId) {
        if (query.checkPage ()) {
            PageHelper.startPage (query.getPageNum (), query.getPageSize ());
        }
        List<ResultPageListVo> resultVoList = evResultVbMapper.selectByChildrenId(childrenId);
        return new PageInfo<>(resultVoList);
    }

    /**
     * 第一步先创建一个 status =0 的评估记录
     * @param resultCreateDto
     * @return
     */
    @Override
    public Integer createResult(EvResultCreateDto resultCreateDto) {
        //查询这个孩子是否存在最后一条 status = 0 的记录，有直接返回，否则新增
        Example example = new Example(EvResult.class);
        example.createCriteria().andEqualTo("childrenId", resultCreateDto.getChildrenId())
            .andEqualTo("type", resultCreateDto.getType())
            .andEqualTo("status", 0)
            .andEqualTo("isDelete", 0);
        example.orderBy("createTime").desc();
        List<EvResult> resultList = evResultMapper.selectByExample(example);
        if (CollectionUtil.isNotEmpty(resultList)) {
            return resultList.get(0).getId();
        }
        EvResult evResult = BeanUtil.copyProperties(resultCreateDto, EvResult.class);
        //先设置为0,作为草稿保存,最终生成报表时，更新为1
        evResult.setStatus(0);
        evResult.setIsDelete(0);
        evResult.setTotalScore(BigDecimal.ZERO);
        evResult.setTeacherId(SecurityUtil.getUserId());
        evResult.setCreateTime(new Date());
        evResult.setCreateUser(SecurityUtil.getNickName());
        evResult.setUpdateTime(new Date());
        evResult.setUpdateUser(SecurityUtil.getNickName());
        String resultNo = DateUtil.format(new Date(), "yyyyMMddHHmmss") + RandomUtil.randomNumbers(4);
        evResult.setResultNo(resultNo);
        //创建评估
        evResultMapper.insert(evResult);
        return evResult.getId();
    }

    /**
     * 记录IEP评估测评信息  (第2步 ： 如果是vbmapp ，录入测评信息，如果以前存在结果 回显相关录入的结果)
     * @param resultId 评估Id
     * @return
     */
    @Override
    public Result<?> recordVbResult (Integer resultId) {
        EvResult evResult = evResultMapper.selectByPrimaryKey(resultId);
        //根据评估类型获取模板
        List<EvDomain> evDomainList = evDomainMapper.selectByType(evResult.getType());
        //找到已经选择项
        List<EvSelectResultVo> evSelectResultVoList = new ArrayList<>();
        if (0 == evResult.getType()) {
            evSelectResultVoList = evVbResultDomainProjectMapper.getSelectResultListByResutlId(resultId);
        }
        //如果是转衔中的前5道题，自动赋值，不能修改和选择
        List<Integer> disableList = new ArrayList<>();
        disableList.add(404);
        disableList.add(405);
        disableList.add(406);
        disableList.add(407);
        disableList.add(408);
        //赋值分数的 主键 为 domain_project_id + no
        //构造一个Map
        Map<String, BigDecimal> scoreMap = evSelectResultVoList.stream().collect(HashMap::new,
            (map, item) -> map.put(item.getDomainProjectId() + "_" + item.getNo(), item.getScore()), HashMap::putAll);
        EvAddResultVbVo evAddResultVbVo = new EvAddResultVbVo();
        evAddResultVbVo.setId(evResult.getId());
        List<EvDomainProject> evDomainProjectList = evDomainProjectMapper.selectByType(evResult.getType());
        Map<Integer, List<EvDomainProject>> projectMap = evDomainProjectList.stream().collect(Collectors.groupingBy(EvDomainProject::getDomainId));
        List<EvDomainProjectAnswer> evDomainProjectAnswerList = evDomainProjectAnswerMapper.selectByType(evResult.getType());
        Map<Integer, List<EvDomainProjectAnswer>> answerMap = evDomainProjectAnswerList.stream().collect(Collectors.groupingBy(EvDomainProjectAnswer::getDomainProjectId));
        List<EvDomainVbVo> evDomainVbList = new ArrayList<>();
        evDomainList.forEach(evDomain -> {
            EvDomainVbVo evDomainVb = new EvDomainVbVo();
            evDomainVb.setDomainId(evDomain.getId());
            evDomainVb.setVbType(evDomain.getVbType());
            evDomainVb.setDomainName(evDomain.getDomain());
            evDomainVb.setRealDomainName(evDomain.getDomain());
            List<EvDomainProjectVbVo> evDomainProjectVbList = new ArrayList<>();
            projectMap.get(evDomain.getId()).forEach(project -> {
                EvDomainProjectVbVo evDomainProjectVb = new EvDomainProjectVbVo();
                evDomainProjectVb.setDomainProjectId(project.getId());
                evDomainProjectVb.setTitle(project.getTitle());
                evDomainProjectVb.setShortTitle(project.getShortTitle());
                evDomainProjectVb.setNo(project.getNo());
                evDomainProjectVb.setDomainId(evDomain.getId());
                evDomainProjectVb.setDomainName(evDomain.getDomain());
                evDomainProjectVb.setStage(project.getStage());
                evDomainProjectVb.setDisable(0);
                //如果是转衔中的前5道题，自动赋值，不能修改和选择
                if (disableList.contains(project.getId())){
                    evDomainProjectVb.setDisable(1);
                }
                //设置已经选中的分数
                BigDecimal score = scoreMap.get(evDomainProjectVb.getDomainProjectId() + "_" + evDomainProjectVb.getNo());
                if (ObjectUtil.isNotEmpty(score)) {
                    evDomainProjectVb.setScore(score.doubleValue());
                } else {
                    //未选择过给null
                    evDomainProjectVb.setScore(null);
                }
                List<EvDomainProjectAnswerVbVo> evDomainProjectAnswerVbList = new ArrayList<>();
                answerMap.get(project.getId()).forEach(answer -> {
                    EvDomainProjectAnswerVbVo evDomainProjectAnswerVb = new EvDomainProjectAnswerVbVo();
                    evDomainProjectAnswerVb.setScore(answer.getScore());
                    evDomainProjectAnswerVb.setTxt(answer.getTxt());
                    evDomainProjectAnswerVbList.add(evDomainProjectAnswerVb);
                });
                evDomainProjectVb.setEvDomainProjectAnswerVbList(evDomainProjectAnswerVbList);
                evDomainProjectVbList.add(evDomainProjectVb);
            });
            evDomainVb.setCount(evDomainProjectVbList.size());
            evDomainVb.setEvDomainProjectVbList(evDomainProjectVbList);
            //重新计算总分
            double totalScore = evDomainProjectVbList.stream().map(EvDomainProjectVbVo::getScore)
                .filter(x -> x != null) // 过滤掉 null 值
                .mapToDouble(Double::doubleValue)
                .sum();

            BigDecimal sumScore = Optional.ofNullable(totalScore)
                .map(BigDecimal::valueOf)
                .orElse(BigDecimal.ZERO); // null时返回0

            evDomainVb.setScore(sumScore);
            evDomainVbList.add(evDomainVb);
        });
        //分组统计
        List<EvDomainVbTypeVo> evDomainVbTypeVoList = new ArrayList<>();
        //vbmapp 分类  1 里程碑 2 障碍评估 3 转衔评估
        //1 里程碑
        EvDomainVbTypeVo domainVbTypeVo = new EvDomainVbTypeVo();
        domainVbTypeVo.setVbType(1);
        domainVbTypeVo.setVbTypeName("里程碑");
        List<EvDomainVbVo> domainVbList = evDomainVbList.stream().filter(item->1==item.getVbType()).collect(Collectors.toList());
        Integer count = evDomainVbList.stream().filter(item->1==item.getVbType())
            .map(EvDomainVbVo::getCount)
            .filter(x -> x != null) // 过滤掉 null 值
            .mapToInt(Integer::intValue)
            .sum();
        domainVbTypeVo.setEvDomainVbList(domainVbList);
        domainVbTypeVo.setCount(count);
        evDomainVbTypeVoList.add(domainVbTypeVo);
        //2 障碍评估
        domainVbTypeVo = new EvDomainVbTypeVo();
        domainVbTypeVo.setVbType(2);
        domainVbTypeVo.setVbTypeName("障碍评估");
        domainVbList = evDomainVbList.stream().filter(item->2==item.getVbType()).collect(Collectors.toList());
        count = evDomainVbList.stream().filter(item->2==item.getVbType())
            .map(EvDomainVbVo::getCount)
            .filter(x -> x != null) // 过滤掉 null 值
            .mapToInt(Integer::intValue)
            .sum();
        domainVbTypeVo.setEvDomainVbList(domainVbList);
        domainVbTypeVo.setCount(count);
        evDomainVbTypeVoList.add(domainVbTypeVo);
        //3 转衔评估
        domainVbTypeVo = new EvDomainVbTypeVo();
        domainVbTypeVo.setVbType(3);
        domainVbTypeVo.setVbTypeName("转衔评估");
        domainVbList = evDomainVbList.stream().filter(item->3==item.getVbType()).collect(Collectors.toList());
        List<EvDomainVbVo> newDomainVbVoList = new ArrayList<>();
        //领域id不变，构造一个三层的；第一类；第二类；第三类
        List<String> stageList = new ArrayList<>();
        stageList.add("第一类");
        stageList.add("第二类");
        stageList.add("第三类");
        List<EvDomainVbVo> domainList = domainVbList;
        if (CollectionUtil.isNotEmpty(domainList)){
            stageList.forEach(stage->{
                domainList.forEach(item->{
                    EvDomainVbVo domainVo = BeanUtil.copyProperties(item,EvDomainVbVo.class);
                    domainVo.setDomainName(stage);
                    List<EvDomainProjectVbVo> domainProjectVbList =  item.getEvDomainProjectVbList().stream().filter(t->stage.equals(t.getStage())).collect(Collectors.toList());
                    domainVo.setEvDomainProjectVbList(domainProjectVbList);
                    if (CollectionUtil.isNotEmpty(domainProjectVbList)) {
                        domainVo.setCount(domainProjectVbList.size());
                    } else {
                        domainVo.setCount(0);
                    }
                    newDomainVbVoList.add(domainVo);
                });
            });
        }
        count = evDomainVbList.stream().filter(item->2==item.getVbType())
            .map(EvDomainVbVo::getCount)
            .filter(x -> x != null) // 过滤掉 null 值
            .mapToInt(Integer::intValue)
            .sum();
        domainVbTypeVo.setEvDomainVbList(newDomainVbVoList);
        domainVbTypeVo.setCount(count);
        evDomainVbTypeVoList.add(domainVbTypeVo);
        evAddResultVbVo.setEvDomainVbTypeVoList(evDomainVbTypeVoList);
        return ResultUtil.success(evAddResultVbVo);
    }

    /**
     * 选择分数项保存 (第3步 ： 如果是vbmapp ，每次保存或修改选题结果)
     * @param scoreDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer recordVbScore (EvResultVbScoreDto scoreDto) {
        //判断这个分数存在，不存在插入，否则更新
        Example example = new Example (EvVbResultDomain.class);
        example.createCriteria ().andEqualTo ("resultId", scoreDto.getResultId())
            .andEqualTo ("domainId", scoreDto.getDomainId())
            .andEqualTo("status", 1)
            .andEqualTo("isDelete", 0);
        List<EvVbResultDomain> vbResultDomainList =  evVbResultDomainMapper.selectByExample(example);
        //如果不存在，插入
        if (CollectionUtil.isEmpty(vbResultDomainList)){
            EvVbResultDomain evVbResultDomain = new EvVbResultDomain();
            evVbResultDomain.setDomainId(scoreDto.getDomainId());
            evVbResultDomain.setDomainName(scoreDto.getDomainName());
            evVbResultDomain.setResultId(scoreDto.getResultId());
            evVbResultDomain.setScore(scoreDto.getScore());
            evVbResultDomain.setStatus(1);
            evVbResultDomain.setIsDelete(0);
            evVbResultDomain.setCreateTime(new Date());
            evVbResultDomain.setCreateUser(SecurityUtil.getNickName());
            evVbResultDomain.setUpdateTime(new Date());
            evVbResultDomain.setUpdateUser(SecurityUtil.getNickName());
            evVbResultDomainMapper.insertUseGeneratedKeys(evVbResultDomain);
            //插入选择项
            EvVbResultDomainProject evVbResultDomainProject = new EvVbResultDomainProject();
            evVbResultDomainProject.setResultDomainId(evVbResultDomain.getId());
            evVbResultDomainProject.setDomainProjectId(scoreDto.getDomainProjectId());
            evVbResultDomainProject.setNo(scoreDto.getNo());
            evVbResultDomainProject.setScore(ObjectUtil.isNull(scoreDto.getScore())?0.00:scoreDto.getScore().doubleValue());
            evVbResultDomainProject.setStatus(1);
            evVbResultDomainProject.setIsDelete(0);
            evVbResultDomainProject.setCreateTime(new Date());
            evVbResultDomainProject.setUpdateTime(new Date());
            evVbResultDomainProject.setCreateUser(SecurityUtil.getNickName());
            evVbResultDomainProject.setUpdateUser(SecurityUtil.getNickName());
            evVbResultDomainProjectMapper.insertUseGeneratedKeys(evVbResultDomainProject);
            return  evVbResultDomainProject.getId();
        } else {
            //相应的分数记录是否存在
            EvVbResultDomain evVbResultDomain = vbResultDomainList.get(0);
            Example exampleProject = new Example (EvVbResultDomainProject.class);
            exampleProject.createCriteria ()
                .andEqualTo ("resultDomainId", evVbResultDomain.getId())
                .andEqualTo ("domainProjectId", scoreDto.getDomainProjectId())
                .andEqualTo ("no", scoreDto.getNo())
                .andEqualTo("status", 1)
                .andEqualTo("isDelete", 0);
            List<EvVbResultDomainProject> evVbResultDomainProjectList =  evVbResultDomainProjectMapper.selectByExample(exampleProject);
            //如果不存在，插入
            EvVbResultDomainProject evVbResultDomainProject = new EvVbResultDomainProject();
            if (CollectionUtil.isEmpty(evVbResultDomainProjectList)){
                evVbResultDomainProject.setResultDomainId(evVbResultDomain.getId());
                evVbResultDomainProject.setDomainProjectId(scoreDto.getDomainProjectId());
                evVbResultDomainProject.setNo(scoreDto.getNo());
                evVbResultDomainProject.setScore(ObjectUtil.isNull(scoreDto.getScore())?0.00:scoreDto.getScore().doubleValue());
                evVbResultDomainProject.setStatus(1);
                evVbResultDomainProject.setIsDelete(0);
                evVbResultDomainProject.setCreateTime(new Date());
                evVbResultDomainProject.setUpdateTime(new Date());
                evVbResultDomainProject.setCreateUser(SecurityUtil.getNickName());
                evVbResultDomainProject.setUpdateUser(SecurityUtil.getNickName());
                evVbResultDomainProjectMapper.insertUseGeneratedKeys(evVbResultDomainProject);
                //分数增加
                evVbResultDomain.setScore(evVbResultDomain.getScore().add(scoreDto.getScore()));
                evVbResultDomainMapper.updateByPrimaryKeySelective(evVbResultDomain);
            } else {
                evVbResultDomainProject = evVbResultDomainProjectList.get(0);
                BigDecimal subScoure = new BigDecimal(ObjectUtil.isNull(evVbResultDomainProject.getScore())?0.00:evVbResultDomainProject.getScore());
                evVbResultDomainProject.setResultDomainId(evVbResultDomain.getId());
                evVbResultDomainProject.setDomainProjectId(scoreDto.getDomainProjectId());
                evVbResultDomainProject.setNo(scoreDto.getNo());
                evVbResultDomainProject.setScore(ObjectUtil.isNull(scoreDto.getScore())?0.00:scoreDto.getScore().doubleValue());
                evVbResultDomainProject.setUpdateTime(new Date());
                evVbResultDomainProject.setUpdateUser(SecurityUtil.getNickName());
                evVbResultDomainProjectMapper.updateByPrimaryKeySelective(evVbResultDomainProject);
                //分数变动
                evVbResultDomain.setScore(evVbResultDomain.getScore().add(scoreDto.getScore()).subtract(subScoure));
                evVbResultDomainMapper.updateByPrimaryKeySelective(evVbResultDomain);
            }
            return evVbResultDomainProject.getId();
        }
    }

    /**
     * 录入评估测评结果后提交AI (第4步 ： 除障碍外，未选项目给0分，ev_result ->status = 1 从草稿变发布)
     * @param resultDto 评估参数
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> addOrUpdateVbResult(EvResultVbDto resultDto) {
        EvResult evResult = evResultMapper.selectByPrimaryKey(resultDto.getId());
        //进行更新，已经存在的记录不再处理，仅处理
        evResult.setUpdateTime(new Date());
        evResult.setStatus(1);
        evResult.setUpdateUser(SecurityUtil.getNickName());
        evResultMapper.updateByPrimaryKeySelective(evResult);
        //判断评估中领域是否已经记录分数
        //判断这个分数存在，不存在插入，否则更新
        Example example = new Example(EvVbResultDomain.class);
        example.createCriteria().andEqualTo("resultId", resultDto.getId())
            .andEqualTo("status", 1)
            .andEqualTo("isDelete", 0);
        List<EvVbResultDomain> vbResultDomainList = evVbResultDomainMapper.selectByExample(example);
        //变Map用于判断是否已经记录过分数
        Map<Integer, EvVbResultDomain> evVbDomainMap = vbResultDomainList.stream().collect(Collectors.toMap(
            EvVbResultDomain::getDomainId,
            domain -> domain,
            (existing, replacement) -> existing // 保留先出现的元素（不覆盖）
        ));
        //判断评估中领域项目是否已经记录分数
        List<Integer> vbResutlDomainIds = vbResultDomainList.stream().map(EvVbResultDomain::getId).collect(Collectors.toList());
        List<EvVbResultDomainProject> vbResultDomainProjectList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(vbResutlDomainIds)) {
            Example exampleProject = new Example(EvVbResultDomainProject.class);
            exampleProject.createCriteria().andIn("resultDomainId", vbResutlDomainIds)
                .andEqualTo("status", 1)
                .andEqualTo("isDelete", 0);
            vbResultDomainProjectList = evVbResultDomainProjectMapper.selectByExample(exampleProject);
        }
        //构造一个Map
        Map<String, Double> scoreMap = vbResultDomainProjectList.stream().collect(HashMap::new,
            (map, item) -> map.put(item.getDomainProjectId() + "_" + item.getNo(), item.getScore()), HashMap::putAll);
        //根据评估类型获取模板,除了障碍外 其他未选择项默认给0
        List<EvDomain> evDomainList = evDomainMapper.selectByType(evResult.getType());
        Map<Integer, EvDomain> evDomainMap = evDomainList.stream().collect(Collectors.toMap(EvDomain::getId, domain -> domain));

        List<EvDomainVbTypeVo> evDomainVbTypeDtoList = resultDto.getEvDomainVbTypeVoList();
        //取第2层进行领域处理，第1层是虚拟的为了前端展示 1 里程碑评估 2 障碍评估 3 转衔评估
        List<EvDomainVbVo> evDomainDtoList = new ArrayList<>();
        //把转衔的合并起来,原来分成了3类和多层
        EvDomainVbTypeVo vbTypeVo = new EvDomainVbTypeVo();
        EvDomainVbVo transferVo = new EvDomainVbVo();
        List<EvDomainProjectVbVo> transferProjectVo = new ArrayList<>();
        for (int i = 0; i < evDomainVbTypeDtoList.size(); i++) {
            EvDomainVbTypeVo typeVo = evDomainVbTypeDtoList.get(i);
            if (3==typeVo.getVbType()){
                //取最后一个合并的领域
                List<EvDomainVbVo> domainVbList = typeVo.getEvDomainVbList();
                for (int m = 0; m < domainVbList.size(); m++) {
                    EvDomainVbVo domainVb  = domainVbList.get(m);
                    transferVo = BeanUtil.copyProperties(domainVb,EvDomainVbVo.class);
                    transferVo.setDomainName(domainVb.getRealDomainName());
                    transferProjectVo.addAll(domainVb.getEvDomainProjectVbList());
                }
            }
        }
        List<EvDomainVbVo>  transferVoList = new ArrayList<>();
        //把其他的转衔的项目合并到这个最后的领域下
        transferVo.setEvDomainProjectVbList(transferProjectVo);
        transferVoList.add(transferVo);
        vbTypeVo.setEvDomainVbList(transferVoList);
        evDomainVbTypeDtoList.removeIf(x->(3==x.getVbType()));
        evDomainVbTypeDtoList.add(vbTypeVo);
        evDomainVbTypeDtoList.forEach(item->{
            evDomainDtoList.addAll(item.getEvDomainVbList());
        });
        List<EvVbResultDomainProject> evVbResultDomainProjectList = new ArrayList<>();
        evDomainDtoList.forEach(evDomainVo -> {
            //如果这个领域已经存在，不增加，仅获得领域信息
            EvVbResultDomain evVbResultDomain;
            if (CollectionUtil.isNotEmpty(evVbDomainMap) && ObjectUtil.isNotNull(evVbDomainMap.get(evDomainVo.getDomainId()))) {
                evVbResultDomain = evVbDomainMap.get(evDomainVo.getDomainId());
            } else {
                evVbResultDomain = new EvVbResultDomain();
                evVbResultDomain.setDomainId(evDomainVo.getDomainId());
                evVbResultDomain.setDomainName(evDomainVo.getRealDomainName());
                evVbResultDomain.setResultId(resultDto.getId());
                evVbResultDomain.setScore(BigDecimal.ZERO);
                evVbResultDomain.setStatus(1);
                evVbResultDomain.setIsDelete(0);
                evVbResultDomain.setCreateTime(new Date());
                evVbResultDomain.setCreateUser("system");
                evVbResultDomain.setUpdateTime(new Date());
                evVbResultDomain.setUpdateUser("system");
                evVbResultDomainMapper.insertUseGeneratedKeys(evVbResultDomain);
            }

            for (EvDomainProjectVbVo evDomainProjectVo : evDomainVo.getEvDomainProjectVbList()) {
                //如果障碍的没选择 跳过， 其他的默认给0
                EvDomain evDomain = evDomainMap.get(evDomainVo.getDomainId());
                if (ObjectUtil.isNotNull(evDomain)) {
                    //如果障碍的没选择 跳过
                    if (evDomainProjectVo.getScore() == null) {
                        if (2 == evDomain.getVbType()) {
                            continue;
                        }
                    } else {
                        evDomainProjectVo.setScore(0.00);
                    }
                }
                //已经录入了数据的给0
                if (CollectionUtil.isNotEmpty(scoreMap) && ObjectUtil.isNotNull(scoreMap.get(evDomainProjectVo.getDomainProjectId() + "_" + evDomainProjectVo.getNo()))) {
                    continue;
                }
                //如果已经存在不处理，否则插入0分
                EvVbResultDomainProject evVbResultDomainProject = new EvVbResultDomainProject();
                evVbResultDomainProject.setResultDomainId(evVbResultDomain.getId());
                evVbResultDomainProject.setScore(0.00);
                evVbResultDomainProject.setDomainProjectId(evDomainProjectVo.getDomainProjectId());
                evVbResultDomainProject.setNo(evDomainProjectVo.getNo());
                evVbResultDomainProject.setStatus(1);
                evVbResultDomainProject.setIsDelete(0);
                evVbResultDomainProject.setCreateTime(new Date());
                evVbResultDomainProject.setUpdateTime(new Date());
                evVbResultDomainProject.setCreateUser("system");
                evVbResultDomainProject.setUpdateUser("system");
                evVbResultDomainProjectList.add(evVbResultDomainProject);
            }
        });
        if (CollectionUtil.isNotEmpty(evVbResultDomainProjectList)) {
            evVbResultDomainProjectMapper.insertList(evVbResultDomainProjectList);
        }
        return ResultUtil.success(evResult.getId());
    }

    /**
     * 创建Vb评估报告
     * @param resultId 评估id参数
     * @return 报告id
     */
    @Override
    public Integer createVbReport(Integer resultId) {
        if (ObjectUtil.isNull(resultId)) return null;
        EvResult evResult = evResultMapper.selectByPrimaryKey(resultId);
        if (ObjectUtil.isNull(evResult)) return 0;
        //查询相应的vbmapp报告是否存在，不存在插入，否则更新
        Example example = new Example (EvResultVbmapp.class);
        example.createCriteria ().andEqualTo ("resultId", resultId);
        List<EvResultVbmapp> vbmappList = evResultVbMapper.selectByExample(example);
        EvResultVbmapp evResultVb ;
        if (CollectionUtil.isEmpty(vbmappList)){
            evResultVb = BeanUtil.copyProperties (evResult, EvResultVbmapp.class);
            evResultVb.setResultId(resultId);
            evResultVb.setId(null);
            //然后再插入评价记录
            evResultVb.setAssessResult ("");
            evResultVb.setAdvance ("");
            evResultVb.setAssessQuestion ("");
            evResultVb.setReferralSuggestion ("");
            evResultVb.setStatus(1);
            evResultVb.setIsDelete(0);
            evResultVb.setCreateTime(new Date());
            evResultVb.setCreateUser(SecurityUtil.getNickName());
            evResultVb.setUpdateTime(new Date());
            evResultVb.setUpdateUser(SecurityUtil.getNickName());
            //创建vb评估报告
            evResultVbMapper.insert(evResultVb);
            try {
                //分3步生成空的内容
                EvResultAddDto evResultAddDto = this.getAIParams(resultId,1,null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPAssessResultHome(evResultAddDto);
                assessAiService.createVBMAPPResult(evResultAddDto);
                //AI生成VBMAPP障碍全部内容
                evResultAddDto = this.getAIParams(resultId,2,null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPAssessResultHome(evResultAddDto);
                //没有设置固定文案的障碍，不再报表中体现，传入参数2，给AI时，把相应的领域过滤掉
                evResultAddDto = this.getAIParams(resultId,2,null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPObstacleResult(evResultAddDto);
                //AI生成VBMAPP转衔全部内容
                evResultAddDto = this.getAIParams(resultId,3,null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPAssessResultHome(evResultAddDto);
                assessAiService.createVBMAPPTransitionResult(evResultAddDto);
            } catch (ExecutionException e) {
                e.printStackTrace();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            return  evResultVb.getId();
        } else {
            evResultVb = vbmappList.get(0);
            //进行更新
            evResultVb.setUpdateTime(new Date());
            evResultVb.setUpdateUser(SecurityUtil.getNickName());
            evResultVbMapper.updateByPrimaryKeySelective(evResultVb);
            return evResultVb.getId();
        }
    }

    /**
     * 生成给AI的参数
     * @param resultId
     * @param vbType 1 里程碑评估 2 障碍评估 3 转衔评估 ； 0 表示全部
     * @param domainId 里程碑领域Id 或 障碍或转衔的projectId ，如果全部领域 传 null
     * @return
     */
    private  EvResultAddDto getAIParams(Integer resultId,Integer vbType,Integer domainId) {
        //赋值评估信息
        EvResult evResult = evResultMapper.selectByPrimaryKey(resultId);
        EvResultAddDto evResultAddDto = BeanUtil.copyProperties(evResult, EvResultAddDto.class);
        //赋值孩子信息
        YcxChildren ycxChildren = ycxChildrenMapper.selectByPrimaryKey(evResult.getChildrenId());
        if (ObjectUtil.isNotNull(ycxChildren)) {
            evResultAddDto.setChildrenName(ycxChildren.getName());
            evResultAddDto.setBirthday(ycxChildren.getBirthday());
            //评估满分
            List<EvDomain> domainList = evDomainMapper.selectByType(evResult.getType());
            BigDecimal score = domainList.stream().map(EvDomain::getScore)
                .filter(Objects::nonNull)
                .map(BigDecimal::valueOf) // 将 double 转为 BigDecimal
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            evResultAddDto.setScore(score);
        }
        //赋值补充信息
        EvResultVbmapp resultVbmapp = evResultVbMapper.getResultVbByResutlId(resultId);
        if (ObjectUtil.isNotNull(resultVbmapp)) {
            //赋值评估选题信息
            evResultAddDto.setAssessQuestion(resultVbmapp.getAssessQuestion());
            evResultAddDto.setAssessResult(resultVbmapp.getAssessResult());
            evResultAddDto.setAdvance(resultVbmapp.getAdvance());
            evResultAddDto.setReferralSuggestion(resultVbmapp.getReferralSuggestion());
        }
        //根据评估类型获取模板
        List<EvDomain> evDomainList ;
        if (vbType>0) {
            evDomainList = evDomainMapper.selectByVbType(evResult.getType(), vbType);
        } else {
            evDomainList = evDomainMapper.selectByType(evResult.getType());
        }
        //如果是里程碑仅显示相应领域的分数和选择，用于单独AI生成单个领域的未来3个月建议
        if (ObjectUtil.isNotEmpty(domainId) && (1==vbType)){
            evDomainList = evDomainList.stream().filter(d->d.getId().equals(domainId)).collect(Collectors.toList());
        }
        List<EvDomainProject> evDomainProjectList = evDomainProjectMapper.selectByType(evResult.getType());
        //如果是障碍或转衔仅显示相应项目的分数和选择，用于单独AI生成单个项目未来3个月建议
        if (ObjectUtil.isNotEmpty(domainId) && (2==vbType || 3== vbType)){
            evDomainProjectList = evDomainProjectList.stream().filter(d->d.getId().equals(domainId)).collect(Collectors.toList());
        }
        Map<Integer, List<EvDomainProject>> projectMap = evDomainProjectList.stream().collect(Collectors.groupingBy(EvDomainProject::getDomainId));
        List<EvDomainProjectAnswer> evDomainProjectAnswerList = evDomainProjectAnswerMapper.selectByType(evResult.getType());
        Map<Integer, List<EvDomainProjectAnswer>> answerMap = evDomainProjectAnswerList.stream().collect(Collectors.groupingBy(EvDomainProjectAnswer::getDomainProjectId));

        //获得已经选择的项目和分数
        List<EvSelectResultVo> evSelectResultVoList = evVbResultDomainProjectMapper.getSelectResultListByResutlId(resultId);
        //构造一个Map
        Map<String, BigDecimal> scoreMap = evSelectResultVoList.stream().collect(HashMap::new,
            (map, item) -> map.put(item.getDomainProjectId() + "_" + item.getNo(), item.getScore()), HashMap::putAll);

        //赋值答题信息
        List<EvDomainTemplateVo> evDomainTemplateVoList = new ArrayList<>();
        evDomainList.forEach(evDomain -> {
            EvDomainTemplateVo evDomainTemplateVo = new EvDomainTemplateVo();
            evDomainTemplateVo.setDomainId(evDomain.getId());
            evDomainTemplateVo.setDomainName(evDomain.getDomain());
            evDomainTemplateVo.setVbType(evDomain.getVbType());
            //1 里程碑评估 2 障碍评估 3 转衔评估
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVoList = new ArrayList<>();
            //障碍 特殊处理 无固定文案的不显示再报告里面，但是分数中需要显示
            List<EvDomainProject> domainProjectList =  projectMap.get(evDomain.getId()) ;
            domainProjectList.forEach(project -> {
                EvDomainProjectTemplateVo evDomainProjectTemplateVo = new EvDomainProjectTemplateVo();
                evDomainProjectTemplateVo.setDomainProjectId(project.getId());
                evDomainProjectTemplateVo.setTitle(project.getTitle());
                evDomainProjectTemplateVo.setShortTitle(project.getShortTitle());
                evDomainProjectTemplateVo.setNo(project.getNo());
                evDomainProjectTemplateVo.setStage(project.getStage());
                evDomainProjectTemplateVo.setMaxScore(project.getScore());
                evDomainProjectTemplateVo.setOpinion(project.getOpinion());
                //设置已经选中的分数
                BigDecimal score = scoreMap.get(evDomainProjectTemplateVo.getDomainProjectId() + "_" + evDomainProjectTemplateVo.getNo());
                if (ObjectUtil.isNotEmpty(score)) {
                    evDomainProjectTemplateVo.setScore(score.doubleValue());
                } else {
                    //没分数的旧数据，直接给0
                    evDomainProjectTemplateVo.setScore(0.0);
                }
                List<EvDomainProjectAnswerTemplateVo> evDomainProjectAnswerTemplateVoList = new ArrayList<>();
                answerMap.get(project.getId()).forEach(answer -> {
                    EvDomainProjectAnswerTemplateVo evDomainProjectAnswerTemplateVo = new EvDomainProjectAnswerTemplateVo();
                    evDomainProjectAnswerTemplateVo.setScore(answer.getScore());
                    evDomainProjectAnswerTemplateVo.setTxt(answer.getTxt());
                    if (ObjectUtil.isNotEmpty(score)) {
                        evDomainProjectAnswerTemplateVo.setValue(score.doubleValue());
                    } else {
                        evDomainProjectAnswerTemplateVo.setValue(0.0);
                    }
                    evDomainProjectAnswerTemplateVoList.add(evDomainProjectAnswerTemplateVo);
                });
                evDomainProjectTemplateVo.setEvDomainProjectAnswerTemplateVoList(evDomainProjectAnswerTemplateVoList);
                evDomainProjectTemplateVoList.add(evDomainProjectTemplateVo);
            });

            //如果有下面的数据才进行处理
            if (CollectionUtil.isNotEmpty(evDomainProjectTemplateVoList)) {
                evDomainTemplateVo.setCount(evDomainProjectTemplateVoList.size());
                evDomainTemplateVo.setEvDomainProjectTemplateVoList(evDomainProjectTemplateVoList);
                //重新计算领域总分
                double totalScore = evDomainProjectTemplateVoList.stream().map(EvDomainProjectTemplateVo::getScore)
                    .filter(x -> x != null) // 过滤掉 null 值
                    .mapToDouble(Double::doubleValue)
                    .sum();
                BigDecimal sumScore = Optional.ofNullable(totalScore)
                    .map(BigDecimal::valueOf)
                    .orElse(BigDecimal.ZERO); // null时返回0
                //领域得分
                evDomainTemplateVo.setTotalScore(sumScore);

                //领域总分
                totalScore = evDomainProjectTemplateVoList.stream().map(EvDomainProjectTemplateVo::getMaxScore)
                    .filter(x -> x != null) // 过滤掉 null 值
                    .mapToDouble(Double::doubleValue)
                    .sum();
                sumScore = Optional.ofNullable(totalScore)
                    .map(BigDecimal::valueOf)
                    .orElse(BigDecimal.ZERO); // null时返回0
                evDomainTemplateVo.setScore(sumScore);

                //里程碑 需要分阶段单独统计分数给AI
                if (1 == vbType) {
                    List<EvDomainTemplateStageVo> stageList = new ArrayList<>();
                    EvDomainTemplateStageVo stageVo = new EvDomainTemplateStageVo();
                    stageVo.setStageName("第一阶段");
                    stageVo.setLevelId(1);
                    stageVo.setScore(BigDecimal.ZERO);
                    stageVo.setTotalScore(BigDecimal.ZERO);
                    stageList.add(stageVo);
                    stageVo = new EvDomainTemplateStageVo();
                    stageVo.setStageName("第二阶段");
                    stageVo.setLevelId(2);
                    stageVo.setScore(BigDecimal.ZERO);
                    stageVo.setTotalScore(BigDecimal.ZERO);
                    stageList.add(stageVo);
                    stageVo = new EvDomainTemplateStageVo();
                    stageVo.setStageName("第三阶段");
                    stageVo.setLevelId(3);
                    stageVo.setScore(BigDecimal.ZERO);
                    stageVo.setTotalScore(BigDecimal.ZERO);
                    stageList.add(stageVo);
                    stageList.forEach(stage->{
                        //重新计算总分
                        double stageTotalScore = evDomainProjectTemplateVoList.stream().filter(x->x.getStage().equals(stage.getStageName())).map(EvDomainProjectTemplateVo::getScore)
                            .filter(x -> x != null) // 过滤掉 null 值
                            .mapToDouble(Double::doubleValue)
                            .sum();

                        BigDecimal stageSumScore = Optional.ofNullable(stageTotalScore)
                            .map(BigDecimal::valueOf)
                            .orElse(BigDecimal.ZERO); // null时返回0
                        //阶段得分
                        stage.setTotalScore(stageSumScore);
                        //阶段总分，最大分
                        stageTotalScore = evDomainProjectTemplateVoList.stream().filter(x->x.getStage().equals(stage.getStageName())).map(EvDomainProjectTemplateVo::getMaxScore)
                            .filter(x -> x != null) // 过滤掉 null 值
                            .mapToDouble(Double::doubleValue)
                            .sum();

                         stageSumScore = Optional.ofNullable(stageTotalScore)
                            .map(BigDecimal::valueOf)
                            .orElse(BigDecimal.ZERO); // null时返回0
                        //阶段总分
                        stage.setScore(stageSumScore);
                    });
                    evDomainTemplateVo.setStageList(stageList);
                }
                evDomainTemplateVoList.add(evDomainTemplateVo);
            }
        });
        evResultAddDto.setEvDomainTemplateVoList(evDomainTemplateVoList);
        //重新计算总分
        BigDecimal totalScore = evDomainTemplateVoList.stream().map(EvDomainTemplateVo::getTotalScore)
            .filter(x -> x != null) // 过滤掉 null 值
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        evResultAddDto.setTotalScore(totalScore);
        return evResultAddDto;
    }
    /**
     * 修改报告补充信息，post，json 格式
     * @param complementDto 补充信息参数
     * @return 报告id
     */
    @Override
    public Integer complementVbReport(EvResultVbComplementDto complementDto) {
        EvResultVbmapp evResultVb = BeanUtil.copyProperties (complementDto, EvResultVbmapp.class);
        //进行更新
        evResultVb.setUpdateTime(new Date());
        evResultVb.setUpdateUser(SecurityUtil.getNickName());
        evResultVbMapper.updateByPrimaryKeySelective(evResultVb);
        return evResultVb.getId();
    }

    /**
     * 修改报告内容 分情况调用相关的6个接口
     * @param reportDto
     * @return
     */
    @Override
    public String updateVbReport(EvResultVbUpdateDto reportDto) {
        String result = "";
        EvResultAddDto resultAddDto = new EvResultAddDto();
        VbReportUpdateDto vbReportUpdateDto = new VbReportUpdateDto();
        EvResultVbmapp resultVbmapp = evResultVbMapper.selectByPrimaryKey(reportDto.getReportId());
        vbReportUpdateDto.setId(reportDto.getReportId());
        Integer vbType = 1;
        if (reportDto.getColumnId() == 5) {
            vbType = 2;
        } else if (reportDto.getColumnId() == 7) {
            vbType = 3;
        }
        Integer resultId = 0;
        if (ObjectUtil.isNotNull(resultVbmapp)) {
            resultId = resultVbmapp.getResultId();
        }
        //1 ：更新领域的未来三月建议 ； 2：更新总建议
        Integer upType = reportDto.getUpType();
        //调AI
        if (1 == reportDto.getOpType()) {
            //构造参数，构造相应领域的参数，如果是结尾的建议，需要全量参数
            if (upType == 2) {
                //生成全量的选题参数
                resultAddDto = this.getAIParams(resultId, vbType, null);
            } else {
                //生成单独领域或项目的选题
                resultAddDto = this.getAIParams(resultId, vbType, reportDto.getDomainId());
            }
            resultAddDto.setId(reportDto.getReportId());
            resultAddDto.setResultId(resultVbmapp.getResultId());
        } else {
            //不调AI
            resultAddDto = null;
        }
        if (upType == 1) {
            if (vbType == 1) {
                vbReportUpdateDto.setDomainId(reportDto.getDomainId());
                vbReportUpdateDto.setTargetContent(reportDto.getTargetContent());
            } else {
                vbReportUpdateDto.setProjectId(reportDto.getDomainId());
                vbReportUpdateDto.setTargetContent(reportDto.getTargetContent());
            }
        } else {
            //修改的总结建议
            if (vbType == 1) {
                vbReportUpdateDto.setSuggest(reportDto.getSuggest());
            } else {
                vbReportUpdateDto.setProjectSuggest(reportDto.getSuggest());
            }
        }
        // 里程碑修改各个目标
        if ((vbType == 1) && (upType == 1)) {
            result = assessAiService.createSmTarget(resultAddDto, vbReportUpdateDto);
        }
        // 里程碑修改总建议
        if ((vbType == 1) && (upType == 2)) {
            result = assessAiService.createSmPropose(resultAddDto, vbReportUpdateDto);
        }
        // 障碍修改各个目标
        if ((vbType == 2) && (upType == 1)) {
            result = assessAiService.createObstacleProjectSuggest(resultAddDto, vbReportUpdateDto);
        }
        // 障碍修改总建议
        if ((vbType == 2) && (upType == 2)) {
            result = assessAiService.createObstacleSuggest(resultAddDto, vbReportUpdateDto);
        }
        // 转衔修改各个目标
        if ((vbType == 3) && (upType == 1)) {
            result = assessAiService.createTransitionProjectSuggest(resultAddDto, vbReportUpdateDto);
        }
        // 转衔修改总建议
        if ((vbType == 3) && (upType == 2)) {
            result = assessAiService.createTransitionSuggest(resultAddDto, vbReportUpdateDto);
        }
        //如果AI生成为null,前端需要""
        if (ObjectUtil.isNull(result)){
            result = "";
        }
        return result;
    }

    /**
     * 修改报告中总览里程碑指定内容 (post，json 格式)
     * @param reportDto 总览里程碑指定内容
     * @return 报告id
     */
    @Override
    public String updateSmAssessResult(VbReportUpdateSmDto reportDto) {
        return assessAiService.updateSmAssessResult(reportDto.getReportId(),reportDto.getId(),reportDto.getLevel());
    }

    /**
     * 修改报告中总览里转衔指定内容 (post，json 格式)
     * @param reportDto 总览里程碑指定内容
     * @return 报告id
     */
    @Override
    public String updateTransitionAssessResult(VbReportUpdateTransDto reportDto) {
        return assessAiService.updateTransitionAssessResult(reportDto.getReportId(),reportDto.getType(),reportDto.getLevel());
    }

    /**
     * 导出VBMAPP报告
     *
     * @param reportId 报告ID
     * @param type 0 下载报告 1 合并报告
     * @return 报告链接
     */
    @Override
    public Object exportVBMAPPReport (Integer reportId,Integer type) {
        EvResultVbmapp evResultVbmapp = evResultVbMapper.selectByPrimaryKey(reportId);
        //获取需要展示的部分
        List<VbReportColumnVo> vbReportColumnVoList = evResultVbmappService.showReportColumn (reportId);
        int firstType = 0;
        int secondType = 0;
        int thirdType = 0;
        for (VbReportColumnVo vbReportColumnVo : vbReportColumnVoList) {
            if (vbReportColumnVo.getColumnId ()==2){
                firstType = 1;
            }else if (vbReportColumnVo.getColumnId ()==4){
                secondType = 1;
            }else if (vbReportColumnVo.getColumnId ()==6){
                thirdType = 1;
            }
        }
        Integer resultId = evResultVbmapp.getResultId();
        //查询评估分数详情
        Map<String, Object> reportData = new HashMap<>();
        EvResult evResult = evResultMapper.selectByPrimaryKey(resultId);
        YcxChildren ycxChildren = ycxChildrenMapper.selectByPrimaryKey(evResultVbmapp.getChildrenId());
        SysUser sysUser = sysUserMapper.selectByPrimaryKey(evResult.getTeacherId());
        //表头信息
        reportData.put("childrenName", ycxChildren.getName());
        reportData.put("gender", ycxChildren.getGender() == 1 ? "男" : "女");
        reportData.put("birthday", DateUtil.formatDate(ycxChildren.getBirthday()));
        long totalMonths = DateUtil.betweenMonth(ycxChildren.getBirthday (), new Date (), true);
        int years = (int) (totalMonths / 12);
        int months = (int) (totalMonths % 12);
        reportData.put("age", years+"岁"+months+"个月");
        reportData.put("assessDate", DateUtil.formatDate(evResult.getCreateTime()));
        reportData.put("teacherName", sysUser.getName());
        reportData.put("resultNo", evResult.getResultNo());
        //三个评估的分数
        Example evVbResultDomainExample = new Example(EvVbResultDomain.class);
        evVbResultDomainExample.createCriteria()
            .andEqualTo("resultId", resultId)
            .andEqualTo("status", 1)
            .andEqualTo("isDelete", 0);
        List<EvVbResultDomain> evVbResultDomainList = evVbResultDomainMapper.selectByExample(evVbResultDomainExample);
        //里程碑评估结果
        List<Map<String, Object>> VBMAPPResultMapList = new ArrayList<>();
        String milestoneSummary = evResultVbmapp.getMilestoneSummary();
        if (StrUtil.isNotBlank(milestoneSummary)) {
            List<SmAssessResultVo> smAssessResultVoList = JSONUtil.toList(milestoneSummary, SmAssessResultVo.class);
            Map<String, Object> VBMAPPResult = new HashMap<>();
            for (int i = 0; i < smAssessResultVoList.size(); i++) {
                SmAssessResultVo smAssessResultVo = smAssessResultVoList.get(i);
                VBMAPPResult.put(i + "score", smAssessResultVo.getTotalScore());
                VBMAPPResult.put(i + "level", smAssessResultVo.getLevel());
            }
            VBMAPPResultMapList.add(VBMAPPResult);
            reportData.put("VBMAPPResultMapList", VBMAPPResultMapList);
        } else {
            reportData.put("VBMAPPResultMapList", false);
        }
        //障碍评估结果
        List<Map<String, Object>> obstacleAssessResultMapList = new ArrayList<>();
        String obstacleSummary = evResultVbmapp.getObstacleSummary();
        if (StrUtil.isNotBlank(obstacleSummary)) {
            Map<String, Object> obstacleAssessResultMap = new HashMap<>();
            List<ObstacleAssessResultVo> obstacleAssessResultVoList = JSONUtil.toList(obstacleSummary, ObstacleAssessResultVo.class);
            for (ObstacleAssessResultVo obstacleAssessResultVo : obstacleAssessResultVoList) {
                StringBuilder obstacleAssessResultStr = new StringBuilder();
                for (String s : obstacleAssessResultVo.getTitleList()) {
                    obstacleAssessResultStr.append(s)
                        .append("\n");
                }
                obstacleAssessResultMap.put(String.valueOf(obstacleAssessResultVo.getScore()), obstacleAssessResultStr.toString());
            }
            obstacleAssessResultMapList.add(obstacleAssessResultMap);
            reportData.put("obstacleAssessResultMapList", obstacleAssessResultMapList);
        } else {
            reportData.put("obstacleAssessResultMapList", false);
        }
        //转衔评估结果
        List<Map<String, Object>> transitionAssessResultMapList = new ArrayList<>();
        String transferSummary = evResultVbmapp.getTransferSummary();
        if (StrUtil.isNotBlank(transferSummary)) {
            Map<String, Object> transitionAssessResultMap = new HashMap<>();
            List<TransitionAssessResultVo> transitionAssessResultVoList = JSONUtil.toList(transferSummary, TransitionAssessResultVo.class);
            for (TransitionAssessResultVo transitionAssessResultVo : transitionAssessResultVoList) {
                transitionAssessResultMap.put(transitionAssessResultVo.getType(), transitionAssessResultVo.getLevel());
            }
            transitionAssessResultMapList.add(transitionAssessResultMap);
            reportData.put("transitionAssessResultMapList", transitionAssessResultMapList);
        } else {
            reportData.put("transitionAssessResultMapList", false);
        }
        //里程碑评估报告
        List<Map<String, Object>> VBMAPPSmMapList = new ArrayList<>();
        String milestoneReport = evResultVbmapp.getMilestoneReport();
        if (firstType == 1) {
            double smSum = evVbResultDomainList.stream().filter(evVbResultDomain -> evVbResultDomain.getDomainId() >= 8 && evVbResultDomain.getDomainId() <= 23).mapToDouble(evVbDomain -> evVbDomain.getScore().doubleValue()).sum();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean(milestoneReport, VBMAPPReportVo.class);
            Map<String, Object> VBMAPPSmMap = new HashMap<>();
            VBMAPPReportHeadVo head = vbmappReportVo.getHead();
            VBMAPPSmMap.put("head1", head.getFirst ());
            VBMAPPSmMap.put("head2", head.getSecond ());
            VBMAPPSmMap.put("head3", head.getThird ());
            VBMAPPSmMap.put ("smSum",smSum);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList();
            List<Map<String,Object>> domainMapList = new ArrayList<>();
            for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
                Map<String ,Object> domainMap = new HashMap<>();
                domainMap.put("name", vbmappAssessTargetSuggestVo.getName());
                domainMap.put("score", vbmappAssessTargetSuggestVo.getScore ());
                domainMap.put("targetName", vbmappAssessTargetSuggestVo.getTargetName ());
                domainMap.put("targetContent", vbmappAssessTargetSuggestVo.getTargetContent ());
                domainMapList.add(domainMap);
            }
            VBMAPPSmMap.put("domainList", domainMapList);
            VBMAPPSmMap.put("suggest", vbmappReportVo.getSuggest());
            VBMAPPSmMapList.add(VBMAPPSmMap);
            reportData.put("VBMAPPSmMapList", VBMAPPSmMapList);
        } else {
            reportData.put("VBMAPPSmMapList", false);
        }
        //障碍评估报告
        List<Map<String, Object>> obstacleReportMapList = new ArrayList<>();
        String obstacleReport = evResultVbmapp.getObstacleReport();
        if (secondType==1) {
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean(obstacleReport, VBMAPPReportVo.class);
            Map<String, Object> obstacleReportMap = new HashMap<>();
            VBMAPPReportHeadVo head = vbmappReportVo.getHead();
            double obstacleSum = evVbResultDomainList.stream().filter(evVbResultDomain -> evVbResultDomain.getDomainId() == 24).mapToDouble(evVbDomain -> evVbDomain.getScore().doubleValue()).sum();
            obstacleReportMap.put("head1", head.getFirst ());
            obstacleReportMap.put("head2", head.getSecond ());
            obstacleReportMap.put("head3", head.getThird ());
            obstacleReportMap.put("obstacleSum", obstacleSum);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList();
            List<Map<String,Object>> domainMapList = new ArrayList<>();
            for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : vbmappAssessTargetSuggestVoList) {
                Map<String ,Object> domainMap = new HashMap<>();
                domainMap.put("name", vbmappAssessTargetSuggestVo.getName());
                domainMap.put("score", vbmappAssessTargetSuggestVo.getScore ());
                domainMap.put ("opinion", vbmappAssessTargetSuggestVo.getOpinion());
                domainMap.put("targetName", vbmappAssessTargetSuggestVo.getTargetName ());
                domainMap.put("targetContent", vbmappAssessTargetSuggestVo.getTargetContent ());
                domainMapList.add(domainMap);
            }
            obstacleReportMap.put("domainList", domainMapList);
            obstacleReportMap.put("suggest", vbmappReportVo.getSuggest());
            obstacleReportMapList.add(obstacleReportMap);
            reportData.put("obstacleReportMapList", obstacleReportMapList);
        } else {
            reportData.put("obstacleReportMapList", false);
        }
        //转衔评估报告
        List<Map<String, Object>> transitionReportMapList = new ArrayList<>();
        String transferReport = evResultVbmapp.getTransferReport();
        if (thirdType==1) {
            Map<String, Object> transferReportMap = new HashMap<>();
            VBMAPPReportVo vbmappReportVo = JSONUtil.toBean(transferReport, VBMAPPReportVo.class);
            double transitionSum = evVbResultDomainList.stream().filter(evVbResultDomain -> evVbResultDomain.getDomainId() == 25).mapToDouble(evVbDomain -> evVbDomain.getScore().doubleValue()).sum();
            VBMAPPReportHeadVo head = vbmappReportVo.getHead();
            transferReportMap.put("head1", head.getFirst ());
            transferReportMap.put("head2", head.getSecond ());
            transferReportMap.put("head3", head.getThird ());
            transferReportMap.put("transitionSum", transitionSum);
            List<VBMAPPAssessTargetSuggestVo> vbmappAssessTargetSuggestVoList = vbmappReportVo.getVbmappAssessTargetSuggestVoList();
            List<Map<String,Object>> one = new ArrayList<>();
            List<Map<String,Object>> two = new ArrayList<>();
            List<Map<String,Object>> three = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(vbmappAssessTargetSuggestVoList)) {
                List<VBMAPPAssessTargetSuggestVo> first = vbmappAssessTargetSuggestVoList.stream().filter(vo -> vo.getType() == 1).collect(Collectors.toList());
                List<VBMAPPAssessTargetSuggestVo> second = vbmappAssessTargetSuggestVoList.stream().filter(vo -> vo.getType() == 2).collect(Collectors.toList());
                List<VBMAPPAssessTargetSuggestVo> third = vbmappAssessTargetSuggestVoList.stream().filter(vo -> vo.getType() == 3).collect(Collectors.toList());
                for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : first) {
                    Map<String ,Object> domainMap = new HashMap<>();
                    domainMap.put("name", vbmappAssessTargetSuggestVo.getName());
                    domainMap.put("score", vbmappAssessTargetSuggestVo.getScore ());
                    domainMap.put ("opinion",vbmappAssessTargetSuggestVo.getOpinion());
                    domainMap.put("targetName", vbmappAssessTargetSuggestVo.getTargetName ());
                    domainMap.put("targetContent", vbmappAssessTargetSuggestVo.getTargetContent ());
                    one.add(domainMap);
                }
                for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : second) {
                    Map<String ,Object> domainMap = new HashMap<>();
                    domainMap.put("name", vbmappAssessTargetSuggestVo.getName());
                    domainMap.put("score", vbmappAssessTargetSuggestVo.getScore ());
                    domainMap.put ("opinion",vbmappAssessTargetSuggestVo.getOpinion());
                    domainMap.put("targetName", vbmappAssessTargetSuggestVo.getTargetName ());
                    domainMap.put("targetContent", vbmappAssessTargetSuggestVo.getTargetContent ());
                    two.add(domainMap);
                }
                for (VBMAPPAssessTargetSuggestVo vbmappAssessTargetSuggestVo : third) {
                    Map<String ,Object> domainMap = new HashMap<>();
                    domainMap.put("name", vbmappAssessTargetSuggestVo.getName());
                    domainMap.put("score", vbmappAssessTargetSuggestVo.getScore ());
                    domainMap.put ("opinion",vbmappAssessTargetSuggestVo.getOpinion());
                    domainMap.put("targetName", vbmappAssessTargetSuggestVo.getTargetName ());
                    domainMap.put("targetContent", vbmappAssessTargetSuggestVo.getTargetContent ());
                    three.add(domainMap);
                }
                transferReportMap.put("one", one);
                transferReportMap.put("two", two);
                transferReportMap.put("three", three);
            }
            transferReportMap.put("suggest", vbmappReportVo.getSuggest());
            transitionReportMapList.add(transferReportMap);
            reportData.put("transitionReportMapList", transitionReportMapList);
        } else {
            reportData.put("transitionReportMapList", false);
        }
        //补充内容
        reportData.put("assessQuestion", evResultVbmapp.getAssessQuestion());
        reportData.put("assessResult", evResultVbmapp.getAssessResult());
        reportData.put("advance", evResultVbmapp.getAdvance());
        reportData.put("referralSuggestion", evResultVbmapp.getReferralSuggestion());
        //读取模版配置
        LoopRowTableRenderPolicy policy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder()
            .bind("obstacleReportMapList.domainList", policy)
            .bind("transitionReportMapList.one", policy)
            .bind("transitionReportMapList.two", policy)
            .bind("transitionReportMapList.three", policy)
            .build();
        XWPFTemplate childrenCompile = XWPFTemplate.compile(Objects.requireNonNull(ReportServiceImpl.class.getClassLoader().getResourceAsStream("template/VBMAPPTemplate.docx")), config);
        XWPFTemplate rendered = childrenCompile.render(reportData);
        //里程碑评估计分表
        EvResultAddDto evResultAddDto = this.getAIParams(resultId, 1, null);
        List<EvDomainTemplateVo> evDomainTemplateVoList = evResultAddDto.getEvDomainTemplateVoList();
        // 模拟你的题目数据（16列，每列5题）
        Map<String, List<Double>> firstStageMap = new LinkedHashMap<> ();
        Map<String, List<Double>> secondStageMap = new LinkedHashMap<>();
        Map<String, List<Double>> thirdStageMap = new LinkedHashMap<>();
        for (EvDomainTemplateVo evDomainTemplateVo : evDomainTemplateVoList) {
            String domainName = evDomainTemplateVo.getDomainName();
            List<Double> firstList = new ArrayList<>();
            List<Double> secondList = new ArrayList<>();
            List<Double> thirdList = new ArrayList<>();
            for (EvDomainProjectTemplateVo evDomainProjectTemplateVo : evDomainTemplateVo.getEvDomainProjectTemplateVoList()) {
                if (1 <= evDomainProjectTemplateVo.getNo() && evDomainProjectTemplateVo.getNo() <= 5) {
                    firstList.add(evDomainProjectTemplateVo.getScore());
                } else if (6 <= evDomainProjectTemplateVo.getNo() && evDomainProjectTemplateVo.getNo() <= 10) {
                    secondList.add(evDomainProjectTemplateVo.getScore());
                } else {
                    thirdList.add(evDomainProjectTemplateVo.getScore());
                }
            }
            if (CollectionUtil.isNotEmpty(firstList)) {
                firstStageMap.put(domainName, firstList);
            }
            if (CollectionUtil.isNotEmpty(secondList)) {
                secondStageMap.put(domainName, secondList);
            }
            if (CollectionUtil.isNotEmpty(thirdList)) {
                thirdStageMap.put(domainName, thirdList);
            }
        }
        NiceXWPFDocument xwpfDocument = rendered.getXWPFDocument();
        List<XWPFTable> tableList = xwpfDocument.getTables();
        if (firstType==1) {
            //第一阶段表格
            XWPFTable firstStage = tableList.get(3);
            smRender(firstStageMap, firstStage);
            //第二阶段表格
            XWPFTable secondStage = tableList.get(2);
            smRender(secondStageMap, secondStage);
            //第三阶段表格
            XWPFTable thirdStage = tableList.get(1);
            smRender(thirdStageMap, thirdStage);
        }
        //障碍评估计分表
        if (secondType==1) {
            EvResultAddDto aiParams = this.getAIParams(resultId, 2, null);
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVoList = aiParams.getEvDomainTemplateVoList().get(0).getEvDomainProjectTemplateVoList();
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosFirst = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 1 && vo.getNo() <= 6).collect(Collectors.toList());
            Map<String, Double> firstScoreMap = evDomainProjectTemplateVosFirst.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosSecond = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 7 && vo.getNo() <= 12).collect(Collectors.toList());
            Map<String, Double> secondScoreMap = evDomainProjectTemplateVosSecond.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosThird = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 13 && vo.getNo() <= 18).collect(Collectors.toList());
            Map<String, Double> thirdScoreMap = evDomainProjectTemplateVosThird.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosForth = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 19 && vo.getNo() <= 24).collect(Collectors.toList());
            Map<String, Double> forthScoreMap = evDomainProjectTemplateVosForth.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            if (firstType==1) {
                XWPFTable forth = tableList.get(4);
                XWPFTableRow forthRow = forth.getRow(4);
                Map<String, Integer> forthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < forthRow.getTableCells().size(); col++) {
                    String title = forthRow.getCell(col).getText().trim();
                    forthProblemColumnMap.put(title, col);
                }
                obstacleRender(firstScoreMap, forthProblemColumnMap, forth);
                XWPFTable fifth = tableList.get(5);
                XWPFTableRow fifthRow = fifth.getRow(4);
                Map<String, Integer> fifthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < fifthRow.getTableCells().size(); col++) {
                    String title = fifthRow.getCell(col).getText().trim();
                    fifthProblemColumnMap.put(title, col);
                }
                obstacleRender(secondScoreMap, fifthProblemColumnMap, fifth);
                XWPFTable sixth = tableList.get(6);
                XWPFTableRow sixthRow = sixth.getRow(4);
                Map<String, Integer> sixthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < sixthRow.getTableCells().size(); col++) {
                    String title = sixthRow.getCell(col).getText().trim();
                    sixthProblemColumnMap.put(title, col);
                }
                obstacleRender(thirdScoreMap, sixthProblemColumnMap, sixth);
                XWPFTable seventh = tableList.get(7);
                XWPFTableRow seventhRow = seventh.getRow(4);
                Map<String, Integer> seventhProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < seventhRow.getTableCells().size(); col++) {
                    String title = seventhRow.getCell(col).getText().trim();
                    seventhProblemColumnMap.put(title, col);
                }
                obstacleRender(forthScoreMap, seventhProblemColumnMap, seventh);
            }
        }
        //转衔评估计分表
        if (thirdType ==1) {
            EvResultAddDto aiParams = this.getAIParams(resultId, 3, null);
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVoList = aiParams.getEvDomainTemplateVoList().get(0).getEvDomainProjectTemplateVoList();
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosFirst = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 1 && vo.getNo() <= 6).collect(Collectors.toList());
            Map<String, Double> firstScoreMap = evDomainProjectTemplateVosFirst.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosSecond = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 7 && vo.getNo() <= 12).collect(Collectors.toList());
            Map<String, Double> secondScoreMap = evDomainProjectTemplateVosSecond.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            List<EvDomainProjectTemplateVo> evDomainProjectTemplateVosThird = evDomainProjectTemplateVoList.stream().filter(vo -> vo.getNo() >= 13 && vo.getNo() <= 18).collect(Collectors.toList());
            Map<String, Double> thirdScoreMap = evDomainProjectTemplateVosThird.stream().collect(Collectors.toMap(EvDomainProjectTemplateVo::getShortTitle, EvDomainProjectTemplateVo::getScore));
            if (firstType != 1 && secondType == 1) {
                XWPFTable fifth = tableList.get(5);
                XWPFTableRow fifthRow = fifth.getRow(5);
                Map<String, Integer> fifthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < fifthRow.getTableCells().size(); col++) {
                    String title = fifthRow.getCell(col).getText().trim();
                    fifthProblemColumnMap.put(title, col);
                }
                transitionRender(firstScoreMap, fifthProblemColumnMap, fifth);
                XWPFTable sixth = tableList.get(6);
                XWPFTableRow sixthRow = sixth.getRow(5);
                Map<String, Integer> sixthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < sixthRow.getTableCells().size(); col++) {
                    String title = sixthRow.getCell(col).getText().trim();
                    sixthProblemColumnMap.put(title, col);
                }
                transitionRender(secondScoreMap, sixthProblemColumnMap, sixth);
                XWPFTable seventh = tableList.get(7);
                XWPFTableRow seventhRow = seventh.getRow(5);
                Map<String, Integer> seventhProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < seventhRow.getTableCells().size(); col++) {
                    String title = seventhRow.getCell(col).getText().trim();
                    seventhProblemColumnMap.put(title, col);
                }
                transitionRender(thirdScoreMap, seventhProblemColumnMap, seventh);
            } else if (firstType != 1 && secondType !=1) {
                XWPFTable first = tableList.get(1);
                XWPFTableRow firstRow = first.getRow(5);
                Map<String, Integer> firstProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < firstRow.getTableCells().size(); col++) {
                    String title = firstRow.getCell(col).getText().trim();
                    firstProblemColumnMap.put(title, col);
                }
                transitionRender(firstScoreMap, firstProblemColumnMap, first);
                XWPFTable second = tableList.get(2);
                XWPFTableRow secondRow = second.getRow(5);
                Map<String, Integer> secondProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < secondRow.getTableCells().size(); col++) {
                    String title = secondRow.getCell(col).getText().trim();
                    secondProblemColumnMap.put(title, col);
                }
                transitionRender(secondScoreMap, secondProblemColumnMap, second);
                XWPFTable third = tableList.get(3);
                XWPFTableRow thirdRow = third.getRow(5);
                Map<String, Integer> thirdProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < thirdRow.getTableCells().size(); col++) {
                    String title = thirdRow.getCell(col).getText().trim();
                    thirdProblemColumnMap.put(title, col);
                }
                transitionRender(thirdScoreMap, thirdProblemColumnMap, third);
            } else if (firstType ==1 && secondType==1) {
                XWPFTable eighth = tableList.get(8);
                XWPFTableRow eighthRow = eighth.getRow(5);
                Map<String, Integer> eighthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < eighthRow.getTableCells().size(); col++) {
                    String title = eighthRow.getCell(col).getText().trim();
                    eighthProblemColumnMap.put(title, col);
                }
                transitionRender(firstScoreMap, eighthProblemColumnMap, eighth);
                XWPFTable ninth = tableList.get(9);
                XWPFTableRow ninthRow = ninth.getRow(5);
                Map<String, Integer> ninthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < ninthRow.getTableCells().size(); col++) {
                    String title = ninthRow.getCell(col).getText().trim();
                    ninthProblemColumnMap.put(title, col);
                }
                transitionRender(secondScoreMap, ninthProblemColumnMap, ninth);
                XWPFTable tenth = tableList.get(10);
                XWPFTableRow tenthRow = tenth.getRow(5);
                Map<String, Integer> tenthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < tenthRow.getTableCells().size(); col++) {
                    String title = tenthRow.getCell(col).getText().trim();
                    tenthProblemColumnMap.put(title, col);
                }
                transitionRender(thirdScoreMap, tenthProblemColumnMap, tenth);
            } else {
                XWPFTable forth = tableList.get(4);
                XWPFTableRow forthRow = forth.getRow(5);
                Map<String, Integer> forthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < forthRow.getTableCells().size(); col++) {
                    String title = forthRow.getCell(col).getText().trim();
                    forthProblemColumnMap.put(title, col);
                }
                transitionRender(firstScoreMap, forthProblemColumnMap, forth);
                XWPFTable fifth = tableList.get(5);
                XWPFTableRow fifthRow = fifth.getRow(5);
                Map<String, Integer> fifthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < fifthRow.getTableCells().size(); col++) {
                    String title = fifthRow.getCell(col).getText().trim();
                    fifthProblemColumnMap.put(title, col);
                }
                transitionRender(secondScoreMap, fifthProblemColumnMap, fifth);
                XWPFTable sixth = tableList.get(6);
                XWPFTableRow sixthRow = sixth.getRow(5);
                Map<String, Integer> sixthProblemColumnMap = new HashMap<>(); // 问题名称 → 列索引
                for (int col = 1; col < sixthRow.getTableCells().size(); col++) {
                    String title = sixthRow.getCell(col).getText().trim();
                    sixthProblemColumnMap.put(title, col);
                }
                transitionRender(thirdScoreMap, sixthProblemColumnMap, sixth);
            }
        }
        if (1==type){
            return rendered;
        }
        //上传七牛
        try {
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            // 写入内存流
            rendered.write(out);
            // 3. 关闭
            rendered.close();
            byte[] bytes = out.toByteArray();
            String url = new QiNiuFileUpLoadUtil(fileConfig).uploadByte(bytes, "/qscz/report/" + DateUtil.format (new Date(),"yyyy-MM-dd") + "/" + System.currentTimeMillis() + "/VBMAPP评估报告_" + ycxChildren.getName() + "_" + DateUtil.format (new Date(),"yyyy-MM-dd") + ".docx");
            evResult.setReportUrl (url);
            evResultMapper.updateByPrimaryKeySelective(evResult);
            return url;
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private static void obstacleRender (Map<String, Double> firstScoreMap, Map<String, Integer> forthProblemColumnMap, XWPFTable table) {
        // 遍历每个问题，根据得分渲染颜色
        for (Map.Entry<String, Double> entry : firstScoreMap.entrySet()) {
            String problem = entry.getKey();
            Double score = entry.getValue(); // 假设是 1~4 分

            Integer colIndex = forthProblemColumnMap.get(problem);
            if (colIndex == null) continue;

            // 从下往上染色（得2分染第4行、第3行）
            for (int i = 0; i < score; i++) {
                int rowIndex = 3 - i;
                if (rowIndex < 0) break;

                XWPFTableCell cell = table.getRow(rowIndex).getCell(colIndex);
                cell.setColor("E3E7FF");
            }
        }
    }

    private static void transitionRender (Map<String, Double> firstScoreMap, Map<String, Integer> forthProblemColumnMap, XWPFTable table) {
        // 遍历每个问题，根据得分渲染颜色
        for (Map.Entry<String, Double> entry : firstScoreMap.entrySet()) {
            String problem = entry.getKey();
            Double score = entry.getValue(); // 假设是 1~4 分

            Integer colIndex = forthProblemColumnMap.get(problem);
            if (colIndex == null) continue;

            // 从下往上染色（得2分染第4行、第3行）
            for (int i = 0; i < score; i++) {
                int rowIndex = 4 - i;
                if (rowIndex < 0) break;

                XWPFTableCell cell = table.getRow(rowIndex).getCell(colIndex);
                cell.setColor("E3E7FF");
            }
        }
    }

    private static void smRender (Map<String, List<Double>> firstStageMap, XWPFTable firstStage) {
        int rowCount = 11; // 总共11行
        int bottomRowIndex = rowCount - 2; // 倒数第二行是染色开始点（第10行，索引9）
        int topRowIndex = 0; // 最顶部行（索引0）
        int colIndex = 0; // 当前领域列索引
        for (Map.Entry<String, List<Double>> stringListEntry : firstStageMap.entrySet ()) {
            List<Double> scores = stringListEntry.getValue(); // 5道题的得分
            int currentRow = bottomRowIndex;

            for (Double score : scores) {
                int blocks = (int)(score * 2); // 0分→0行，0.5分→1行，1分→2行
                for (int i = 0; i < blocks; i++) {
                    if (currentRow < topRowIndex) break;
                    XWPFTableCell cell = firstStage.getRow(currentRow).getCell(colIndex);
                    currentRow--;
                    cell.setColor("E3E7FF"); // 你可以改颜色
                }
                currentRow -= (2 - blocks); // 每题占2格，无论是否染色都跳过
            }
            colIndex++;
        }
    }

    /**
     * 获取补充信息
     * @param reportId 报告ID
     * @return 补充信息
     */
    @Override
    public EvResultVbmapp getSupplementInfo (Integer reportId) {
        Example example = new Example (EvResultVbmapp.class);
        example.selectProperties ("assessQuestion","assessResult","advance","referralSuggestion");
        example.createCriteria ().andEqualTo ("id",reportId).andEqualTo ("status",1).andEqualTo ("isDelete",0);
        EvResultVbmapp evResultVbmapp = evResultVbMapper.selectOneByExample (example);
        return evResultVbmapp;
    }

    /**
     * 创建VbMAPP的IEP  (post 方式 )
     * @param reportId 报告ID
     * @return IEP生成的参数，并放入Redis
     */
    @Override
    public Result<?> createVbIEP(Integer reportId) {
        EvResultVbmapp resultVbmapp =  evResultVbMapper.selectByPrimaryKey(reportId);
        EvResultAddDto evaResultAddDto = this.getAIParams(resultVbmapp.getResultId(),0,null);
        //放入redis 前端使用
        redisService.set("ev:" + resultVbmapp.getChildrenId().toString(), evaResultAddDto);
        return ResultUtil.success(resultVbmapp.getResultId());
    }

    /**
     * 自动计算转衔中的前5项分数
     */
    @Override
    public void autoCalcVbTransferScore(EvResultVbScoreDto scoreDto) {
        //查询总分
        VbScoreTotalVo scoreTotalVo = evResultVbMapper.getVbTransferScore(scoreDto.getResultId());
        //根据相应的分数进行对照给分
        //判断点击的是什么领域类型，决定刷新那些数据
        EvDomain evDomain = evDomainMapper.selectByPrimaryKey(scoreDto.getDomainId());
        //如果是里程碑刷新总数
        if (1==evDomain.getVbType()){
            double milestoneTotalScore = scoreTotalVo.getMilestoneTotalScore();
            //404	VB-MAPP里程碑评估得分
            /*
            1.里程碑评估得分为 0~25 分
            2.里程碑评估得分为 26~50 分
            3.里程碑评估得分为 51~100 分
            4.里程碑评估得分为 101~135 分
            5.里程碑评估得分为 136~170分
             */
            BigDecimal score = BigDecimal.ZERO ;
            if (milestoneTotalScore<=25){
                score = new BigDecimal(1);
            } else if (milestoneTotalScore<=50){
                score = new BigDecimal(2);
            } else if (milestoneTotalScore<=100){
                score = new BigDecimal(3);
            } else if (milestoneTotalScore<=135){
                score = new BigDecimal(4);
            } else if (milestoneTotalScore<=170){
                score = new BigDecimal(5);
            }
            //调用
            EvResultVbScoreDto vbScoreDto = new EvResultVbScoreDto();
            vbScoreDto.setResultId(scoreDto.getResultId());
            vbScoreDto.setDomainId(25);
            vbScoreDto.setDomainName("转衔评估");
            vbScoreDto.setDomainProjectId(404);
            vbScoreDto.setNo(1);
            vbScoreDto.setScore(score);
            //保存
            recordVbScore (vbScoreDto);
        }
        //如果是障碍评估总分刷新
        if (2==evDomain.getVbType()) {
            double obstacleTotalScore = scoreTotalVo.getObstacleTotalScore();
            //405	VB-MAPP障碍评估总分
            /*
            1.障碍评估得分为 56~96 分
            2.障碍评估得分为 31~55 分
            3.障碍评估得分为 21~30 分
            4.障碍评估得分为 11~20 分
            5.障碍评估得分为 0~10 分
             */
            BigDecimal score = BigDecimal.ZERO;
            if (obstacleTotalScore <= 10) {
                score = new BigDecimal(5);
            } else if (obstacleTotalScore <= 20) {
                score = new BigDecimal(4);
            } else if (obstacleTotalScore <= 30) {
                score = new BigDecimal(3);
            } else if (obstacleTotalScore <= 55) {
                score = new BigDecimal(2);
            } else if (obstacleTotalScore <= 96) {
                score = new BigDecimal(1);
            }
            //调用
            EvResultVbScoreDto vbScoreDto = new EvResultVbScoreDto();
            vbScoreDto.setResultId(scoreDto.getResultId());
            vbScoreDto.setDomainId(25);
            vbScoreDto.setDomainName("转衔评估");
            vbScoreDto.setDomainProjectId(405);
            vbScoreDto.setNo(2);
            vbScoreDto.setScore(score);
            //保存
            recordVbScore(vbScoreDto);
        }
        //VB-MAPP障碍评估中不良行为和教学控制的得分
        //如果是里程碑，并且操作的domain_project_id in (380,381)
        if (2==evDomain.getVbType() && (scoreDto.getDomainProjectId() ==380 || scoreDto.getDomainProjectId()==381)){
            double obstacleNegativeScore = scoreTotalVo.getObstacleNegativeScore();
            //406	VB-MAPP障碍评估中不良行为和教学控制的得分
            /*
            1.在障碍评估中负面行为和教育控制的总分为6~8分
            2.在障碍评估中负面行为和教育控制的总分为5分
            3.在障碍评估中负面行为和教育控制的总分为3分或4分
            4.在障碍评估中负面行为和教育控制的总分为2分
            5.儿童没有行为问题，在障碍评估中的得分为0分或1分
             */
            BigDecimal score = BigDecimal.ZERO ;
            if (obstacleNegativeScore<=1){
                score = new BigDecimal(5);
            } else if (obstacleNegativeScore<=2){
                score = new BigDecimal(4);
            } else if (obstacleNegativeScore<=4){
                score = new BigDecimal(3);
            } else if (obstacleNegativeScore<=5){
                score = new BigDecimal(2);
            } else if (obstacleNegativeScore<=8){
                score = new BigDecimal(1);
            }
            //调用
            EvResultVbScoreDto vbScoreDto = new EvResultVbScoreDto();
            vbScoreDto.setResultId(scoreDto.getResultId());
            vbScoreDto.setDomainId(25);
            vbScoreDto.setDomainName("转衔评估");
            vbScoreDto.setDomainProjectId(406);
            vbScoreDto.setNo(3);
            vbScoreDto.setScore(score);
            //保存
            recordVbScore (vbScoreDto);
        }
        //VB-MAPP里程碑评估中教室常规和集体技能的得分
        //如果是里程碑，并且操作的domain_id =19
        if (1==evDomain.getVbType() && scoreDto.getDomainId() == 19){
            double milestoneClassScore = scoreTotalVo.getMilestoneClassScore();
            //407	VB-MAPP里程碑评估中教室常规和集体技能的得分
            /*
            1.在里程碑评估中教室常规和集体技能的得分为2分
            2.在里程碑评估中教室常规和集体技能的得分为3~4分
            3.在里程碑评估中教室常规和集体技能的得分为5~7分
            4.在里程碑评估中教室常规和集体技能的得分为8~9分
            5.在里程碑评估中教室常规和集体技能的得分为10分
             */
            BigDecimal score = BigDecimal.ZERO ;
            if (milestoneClassScore<=1){
                score = new BigDecimal(0);
            } else if (milestoneClassScore==2){
                score = new BigDecimal(1);
            } else if (milestoneClassScore<=4){
                score = new BigDecimal(2);
            } else if (milestoneClassScore<=7){
                score = new BigDecimal(3);
            } else if (milestoneClassScore<=9){
                score = new BigDecimal(4);
            } else if (milestoneClassScore<=10){
                score = new BigDecimal(5);
            }
            //调用
            EvResultVbScoreDto vbScoreDto = new EvResultVbScoreDto();
            vbScoreDto.setResultId(scoreDto.getResultId());
            vbScoreDto.setDomainId(25);
            vbScoreDto.setDomainName("转衔评估");
            vbScoreDto.setDomainProjectId(407);
            vbScoreDto.setNo(4);
            vbScoreDto.setScore(score);
            //保存
            recordVbScore (vbScoreDto);
        }

        //VB-MAPP里程碑评估中社会行为和社会游戏的得分
        //如果是里程碑，并且操作的domain_id =12
        if (1==evDomain.getVbType() && scoreDto.getDomainId() == 12){
            double milestoneGameScore = scoreTotalVo.getMilestoneGameScore();
            //408	VB-MAPP里程碑评估中社会行为和社会游戏的得分
            /*
            1.在里程碑评估中社会行为和社会游戏的得分为2~3 分
            2.在里程碑评估中社会行为和社会游戏的得分为4~5 分
            3.在里程碑评估中社会行为和社会游戏的得分为6~9分
            4.在里程碑评估中社会行为和社会游戏的得分为 10~12 分
            5.在里程碑评估中社会行为和社会游戏的得分为 13~15 分
             */
            BigDecimal score = BigDecimal.ZERO ;
            if (milestoneGameScore<2){
                score = new BigDecimal(0);
            } else if (milestoneGameScore<3){
                score = new BigDecimal(1);
            } else if (milestoneGameScore<=5){
                score = new BigDecimal(2);
            } else if (milestoneGameScore<=9){
                score = new BigDecimal(3);
            } else if (milestoneGameScore<=12){
                score = new BigDecimal(4);
            } else if (milestoneGameScore<=15){
                score = new BigDecimal(5);
            }
            //调用
            EvResultVbScoreDto vbScoreDto = new EvResultVbScoreDto();
            vbScoreDto.setResultId(scoreDto.getResultId());
            vbScoreDto.setDomainId(25);
            vbScoreDto.setDomainName("转衔评估");
            vbScoreDto.setDomainProjectId(408);
            vbScoreDto.setNo(5);
            vbScoreDto.setScore(score);
            //保存
            recordVbScore (vbScoreDto);
        }
    }

    /**
     * 重新生成下vbmapp 的报告内容
     */
    @Override
    public void newCreateVbReport() {
        List<Integer> resultIds = new ArrayList<>();
        resultIds.add(96);
        resultIds.add(101);
        resultIds.add(104);
        resultIds.add(125);
        resultIds.add(182);
        resultIds.add(190);
        resultIds.add(239);
        resultIds.add(241);
        resultIds.add(243);
        resultIds.add(250);
        resultIds.add(401);
        resultIds.add(430);
        resultIds.add(444);
        resultIds.forEach(item -> {
            EvResult evResult = evResultMapper.selectByPrimaryKey(item);
            //查询相应的vbmapp报告是否存在，不存在插入，否则更新
            Example example = new Example(EvResultVbmapp.class);
            example.createCriteria().andEqualTo("resultId", item);
            List<EvResultVbmapp> vbmappList = evResultVbMapper.selectByExample(example);
            EvResultVbmapp evResultVb;
            evResultVb = vbmappList.get(0);
            try {
                //分3步生成空的内容
                EvResultAddDto evResultAddDto = this.getAIParams(item, 1, null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPAssessResultHome(evResultAddDto);
                assessAiService.createVBMAPPResult(evResultAddDto);
                //AI生成VBMAPP障碍全部内容
                evResultAddDto = this.getAIParams(item, 2, null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPAssessResultHome(evResultAddDto);
                //没有设置固定文案的障碍，不再报表中体现，传入参数2，给AI时，把相应的领域过滤掉
                evResultAddDto = this.getAIParams(item, 2, null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPObstacleResult(evResultAddDto);
                //AI生成VBMAPP转衔全部内容
                evResultAddDto = this.getAIParams(item, 3, null);
                evResultAddDto.setId(evResultVb.getId());
                assessAiService.createVBMAPPAssessResultHome(evResultAddDto);
                assessAiService.createVBMAPPTransitionResult(evResultAddDto);
            } catch (ExecutionException e) {
                e.printStackTrace();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            evResultVb.setUpdateTime(new Date());
            evResultVb.setUpdateUser(SecurityUtil.getNickName());
            evResultVbMapper.updateByPrimaryKeySelective(evResultVb);

        });
    }

}
