package com.bmh.project.ev.service;

import com.bmh.common.base.BaseQuery;
import com.bmh.common.base.BaseService;
import com.bmh.common.base.Result;
import com.bmh.project.ev.dto.*;
import com.bmh.project.ev.model.EvResult;
import com.bmh.project.ev.model.EvResultVbmapp;
import com.bmh.project.ev.vo.ResultPageListVo;
import com.github.pagehelper.PageInfo;

/**
 * ev_result表对应的Service接口
 *
 * <AUTHOR>
 * @date 2025年05月12日 15:57:45
 */
public interface EvResultService extends BaseService<EvResult> {
    /**
     * 创建评估
     * @param evaResultAddDto 评估参数
     * @return 评估id 评估模板
     */
    Result<?> addEvResult(EvResultAddDto evaResultAddDto);

    /**
     * 分页获得评估记录
     * @param query 分页数据
     * @param childrenId 儿童id
     * @return IEP列表
     */
    PageInfo<ResultPageListVo> pageList(BaseQuery query, Integer childrenId);

    /**
     * 第一步先创建一个 status =0 的评估记录
     * @param resultCreateDto
     * @return
     */
    Integer createResult(EvResultCreateDto resultCreateDto);

    /**
     * 记录IEP评估测评信息  (第2步 ： 如果是vbmapp ，录入测评信息，如果以前存在结果 回显相关录入的结果)
     * @param resultId 评估Id
     * @return 评估id 评估模板
     */
    Result<?> recordVbResult(Integer resultId);

    /**
     * 选择项保存 (第3步 ： 如果是vbmapp ，每次保存或修改选题结果)
     * @param scoreDto
     * @return
     */
    Integer recordVbScore(EvResultVbScoreDto scoreDto);

    /**
     * 录入评估测评结果后提交AI (第4步 ： 除障碍外，未选项目给0分，通过result ->status = 1 )
     * @param resultDto 评估参数
     * @return 评估id 评估模板
     */
    Result<?> addOrUpdateVbResult(EvResultVbDto resultDto);

    /**
     * 创建Vb评估报告
     * @param resultId 评估id参数
     * @return 报告id
     */
    Integer createVbReport(Integer resultId);

    /**
     * 导出VBMAPP报告
     *
     * @param reportId 报告ID
     * @param type 0 下载报告 1 合并报告
     * @return 报告链接
     */
    Object exportVBMAPPReport (Integer reportId,Integer type);

    /**
     * 修改报告补充信息，post，json 格式
     * @param complementDto 补充信息参数
     * @return 报告id
     */
    Integer complementVbReport(EvResultVbComplementDto complementDto);

    /**
     * 修改报告内容
     * @param reportDto
     * @return
     */
    String updateVbReport(EvResultVbUpdateDto reportDto);

    /**
     * 修改报告中总览里程碑指定内容 (post，json 格式)
     * @param reportDto 总览里程碑指定内容
     * @return 报告id
     */
    String updateSmAssessResult(VbReportUpdateSmDto reportDto);

    /**
     * 修改报告中总览里转衔指定内容 (post，json 格式)
     * @param reportDto 总览里程碑指定内容
     * @return 报告id
     */
    String updateTransitionAssessResult(VbReportUpdateTransDto reportDto);

    /**
     * 获取补充信息
     * @param reportId 报告ID
     * @return 补充信息
     */
    EvResultVbmapp getSupplementInfo (Integer reportId);

    /**
     * 创建VbMAPP的IEP  (post 方式 )
     * @param reportId 报告ID
     * @return IEP生成的参数，并放入Redis
     */
    Result<?>  createVbIEP(Integer reportId);

    /**
     * 自动计算vbmapp 中转衔前5项的分数
     */
    void  autoCalcVbTransferScore(EvResultVbScoreDto scoreDto);

    /**
     * 重新生成vbmapp代码
     */
    void newCreateVbReport();

}
