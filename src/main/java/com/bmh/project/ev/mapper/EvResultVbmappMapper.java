package com.bmh.project.ev.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.ev.model.EvResultVbmapp;
import com.bmh.project.ev.vo.ResultPageListVo;
import com.bmh.project.report.vo.ReportLMTTitleVo;
import com.bmh.project.ev.vo.VbScoreTotalVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EvResultVbmappMapper extends BaseMapper<EvResultVbmapp> {

    /**
     * 通过评估获得vbmapp的报告信息
     * @param resultId
     * @return
     */
    EvResultVbmapp getResultVbByResutlId(@Param("resultId") Integer resultId);

    /**
     * 分页查询孩子的评估报表记录，按创建时间倒序
     * @param childrenId
     * @return
     */
    List<ResultPageListVo> selectByChildrenId(@Param("childrenId") Integer childrenId);

    /**
     * 根据儿童ID查询VBMAPP评估报告
     * @param id 儿童id
     * @return 报告列表
     */
    List<ReportLMTTitleVo> selectHistoryList (String id);

    /**
     * 获得已经计算的总分，为了给转衔的前5个自动赋值
     * @param resultId
     * @return
     */
    VbScoreTotalVo getVbTransferScore(@Param("resultId") Integer resultId);

    /**
     * 手动更新下转衔报告
     * @param evResultVbmapp
     */
     void updateTransferReport(EvResultVbmapp evResultVbmapp);

}
