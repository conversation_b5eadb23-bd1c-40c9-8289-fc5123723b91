package com.bmh.common.security.service;

import cn.hutool.core.lang.UUID;
import com.bmh.common.constant.Constants;
import com.bmh.common.redis.RedisService;
import com.bmh.common.security.model.LoginUser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {

    protected static final long MILLIS_SECOND = 1000;
    protected static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;
    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;
    @Value ("${token.header}")
    private String header;
    @Value ("${token.secret}")
    private String secret;
    // 过期时间（秒）
    @Value ("${token.expireTime}")
    private long expireTime;
    @Resource
    private RedisService redisService;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser (HttpServletRequest request) {
        // 获取请求携带的令牌
        String token = getJwtTokenFromRequest (request);
        if (StringUtils.isNotEmpty (token)) {
            Claims claims = parseJwtToken (token);
            // 解析对应的权限以及用户信息
            String redisKey = (String) claims.get (Constants.LOGIN_USER_KEY);
            return (LoginUser)redisService.get (redisKey);
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser (LoginUser loginUser) {
        if (Objects.nonNull (loginUser) && StringUtils.isNotEmpty (loginUser.getRedisKey ())) {
            refreshToken (loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser (String redisKey) {
        if (StringUtils.isNotEmpty (redisKey)) {
            redisService.remove (redisKey);
        }
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createJwtToken (LoginUser loginUser) {
        String uuid = UUID.fastUUID ().toString ();
        String redisKey = getRedisKey (loginUser.getUserId (), uuid);
        loginUser.setRedisKey (redisKey);
        refreshToken (loginUser);

        Map<String, Object> jwtClaims = new HashMap<> ();
        jwtClaims.put (Constants.LOGIN_USER_KEY, redisKey);
        jwtClaims.put (Constants.JWT_USERID, loginUser.getUserId ());
        return createJwtToken (jwtClaims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser 登录用户
     * @return 令牌
     */
    public void verifyToken (LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime ();
        long currentTime = System.currentTimeMillis ();
        if (expireTime - currentTime <= MILLIS_MINUTE_TEN) {
            refreshToken (loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshToken (LoginUser loginUser) {
        loginUser.setLoginTime (System.currentTimeMillis ());
        loginUser.setExpireTime (loginUser.getLoginTime () + expireTime);
        // 根据uuid将loginUser缓存
        redisService.set (loginUser.getRedisKey (), loginUser, expireTime);
    }

    /**
     * 从数据声明生成令牌
     *
     * @param jwtClaims 数据声明
     * @return 令牌
     */
    private String createJwtToken (Map<String, Object> jwtClaims) {
        return Jwts.builder ()
                .setClaims (jwtClaims)
                .signWith (SignatureAlgorithm.HS512, secret).compact ();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param jwtToken 令牌
     * @return 数据声明
     */
    private Claims parseJwtToken (String jwtToken) {
        return Jwts.parser ()
                .setSigningKey (secret)
                .parseClaimsJws (jwtToken)
                .getBody ();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getJwtTokenFromRequest (HttpServletRequest request) {
        String jwtToken = request.getHeader (header);
        if (StringUtils.isNotEmpty (jwtToken) && jwtToken.startsWith (Constants.TOKEN_PREFIX)) {
            jwtToken = jwtToken.replace (Constants.TOKEN_PREFIX, "");
        }
        return jwtToken;
    }

    private String getRedisKey(Integer userId, String uuid){
        return Constants.LOGIN_TOKEN_KEY +":"+userId+":"+ uuid;
    }
}
