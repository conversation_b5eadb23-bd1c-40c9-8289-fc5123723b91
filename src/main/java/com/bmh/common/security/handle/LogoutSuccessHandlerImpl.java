package com.bmh.common.security.handle;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.model.LoginUser;
import com.bmh.common.security.service.TokenService;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutSuccessHandler;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * 自定义退出处理类 返回成功
 *
 * <AUTHOR>
 */
@Configuration
public class LogoutSuccessHandlerImpl implements LogoutSuccessHandler {
    @Resource
    private TokenService tokenService;

    /**
     * 退出处理
     *
     * @return
     */
    @Override
    public void onLogoutSuccess (HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        LoginUser loginUser = tokenService.getLoginUser (request);
        if (Objects.nonNull (loginUser)) {
            String userName = loginUser.getUsername ();
            // 删除用户缓存记录
            tokenService.delLoginUser (loginUser.getRedisKey ());
        }
        ServletUtil.write (response, JSONUtil.toJsonStr (ResultUtil.success ("退出成功")), "application/json");
    }
}
