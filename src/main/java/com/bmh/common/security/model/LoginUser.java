package com.bmh.common.security.model;


import com.bmh.project.supervisor.model.SysSupervisor;
import com.bmh.project.user.model.SysUser;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.Objects;
import java.util.Set;

/**
 * 登录用户身份权限
 *
 * <AUTHOR>
 */
public class LoginUser implements UserDetails {
    private static final long serialVersionUID = 1L;

    /**
     * 用户唯一标识
     */
    @Setter
    @Getter
    private String redisKey;

    /**
     * 登陆时间
     */
    @Setter
    @Getter
    private Long loginTime;

    /**
     * 过期时间
     */
    @Setter
    @Getter
    private Long expireTime;

    /**
     * 权限列表
     */
    @Setter
    @Getter
    private Set<String> permissions;

    /**
     * 账户是否未过期,过期无法验证
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonExpired () {
        return true;
    }

    /**
     * 指定用户是否解锁,锁定的用户无法进行身份验证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isAccountNonLocked () {
        return true;
    }

    /**
     * 指示是否已过期的用户的凭据(密码),过期的凭据防止认证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isCredentialsNonExpired () {
        return true;
    }

    /**
     * 是否可用 ,禁用的用户不能身份验证
     *
     * @return
     */
    @JsonIgnore
    @Override
    public boolean isEnabled () {
        return true;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities () {
        return null;
    }
    /****************************** 自定义信息 *******************************/

    /**
     * 是否是ERP用户
     */
    @Setter
    @Getter
    private Integer isErp;

    /**
     * ERP用户ID
     */
    @Setter
    @Getter
    private Integer erpUserId;

    /**
     * ERP机构ID
     */
    @Setter
    @Getter
    private Integer erpOrgId;

    /**
     * ERP SessionID
     */
    @Setter
    @Getter
    private String erpSessionId;

    /**
     * 所属机构是否为充值机构 （0：否 1：是）
     */
    @Setter
    @Getter
    private Integer orgIsRecharge;



    /**
     * 是否开启家长端小程序 1 开启 0 关闭
     */
    @Setter
    @Getter
    private Integer isParent;

    /**
     * 是否开启个案归属 1开启 0关闭
     */
    @Setter
    @Getter
    private Integer isBelong;

    /**
     * 是否开启家长签课功能 1开启 0关闭
     */
    @Setter
    @Getter
    private Integer isCourseSign;

    /**
     * 用户ID
     */
    @Setter
    @Getter
    private Integer userId;

    @Setter
    @Getter
    private SysUser user;

    @Setter
    @Getter
    private SysSupervisor supervisor;

    @Setter
    @Getter
    private Integer userType = 1;


    public LoginUser () {
    }

    public LoginUser (SysUser user, Set<String> permissions) {
        this.userType = 1;
        this.user = user;
        this.permissions = permissions;
        this.userId = Objects.isNull (user) ? null : user.getId ();
        if(Objects.nonNull (user) && Objects.nonNull (user.getErpUserId ())){
            this.isErp = 1;
            this.erpUserId = user.getErpUserId ();
        } else {
            this.isErp = 0;
        }
    }

    public LoginUser (SysSupervisor supervisor, Set<String> permissions) {
        this.userType = 2;
        this.supervisor = supervisor;
        this.permissions = permissions;
        this.userId = Objects.isNull (supervisor) ? null : supervisor.getId ();
    }


    @JsonIgnore
    @Override
    public String getPassword () {
        return userType == 1 ? user.getPassword () : supervisor.getPassword ();
    }

    @JsonIgnore
    @Override
    public String getUsername () {
        return userType == 1 ? user.getUsername () : supervisor.getMobile ();
    }


}
